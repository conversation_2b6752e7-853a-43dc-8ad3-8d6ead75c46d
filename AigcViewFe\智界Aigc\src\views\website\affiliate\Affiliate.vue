<template>
  <WebsitePage>
    <div class="affiliate-container">
      <!-- 简洁页面标题 -->
      <div class="simple-header">
        <h1 class="simple-title">分销推广</h1>
        <p class="simple-subtitle">加入分销计划，推广智界AIGC获得丰厚佣金</p>
      </div>

      <!-- 分销内容区域 -->
      <section class="affiliate-section">
        <div class="container">
          <!-- 收益展示 -->
          <div class="earnings-card">
            <h2>分销收益</h2>
            <div class="earnings-grid">
              <div class="earning-item">
                <a-spin :spinning="loading" size="small">
                  <div class="earning-number">¥{{ formatNumber(totalEarnings) }}</div>
                  <div class="earning-label">总收益</div>
                </a-spin>
              </div>
              <div class="earning-item">
                <a-spin :spinning="loading" size="small">
                  <div class="earning-number">{{ formatNumber(referrals) }}</div>
                  <div class="earning-label">推荐用户</div>
                </a-spin>
              </div>
              <div class="earning-item">
                <a-spin :spinning="loading" size="small">
                  <div class="earning-number">{{ conversionRate }}%</div>
                  <div class="earning-label">转化率</div>
                </a-spin>
              </div>
            </div>
          </div>

          <!-- 推广工具 -->
          <div class="tools-section">
            <h2>推广工具</h2>
            <div class="tools-grid">
              <div class="tool-card">
                <div class="tool-icon">
                  <a-icon type="link" />
                </div>
                <h3>专属推广链接</h3>
                <p>获取您的专属推广链接，分享给好友即可获得佣金</p>
                <div class="link-input">
                  <a-input 
                    :value="affiliateLink || '正在生成推广链接...'" 
                    readonly 
                    :loading="loading"
                  >
                    <template slot="addonAfter">
                      <a-button 
                        type="primary" 
                        size="small" 
                        :disabled="!affiliateLink || loading"
                        @click="copyLink"
                      >
                        复制
                      </a-button>
                    </template>
                  </a-input>
                </div>
              </div>
              
              <div class="tool-card">
                <div class="tool-icon">
                  <a-icon type="qrcode" />
                </div>
                <h3>推广二维码</h3>
                <p>生成专属二维码，方便线下推广和社交分享</p>
                <button class="btn-generate" @click="generateQRCode">
                  生成二维码
                </button>
              </div>
              
              <div class="tool-card">
                <div class="tool-icon">
                  <a-icon type="file-image" />
                </div>
                <h3>推广素材</h3>
                <p>下载官方推广素材，包括海报、横幅等营销物料</p>
                <button class="btn-download" @click="downloadMaterials">
                  下载素材
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </WebsitePage>
</template>

<script>
import WebsitePage from '@/components/website/WebsitePage.vue'
import { getReferralStats, generateReferralLink } from '@/api/usercenter'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import Vue from 'vue'

export default {
  name: 'Affiliate',
  components: {
    WebsitePage
  },
  data() {
    return {
      loading: true,
      totalEarnings: 0,
      referrals: 0,
      conversionRate: 0,
      affiliateLink: '',
      userInfo: null
    }
  },
  async mounted() {
    await this.checkLoginAndLoadData()
  },
  methods: {
    // 检查登录状态并加载数据
    async checkLoginAndLoadData() {
      const token = Vue.ls.get(ACCESS_TOKEN)
      if (!token) {
        this.$router.push({ path: '/login', query: { redirect: this.$route.fullPath } })
        return
      }
      
      try {
        await this.loadReferralData()
        await this.loadReferralLink()
      } catch (error) {
        console.error('加载分销数据失败:', error)
        this.$notification.error({
          message: '加载失败',
          description: '获取分销数据失败，请稍后重试',
          placement: 'topRight'
        })
      } finally {
        this.loading = false
      }
    },

    // 加载推荐统计数据
    async loadReferralData() {
      try {
        const response = await getReferralStats()
        if (response.success) {
          const data = response.result
          this.totalEarnings = data.total_reward_amount || 0
          this.referrals = data.total_referrals || 0
          // 计算转化率：有效推荐数 / 总推荐数 * 100
          this.conversionRate = this.referrals > 0 ? 
            ((data.rewarded_referrals || 0) / this.referrals * 100).toFixed(1) : 0
        }
      } catch (error) {
        console.error('获取推荐统计失败:', error)
        throw error
      }
    },

    // 加载推荐链接
    async loadReferralLink() {
      try {
        const response = await generateReferralLink({})
        if (response.success) {
          this.affiliateLink = response.result || ''
        }
      } catch (error) {
        console.error('获取推荐链接失败:', error)
        // 如果获取失败，使用默认链接格式
        this.affiliateLink = `${window.location.origin}?ref=loading...`
      }
    },

     // 复制推广链接
     copyLink() {
      if (!this.affiliateLink) {
        this.$notification.warning({
          message: '推广链接未生成',
          description: '推广链接正在生成中，请稍后再试',
          placement: 'topRight'
        })
        return
      }
      
      navigator.clipboard.writeText(this.affiliateLink).then(() => {
        this.$notification.success({
          message: '推广链接已复制',
          description: '推广链接已成功复制到剪贴板，快去分享给好友吧！',
          placement: 'topRight',
          duration: 3,
          style: {
            width: '380px',
            marginTop: '101px',
            borderRadius: '8px',
            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'
          }
        })
      }).catch(() => {
        this.$notification.error({
          message: '复制失败',
          description: '复制推广链接失败，请手动复制',
          placement: 'topRight'
        })
      })
    },

    // 生成推广二维码
    generateQRCode() {
      if (!this.affiliateLink) {
        this.$notification.warning({
          message: '推广链接未生成',
          description: '请等待推广链接生成完成后再生成二维码',
          placement: 'topRight'
        })
        return
      }
      
      this.$notification.info({
        message: '生成推广二维码',
        description: '正在生成推广二维码，请稍候...',
        placement: 'topRight',
        duration: 3,
        style: {
          width: '350px',
          marginTop: '101px',
          borderRadius: '8px',
          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'
        }
      })
      // TODO: 实现二维码生成功能
    },

    // 下载推广素材
    downloadMaterials() {
      this.$notification.info({
        message: '下载推广素材',
        description: '正在准备推广素材下载，请稍候...',
        placement: 'topRight',
        duration: 3,
        style: {
          width: '350px',
          marginTop: '101px',
          borderRadius: '8px',
          boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'
        }
      })
       // TODO: 实现推广素材下载功能
     },

     // 格式化数字显示
     formatNumber(num) {
       if (num === null || num === undefined) return '0'
       const number = parseFloat(num)
       if (isNaN(number)) return '0'
       
       // 如果是金额，保留两位小数
       if (num === this.totalEarnings) {
         return number.toLocaleString('zh-CN', {
           minimumFractionDigits: 2,
           maximumFractionDigits: 2
         })
       }
       
       // 其他数字不保留小数
       return number.toLocaleString('zh-CN')
     }
   }
 }
</script>

<style scoped>
.affiliate-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  min-height: 100vh;
  padding: 2rem 0;
}

/* 简洁页面标题 */
.simple-header {
  text-align: center;
  padding: 2rem 0 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.simple-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.simple-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

/* 分销内容区域 */
.affiliate-section {
  padding: 4rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 收益展示 */
.earnings-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.earnings-card h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

.earnings-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.earning-item {
  text-align: center;
}

.earning-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.5rem;
}

.earning-label {
  color: #64748b;
  font-weight: 600;
}

/* 推广工具 */
.tools-section h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 2rem 0;
  text-align: center;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.tool-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.tool-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.tool-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: white;
  font-size: 2rem;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.tool-card h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.tool-card p {
  color: #64748b;
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
}

.link-input {
  margin-top: 1rem;
}

.btn-generate,
.btn-download {
  width: 100%;
  padding: 0.875rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: none;
  color: white;
  border-radius: 10px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-generate:hover,
.btn-download:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .simple-title {
    font-size: 2rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }

  .earnings-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .tools-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .container {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .simple-title {
    font-size: 1.8rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }
}
</style>
