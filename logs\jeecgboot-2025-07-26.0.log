2025-07-26 12:02:26.149 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-26 12:02:26.173 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 16448 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-26 12:02:26.173 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-26 12:02:26.516 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-26 12:02:27.645 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 12:02:27.647 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 12:02:27.782 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 123ms. Found 0 Redis repository interfaces.
2025-07-26 12:02:27.926 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-26 12:02:27.928 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-26 12:02:27.928 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-26 12:02:28.000 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-26 12:02:28.001 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-26 12:02:28.001 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-26 12:02:28.001 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-26 12:02:28.002 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-26 12:02:28.002 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-26 12:02:28.002 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-26 12:02:28.002 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-26 12:02:28.002 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-26 12:02:28.003 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-26 12:02:28.170 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.173 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.174 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.175 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.175 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.176 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.176 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.177 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.178 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.178 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.179 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.180 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.181 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.181 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.182 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.182 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.183 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.183 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.184 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.185 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.201 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.204 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.267 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.330 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.333 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$c727402c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.365 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.781 [main] INFO  org.jeecg.config.shiro.ShiroConfig:218 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-26 12:02:28.782 [main] INFO  org.jeecg.config.shiro.ShiroConfig:236 - ===============(2)创建RedisManager,连接Redis..
2025-07-26 12:02:28.785 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.787 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.815 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.973 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.980 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$d1796904] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:28.987 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:29.000 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$e489505c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:29.040 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$7b03d5e3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:29.044 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 12:02:29.275 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-26 12:02:29.281 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-26 12:02:29.281 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-26 12:02:29.281 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-26 12:02:29.430 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-26 12:02:29.430 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3225 ms
2025-07-26 12:02:30.006 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-26 12:02:30.007 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-26 12:02:30.007 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-26 12:02:31.442 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:76 -  --- redis config init --- 
2025-07-26 12:02:32.347 [main] INFO  org.jeecg.modules.jianying.service.TosService:86 - 开始初始化TOS客户端...
2025-07-26 12:02:32.348 [main] INFO  org.jeecg.modules.jianying.service.TosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-26 12:02:32.521 [main] INFO  org.jeecg.modules.jianying.service.TosService:115 - 外网TOS客户端初始化成功！
2025-07-26 12:02:32.525 [main] INFO  org.jeecg.modules.jianying.service.TosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-26 12:02:32.526 [main] INFO  org.jeecg.modules.jianying.service.TosService:591 - 正在测试TOS连接...
2025-07-26 12:02:32.526 [main] INFO  org.jeecg.modules.jianying.service.TosService:593 - TOS连接测试成功！
2025-07-26 12:02:33.711 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:86 - 开始初始化TOS客户端...
2025-07-26 12:02:33.712 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-26 12:02:33.713 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:115 - 外网TOS客户端初始化成功！
2025-07-26 12:02:33.715 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-26 12:02:33.715 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:627 - 正在测试TOS连接...
2025-07-26 12:02:33.715 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:629 - TOS连接测试成功！
2025-07-26 12:02:33.923 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:48 - 🔍 开始初始化敏感词库...
2025-07-26 12:02:34.217 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:64 - ✅ 敏感词库初始化完成
2025-07-26 12:02:34.592 [main] INFO  o.j.m.jianying.service.JianyingEffectSearchService:86 - 初始化预定义特效映射完成，共 4 个特效
2025-07-26 12:02:34.600 [main] INFO  org.jeecg.modules.jianying.service.CozeApiService:64 - RestTemplate初始化完成，支持TOS文件下载
2025-07-26 12:02:34.634 [main] INFO  o.j.m.jianying.service.JianyingMaskSearchService:73 - 剪映蒙版搜索服务初始化完成
2025-07-26 12:02:34.665 [main] INFO  o.j.m.j.s.internal.JianyingProEffectSearchService:87 - 超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效
2025-07-26 12:02:34.671 [main] INFO  o.j.m.j.service.internal.JianyingProCozeApiService:59 - 超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载
2025-07-26 12:02:35.115 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-26 12:02:35.118 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-26 12:02:35.127 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-26 12:02:35.127 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-26 12:02:35.131 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-26 12:02:35.133 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-26 12:02:35.134 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-G0NDD8J1753502555117'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-26 12:02:35.135 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-26 12:02:35.135 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-26 12:02:35.135 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@198524ec
2025-07-26 12:02:37.410 [main] INFO  o.j.m.jmreport.config.JimuReportConfiguration:55 -  --- Init JimuReport Config --- 
2025-07-26 12:02:38.238 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-26 12:02:38.367 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  代码生成器数据库连接，使用application.yml的DB配置 ###################
2025-07-26 12:02:38.392 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-26 12:02:38.465 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:21 - ---创建线程池---
2025-07-26 12:02:38.466 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-26 12:02:38.468 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'jmReportTaskExecutor'
2025-07-26 12:02:39.112 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-07-26 12:02:39.133 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/jeecg-boot'
2025-07-26 12:02:39.133 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:93 - Documentation plugins bootstrapped
2025-07-26 12:02:39.136 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:79 - Found 1 custom documentation plugin(s)
2025-07-26 12:02:39.239 [main] INFO  s.d.spring.web.scanners.ApiListingReferenceScanner:44 - Scanning for api listing references
2025-07-26 12:02:39.379 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_1
2025-07-26 12:02:39.385 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_1
2025-07-26 12:02:39.389 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_1
2025-07-26 12:02:39.400 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_2
2025-07-26 12:02:39.403 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-26 12:02:39.404 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_1
2025-07-26 12:02:39.405 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_1
2025-07-26 12:02:39.406 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_1
2025-07-26 12:02:39.408 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_2
2025-07-26 12:02:39.413 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_2
2025-07-26 12:02:39.421 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_3
2025-07-26 12:02:39.424 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-26 12:02:39.425 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_2
2025-07-26 12:02:39.427 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_2
2025-07-26 12:02:39.428 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_2
2025-07-26 12:02:39.433 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_3
2025-07-26 12:02:39.437 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_3
2025-07-26 12:02:39.448 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_4
2025-07-26 12:02:39.449 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-26 12:02:39.450 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_3
2025-07-26 12:02:39.451 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_3
2025-07-26 12:02:39.453 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_4
2025-07-26 12:02:39.456 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_4
2025-07-26 12:02:39.465 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_5
2025-07-26 12:02:39.466 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-26 12:02:39.467 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_4
2025-07-26 12:02:39.468 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_4
2025-07-26 12:02:39.469 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_5
2025-07-26 12:02:39.473 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_5
2025-07-26 12:02:39.479 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_6
2025-07-26 12:02:39.481 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-26 12:02:39.482 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_5
2025-07-26 12:02:39.483 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_5
2025-07-26 12:02:39.486 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_6
2025-07-26 12:02:39.490 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_6
2025-07-26 12:02:39.497 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_7
2025-07-26 12:02:39.499 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-26 12:02:39.500 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_6
2025-07-26 12:02:39.501 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_6
2025-07-26 12:02:39.501 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByReferrerIdUsingGET_1
2025-07-26 12:02:39.503 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_7
2025-07-26 12:02:39.506 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_7
2025-07-26 12:02:39.514 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getUsageStatsUsingGET_1
2025-07-26 12:02:39.522 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_8
2025-07-26 12:02:39.523 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-26 12:02:39.524 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_7
2025-07-26 12:02:39.525 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_7
2025-07-26 12:02:39.525 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_8
2025-07-26 12:02:39.527 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_8
2025-07-26 12:02:39.532 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_9
2025-07-26 12:02:39.533 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-26 12:02:39.534 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_8
2025-07-26 12:02:39.535 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_8
2025-07-26 12:02:39.536 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_9
2025-07-26 12:02:39.540 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_9
2025-07-26 12:02:39.549 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_10
2025-07-26 12:02:39.551 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-26 12:02:39.552 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_9
2025-07-26 12:02:39.553 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_9
2025-07-26 12:02:39.556 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_10
2025-07-26 12:02:39.559 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_10
2025-07-26 12:02:39.568 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_11
2025-07-26 12:02:39.572 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-26 12:02:39.573 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_10
2025-07-26 12:02:39.574 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_10
2025-07-26 12:02:39.578 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_11
2025-07-26 12:02:39.581 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_11
2025-07-26 12:02:39.586 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_12
2025-07-26 12:02:39.588 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-26 12:02:39.589 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_11
2025-07-26 12:02:39.590 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_11
2025-07-26 12:02:39.591 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_12
2025-07-26 12:02:39.594 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_12
2025-07-26 12:02:39.601 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_13
2025-07-26 12:02:39.603 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-26 12:02:39.604 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_12
2025-07-26 12:02:39.605 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_12
2025-07-26 12:02:39.607 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_13
2025-07-26 12:02:39.612 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_13
2025-07-26 12:02:39.618 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_14
2025-07-26 12:02:39.619 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_13
2025-07-26 12:02:39.620 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_13
2025-07-26 12:02:39.622 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_13
2025-07-26 12:02:39.623 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_14
2025-07-26 12:02:39.627 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_14
2025-07-26 12:02:39.633 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_15
2025-07-26 12:02:39.635 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_14
2025-07-26 12:02:39.636 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_14
2025-07-26 12:02:39.638 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_14
2025-07-26 12:02:39.639 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_15
2025-07-26 12:02:39.643 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_15
2025-07-26 12:02:39.748 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addAudiosUsingPOST_1
2025-07-26 12:02:39.750 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addCaptionsUsingPOST_1
2025-07-26 12:02:39.752 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addEffectsUsingPOST_1
2025-07-26 12:02:39.753 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addImagesUsingPOST_1
2025-07-26 12:02:39.755 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addKeyframesUsingPOST_1
2025-07-26 12:02:39.762 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addVideosUsingPOST_1
2025-07-26 12:02:39.797 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_16
2025-07-26 12:02:39.798 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_15
2025-07-26 12:02:39.800 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_15
2025-07-26 12:02:39.801 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_15
2025-07-26 12:02:39.802 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_16
2025-07-26 12:02:39.805 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_16
2025-07-26 12:02:39.810 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_17
2025-07-26 12:02:39.812 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_16
2025-07-26 12:02:39.813 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_16
2025-07-26 12:02:39.814 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_16
2025-07-26 12:02:39.815 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_17
2025-07-26 12:02:39.820 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_17
2025-07-26 12:02:39.824 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_18
2025-07-26 12:02:39.825 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_17
2025-07-26 12:02:39.826 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_17
2025-07-26 12:02:39.827 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_17
2025-07-26 12:02:39.828 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_18
2025-07-26 12:02:39.831 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_18
2025-07-26 12:02:39.835 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_19
2025-07-26 12:02:39.836 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_18
2025-07-26 12:02:39.837 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_18
2025-07-26 12:02:39.838 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_18
2025-07-26 12:02:39.839 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_19
2025-07-26 12:02:39.843 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_19
2025-07-26 12:02:39.850 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_20
2025-07-26 12:02:39.851 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_19
2025-07-26 12:02:39.853 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_19
2025-07-26 12:02:39.855 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_19
2025-07-26 12:02:39.856 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_20
2025-07-26 12:02:39.859 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_20
2025-07-26 12:02:39.867 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_21
2025-07-26 12:02:39.868 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_20
2025-07-26 12:02:39.874 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_21
2025-07-26 12:02:39.875 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_20
2025-07-26 12:02:39.876 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_20
2025-07-26 12:02:39.879 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_21
2025-07-26 12:02:39.891 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_22
2025-07-26 12:02:39.895 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_22
2025-07-26 12:02:39.896 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_21
2025-07-26 12:02:39.897 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_21
2025-07-26 12:02:39.898 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_21
2025-07-26 12:02:39.911 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_22
2025-07-26 12:02:39.933 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getReferralStatsUsingGET_1
2025-07-26 12:02:39.947 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendEmailCodeUsingPOST_1
2025-07-26 12:02:39.948 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendSmsCodeUsingPOST_1
2025-07-26 12:02:39.949 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: verifyCodeUsingPOST_1
2025-07-26 12:02:40.813 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 15.07 seconds (JVM running for 15.965)
2025-07-26 12:02:40.818 [main] INFO  org.jeecg.JeecgSystemApplication:39 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------
2025-07-26 12:02:41.730 [RMI TCP Connection(4)-**********] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 12:02:41.730 [RMI TCP Connection(4)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-26 12:02:41.739 [RMI TCP Connection(4)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 9 ms
2025-07-26 12:02:43.712 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:02:43.712 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:02:43.712 [http-nio-8080-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:02:43.715 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:02:43.715 [http-nio-8080-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:02:43.717 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:02:44.057 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-26 12:02:44.057 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-26 12:02:44.057 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-26 12:02:44.058 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:02:44.058 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:02:44.058 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:02:46.380 [http-nio-8080-exec-10] INFO  o.j.config.sign.interceptor.SignAuthInterceptor:35 - request URI = /jeecg-boot/sys/dict/getDictItems/plugin_category
2025-07-26 12:02:46.382 [http-nio-8080-exec-9] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:02:46.383 [http-nio-8080-exec-9] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:02:46.393 [http-nio-8080-exec-10] INFO  org.jeecg.config.sign.util.SignUtil:48 - Param paramsJsonStr : {}
2025-07-26 12:02:46.395 [http-nio-8080-exec-10] INFO  org.jeecg.config.sign.util.SignUtil:35 - Param Sign : E19D6243CB1945AB4F7202A1B00F77D5
2025-07-26 12:02:46.399 [http-nio-8080-exec-10] INFO  o.j.modules.system.controller.SysDictController:157 -  dictCode : plugin_category
2025-07-26 12:02:46.506 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-26 12:02:46.507 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:02:47.571 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:02:47.572 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:02:47.573 [http-nio-8080-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:02:47.573 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:02:47.573 [http-nio-8080-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:02:47.573 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:02:47.584 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-26 12:02:47.584 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:02:47.584 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-26 12:02:47.584 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:02:47.588 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-26 12:02:47.588 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:02:51.372 [http-nio-8080-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:02:51.372 [http-nio-8080-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:02:51.425 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-26 12:02:51.425 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:02:52.201 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:02:52.201 [http-nio-8080-exec-2] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:02:52.201 [http-nio-8080-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:02:52.201 [http-nio-8080-exec-5] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:02:52.201 [http-nio-8080-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:02:52.201 [http-nio-8080-exec-2] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:02:52.213 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-26 12:02:52.213 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-26 12:02:52.213 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:02:52.213 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:02:52.214 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-26 12:02:52.214 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:02:55.860 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:02:55.860 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:02:55.912 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-26 12:02:55.913 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:03:58.395 [http-nio-8080-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:03:58.395 [http-nio-8080-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:03:58.605 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-26 12:03:58.605 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:54:44.197 [http-nio-8080-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:54:44.197 [http-nio-8080-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:54:44.197 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 12:54:44.365 [http-nio-8080-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:54:44.365 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:54:44.365 [http-nio-8080-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 12:54:44.415 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-26 12:54:44.416 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:54:44.422 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-26 12:54:44.422 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 12:54:44.425 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-26 12:54:44.426 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 13:10:17.557 [http-nio-8080-exec-3] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 13:10:17.559 [http-nio-8080-exec-3] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 13:10:17.560 [http-nio-8080-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 13:10:17.560 [http-nio-8080-exec-1] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 13:10:17.564 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 13:10:17.566 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 13:10:17.587 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-26 13:10:17.587 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 13:10:17.595 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-26 13:10:17.596 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 13:10:17.596 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-26 13:10:17.596 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:31:10.251 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-26 14:31:10.279 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 33848 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-26 14:31:10.279 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-26 14:31:10.677 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-26 14:31:11.918 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 14:31:11.919 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 14:31:12.078 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 148ms. Found 0 Redis repository interfaces.
2025-07-26 14:31:12.258 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-26 14:31:12.259 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-26 14:31:12.259 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-26 14:31:12.346 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-26 14:31:12.347 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-26 14:31:12.349 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-26 14:31:12.349 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-26 14:31:12.350 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-26 14:31:12.350 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-26 14:31:12.350 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-26 14:31:12.351 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-26 14:31:12.351 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-26 14:31:12.351 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-26 14:31:12.542 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.545 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.546 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.547 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.548 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.548 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.549 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.549 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.551 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.552 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.553 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.554 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.555 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.556 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.556 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.557 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.557 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.559 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.559 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.560 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.580 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.584 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.641 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.700 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.702 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$c727402c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:12.740 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:13.111 [main] INFO  org.jeecg.config.shiro.ShiroConfig:218 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-26 14:31:13.113 [main] INFO  org.jeecg.config.shiro.ShiroConfig:236 - ===============(2)创建RedisManager,连接Redis..
2025-07-26 14:31:13.116 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:13.119 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:13.152 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:13.307 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:13.313 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$d1796904] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:13.321 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:13.334 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$e489505c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:13.379 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$7b03d5e3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:13.383 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:31:13.637 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-26 14:31:13.647 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-26 14:31:13.647 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-26 14:31:13.647 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-26 14:31:13.815 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-26 14:31:13.816 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3495 ms
2025-07-26 14:31:14.429 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-26 14:31:14.430 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-26 14:31:14.431 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-26 14:31:15.745 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:76 -  --- redis config init --- 
2025-07-26 14:31:16.540 [main] INFO  org.jeecg.modules.jianying.service.TosService:86 - 开始初始化TOS客户端...
2025-07-26 14:31:16.541 [main] INFO  org.jeecg.modules.jianying.service.TosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-26 14:31:16.654 [main] INFO  org.jeecg.modules.jianying.service.TosService:115 - 外网TOS客户端初始化成功！
2025-07-26 14:31:16.659 [main] INFO  org.jeecg.modules.jianying.service.TosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-26 14:31:16.659 [main] INFO  org.jeecg.modules.jianying.service.TosService:591 - 正在测试TOS连接...
2025-07-26 14:31:16.659 [main] INFO  org.jeecg.modules.jianying.service.TosService:593 - TOS连接测试成功！
2025-07-26 14:31:17.419 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:86 - 开始初始化TOS客户端...
2025-07-26 14:31:17.419 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-26 14:31:17.420 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:115 - 外网TOS客户端初始化成功！
2025-07-26 14:31:17.421 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-26 14:31:17.421 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:627 - 正在测试TOS连接...
2025-07-26 14:31:17.421 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:629 - TOS连接测试成功！
2025-07-26 14:31:17.587 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:48 - 🔍 开始初始化敏感词库...
2025-07-26 14:31:17.848 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:64 - ✅ 敏感词库初始化完成
2025-07-26 14:31:18.170 [main] INFO  o.j.m.jianying.service.JianyingEffectSearchService:86 - 初始化预定义特效映射完成，共 4 个特效
2025-07-26 14:31:18.177 [main] INFO  org.jeecg.modules.jianying.service.CozeApiService:64 - RestTemplate初始化完成，支持TOS文件下载
2025-07-26 14:31:18.203 [main] INFO  o.j.m.jianying.service.JianyingMaskSearchService:73 - 剪映蒙版搜索服务初始化完成
2025-07-26 14:31:18.231 [main] INFO  o.j.m.j.s.internal.JianyingProEffectSearchService:87 - 超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效
2025-07-26 14:31:18.237 [main] INFO  o.j.m.j.service.internal.JianyingProCozeApiService:59 - 超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载
2025-07-26 14:31:18.610 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-26 14:31:18.612 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-26 14:31:18.619 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-26 14:31:18.619 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-26 14:31:18.621 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-26 14:31:18.623 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-26 14:31:18.624 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-G0NDD8J1753511478611'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-26 14:31:18.624 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-26 14:31:18.624 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-26 14:31:18.624 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@33478a2f
2025-07-26 14:31:20.431 [main] INFO  o.j.m.jmreport.config.JimuReportConfiguration:55 -  --- Init JimuReport Config --- 
2025-07-26 14:31:21.150 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-26 14:31:21.237 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  代码生成器数据库连接，使用application.yml的DB配置 ###################
2025-07-26 14:31:21.255 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-26 14:31:21.310 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:21 - ---创建线程池---
2025-07-26 14:31:21.311 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-26 14:31:21.312 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'jmReportTaskExecutor'
2025-07-26 14:31:21.940 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-07-26 14:31:21.953 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:559 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8080 is already in use
2025-07-26 14:31:21.955 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:218 - Shutting down ExecutorService 'jmReportTaskExecutor'
2025-07-26 14:31:21.958 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:845 - Shutting down Quartz Scheduler
2025-07-26 14:31:21.958 [main] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753511478611 shutting down.
2025-07-26 14:31:21.958 [main] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753511478611 paused.
2025-07-26 14:31:21.959 [main] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753511478611 shutdown complete.
2025-07-26 14:31:21.963 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-07-26 14:31:21.966 [main] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-07-26 14:31:21.970 [main] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-07-26 14:31:21.970 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-07-26 14:31:21.989 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Pausing ProtocolHandler ["http-nio-8080"]
2025-07-26 14:31:21.989 [main] INFO  org.apache.catalina.core.StandardService:173 - Stopping service [Tomcat]
2025-07-26 14:31:21.991 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Stopping ProtocolHandler ["http-nio-8080"]
2025-07-26 14:31:21.991 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Destroying ProtocolHandler ["http-nio-8080"]
2025-07-26 14:31:22.000 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-26 14:31:22.003 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter:40 - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-07-26 14:33:54.797 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-26 14:33:54.863 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 34224 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-26 14:33:54.864 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-26 14:33:56.440 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-26 14:33:59.394 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 14:33:59.396 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 14:33:59.608 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 197ms. Found 0 Redis repository interfaces.
2025-07-26 14:33:59.777 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-26 14:33:59.777 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-26 14:33:59.779 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-26 14:33:59.929 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-26 14:33:59.930 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-26 14:33:59.931 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-26 14:33:59.931 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-26 14:33:59.931 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-26 14:33:59.931 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-26 14:33:59.932 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-26 14:33:59.932 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-26 14:33:59.932 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-26 14:33:59.932 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-26 14:34:00.245 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#17ff8810' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.252 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.253 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#17ff8810#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.254 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.255 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#17ff8810#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.259 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.261 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#17ff8810#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.262 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.264 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#17ff8810#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.266 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.267 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#17ff8810#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.268 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.269 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#17ff8810#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.270 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.273 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#17ff8810#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.274 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.277 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#17ff8810#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.278 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.279 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#17ff8810#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.280 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.312 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.317 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.433 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.550 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.556 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$9f7f0cc7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:00.644 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:01.283 [main] INFO  org.jeecg.config.shiro.ShiroConfig:218 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-26 14:34:01.286 [main] INFO  org.jeecg.config.shiro.ShiroConfig:236 - ===============(2)创建RedisManager,连接Redis..
2025-07-26 14:34:01.293 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:01.297 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:01.381 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:01.735 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:01.745 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$a9d1359f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:01.754 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:01.770 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$bce11cf7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:01.819 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$535ba27e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:01.826 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:34:02.624 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-26 14:34:02.639 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-26 14:34:02.640 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-26 14:34:02.641 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-26 14:34:02.998 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-26 14:34:02.998 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 8009 ms
2025-07-26 14:34:03.921 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-26 14:34:03.922 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-26 14:34:03.923 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-26 14:34:07.573 [main] ERROR org.mybatis.spring.mapper.MapperFactoryBean:82 - Error while adding the mapper 'interface org.jeecg.modules.system.mapper.SysUserRoleMapper' to configuration.
java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
	at sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:117)
	at sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:125)
	at sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49)
	at sun.reflect.generics.visitor.Reifier.reifyTypeArguments(Reifier.java:68)
	at sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:138)
	at sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49)
	at sun.reflect.generics.repository.ClassRepository.getSuperInterfaces(ClassRepository.java:108)
	at java.lang.Class.getGenericInterfaces(Class.java:913)
	at org.springframework.core.ResolvableType.getInterfaces(ResolvableType.java:502)
	at org.springframework.core.ResolvableType.as(ResolvableType.java:450)
	at org.springframework.core.GenericTypeResolver.resolveTypeArguments(GenericTypeResolver.java:139)
	at com.baomidou.mybatisplus.core.toolkit.ReflectionKit.getSuperClassGenericType(ReflectionKit.java:97)
	at com.baomidou.mybatisplus.core.injector.AbstractSqlInjector.inspectInject(AbstractSqlInjector.java:43)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parserInjector(MybatisMapperAnnotationBuilder.java:131)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:121)
	at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:83)
	at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:119)
	at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:80)
	at org.springframework.dao.support.DaoSupport.afterPropertiesSet(DaoSupport.java:44)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:453)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:239)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:405)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1237)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.jeecg.JeecgSystemApplication.main(JeecgSystemApplication.java:34)
Caused by: java.lang.ClassNotFoundException: org.jeecg.modules.system.entity.SysUserRole
	at java.net.URLClassLoader.findClass(URLClassLoader.java:382)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:349)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:348)
	at sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:114)
	... 64 common frames omitted
2025-07-26 14:34:07.578 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:559 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'jimuReportTokenService': Unsatisfied dependency expressed through field 'sysBaseAPI'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysBaseApiImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysUserRoleMapper' defined in file [D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes\org\jeecg\modules\system\mapper\SysUserRoleMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
2025-07-26 14:34:07.579 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-07-26 14:34:07.587 [main] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-07-26 14:34:07.598 [main] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-07-26 14:34:07.599 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-07-26 14:34:07.638 [main] INFO  org.apache.catalina.core.StandardService:173 - Stopping service [Tomcat]
2025-07-26 14:34:07.664 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener:136 - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-26 14:34:07.690 [main] ERROR org.springframework.boot.SpringApplication:837 - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'jimuReportTokenService': Unsatisfied dependency expressed through field 'sysBaseAPI'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysBaseApiImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysUserRoleMapper' defined in file [D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes\org\jeecg\modules\system\mapper\SysUserRoleMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:405)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1237)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.jeecg.JeecgSystemApplication.main(JeecgSystemApplication.java:34)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysBaseApiImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysUserRoleMapper' defined in file [D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes\org\jeecg\modules\system\mapper\SysUserRoleMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1420)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysUserRoleMapper' defined in file [D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes\org\jeecg\modules\system\mapper\SysUserRoleMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:453)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:239)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:130)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	... 31 common frames omitted
Caused by: java.lang.IllegalArgumentException: java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
	at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:83)
	at org.springframework.dao.support.DaoSupport.afterPropertiesSet(DaoSupport.java:44)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	... 44 common frames omitted
Caused by: java.lang.TypeNotPresentException: Type org.jeecg.modules.system.entity.SysUserRole not present
	at sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:117)
	at sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:125)
	at sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49)
	at sun.reflect.generics.visitor.Reifier.reifyTypeArguments(Reifier.java:68)
	at sun.reflect.generics.visitor.Reifier.visitClassTypeSignature(Reifier.java:138)
	at sun.reflect.generics.tree.ClassTypeSignature.accept(ClassTypeSignature.java:49)
	at sun.reflect.generics.repository.ClassRepository.getSuperInterfaces(ClassRepository.java:108)
	at java.lang.Class.getGenericInterfaces(Class.java:913)
	at org.springframework.core.ResolvableType.getInterfaces(ResolvableType.java:502)
	at org.springframework.core.ResolvableType.as(ResolvableType.java:450)
	at org.springframework.core.GenericTypeResolver.resolveTypeArguments(GenericTypeResolver.java:139)
	at com.baomidou.mybatisplus.core.toolkit.ReflectionKit.getSuperClassGenericType(ReflectionKit.java:97)
	at com.baomidou.mybatisplus.core.injector.AbstractSqlInjector.inspectInject(AbstractSqlInjector.java:43)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parserInjector(MybatisMapperAnnotationBuilder.java:131)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:121)
	at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:83)
	at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:119)
	at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:80)
	... 47 common frames omitted
Caused by: java.lang.ClassNotFoundException: org.jeecg.modules.system.entity.SysUserRole
	at java.net.URLClassLoader.findClass(URLClassLoader.java:382)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:424)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:349)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:357)
	at java.lang.Class.forName0(Native Method)
	at java.lang.Class.forName(Class.java:348)
	at sun.reflect.generics.factory.CoreReflectionFactory.makeNamedType(CoreReflectionFactory.java:114)
	... 64 common frames omitted
2025-07-26 14:36:14.127 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-26 14:36:14.156 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 34908 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-26 14:36:14.157 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-26 14:36:14.567 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-26 14:36:15.903 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 14:36:15.906 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 14:36:16.043 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 127ms. Found 0 Redis repository interfaces.
2025-07-26 14:36:16.221 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-26 14:36:16.222 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-26 14:36:16.223 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-26 14:36:16.308 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-26 14:36:16.309 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-26 14:36:16.309 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-26 14:36:16.309 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-26 14:36:16.309 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-26 14:36:16.309 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-26 14:36:16.309 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-26 14:36:16.309 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-26 14:36:16.309 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-26 14:36:16.309 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-26 14:36:16.501 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.503 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.506 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.507 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.507 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.509 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.510 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.510 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.512 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.514 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.515 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.516 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.517 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.518 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.519 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.519 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.520 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.520 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.521 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#615db358#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.522 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.543 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.548 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.613 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.667 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.671 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$c727402c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:16.707 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:17.079 [main] INFO  org.jeecg.config.shiro.ShiroConfig:218 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-26 14:36:17.080 [main] INFO  org.jeecg.config.shiro.ShiroConfig:236 - ===============(2)创建RedisManager,连接Redis..
2025-07-26 14:36:17.083 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:17.087 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:17.127 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:17.280 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:17.287 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$d1796904] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:17.295 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:17.307 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$e489505c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:17.343 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$7b03d5e3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:17.346 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:36:17.619 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-26 14:36:17.628 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-26 14:36:17.629 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-26 14:36:17.629 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-26 14:36:17.783 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-26 14:36:17.783 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3578 ms
2025-07-26 14:36:18.427 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-26 14:36:18.429 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-26 14:36:18.429 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-26 14:36:19.883 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:76 -  --- redis config init --- 
2025-07-26 14:36:20.804 [main] INFO  org.jeecg.modules.jianying.service.TosService:86 - 开始初始化TOS客户端...
2025-07-26 14:36:20.804 [main] INFO  org.jeecg.modules.jianying.service.TosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-26 14:36:20.929 [main] INFO  org.jeecg.modules.jianying.service.TosService:115 - 外网TOS客户端初始化成功！
2025-07-26 14:36:20.931 [main] INFO  org.jeecg.modules.jianying.service.TosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-26 14:36:20.931 [main] INFO  org.jeecg.modules.jianying.service.TosService:591 - 正在测试TOS连接...
2025-07-26 14:36:20.932 [main] INFO  org.jeecg.modules.jianying.service.TosService:593 - TOS连接测试成功！
2025-07-26 14:36:21.676 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:86 - 开始初始化TOS客户端...
2025-07-26 14:36:21.677 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-26 14:36:21.680 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:115 - 外网TOS客户端初始化成功！
2025-07-26 14:36:21.681 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-26 14:36:21.682 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:627 - 正在测试TOS连接...
2025-07-26 14:36:21.682 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:629 - TOS连接测试成功！
2025-07-26 14:36:21.836 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:48 - 🔍 开始初始化敏感词库...
2025-07-26 14:36:22.063 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:64 - ✅ 敏感词库初始化完成
2025-07-26 14:36:22.435 [main] INFO  o.j.m.jianying.service.JianyingEffectSearchService:86 - 初始化预定义特效映射完成，共 4 个特效
2025-07-26 14:36:22.444 [main] INFO  org.jeecg.modules.jianying.service.CozeApiService:64 - RestTemplate初始化完成，支持TOS文件下载
2025-07-26 14:36:22.472 [main] INFO  o.j.m.jianying.service.JianyingMaskSearchService:73 - 剪映蒙版搜索服务初始化完成
2025-07-26 14:36:22.502 [main] INFO  o.j.m.j.s.internal.JianyingProEffectSearchService:87 - 超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效
2025-07-26 14:36:22.508 [main] INFO  o.j.m.j.service.internal.JianyingProCozeApiService:59 - 超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载
2025-07-26 14:36:22.889 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-26 14:36:22.891 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-26 14:36:22.899 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-26 14:36:22.899 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-26 14:36:22.902 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-26 14:36:22.904 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-26 14:36:22.904 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-G0NDD8J17535********'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-26 14:36:22.905 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-26 14:36:22.905 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-26 14:36:22.905 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@66b3eab0
2025-07-26 14:36:24.692 [main] INFO  o.j.m.jmreport.config.JimuReportConfiguration:55 -  --- Init JimuReport Config --- 
2025-07-26 14:36:25.394 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-26 14:36:25.528 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  代码生成器数据库连接，使用application.yml的DB配置 ###################
2025-07-26 14:36:25.554 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-26 14:36:25.625 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:21 - ---创建线程池---
2025-07-26 14:36:25.625 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-26 14:36:25.628 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'jmReportTaskExecutor'
2025-07-26 14:36:26.380 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-07-26 14:36:26.402 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/jeecg-boot'
2025-07-26 14:36:26.403 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:93 - Documentation plugins bootstrapped
2025-07-26 14:36:26.406 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:79 - Found 1 custom documentation plugin(s)
2025-07-26 14:36:26.504 [main] INFO  s.d.spring.web.scanners.ApiListingReferenceScanner:44 - Scanning for api listing references
2025-07-26 14:36:26.652 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_1
2025-07-26 14:36:26.661 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_1
2025-07-26 14:36:26.667 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_1
2025-07-26 14:36:26.679 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_2
2025-07-26 14:36:26.682 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-26 14:36:26.683 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_1
2025-07-26 14:36:26.684 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_1
2025-07-26 14:36:26.685 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_1
2025-07-26 14:36:26.687 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_2
2025-07-26 14:36:26.693 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_2
2025-07-26 14:36:26.701 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_3
2025-07-26 14:36:26.704 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-26 14:36:26.705 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_2
2025-07-26 14:36:26.706 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_2
2025-07-26 14:36:26.706 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_2
2025-07-26 14:36:26.710 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_3
2025-07-26 14:36:26.713 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_3
2025-07-26 14:36:26.724 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_4
2025-07-26 14:36:26.725 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-26 14:36:26.726 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_3
2025-07-26 14:36:26.728 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_3
2025-07-26 14:36:26.731 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_4
2025-07-26 14:36:26.737 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_4
2025-07-26 14:36:26.744 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_5
2025-07-26 14:36:26.746 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-26 14:36:26.747 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_4
2025-07-26 14:36:26.748 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_4
2025-07-26 14:36:26.749 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_5
2025-07-26 14:36:26.752 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_5
2025-07-26 14:36:26.757 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_6
2025-07-26 14:36:26.759 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-26 14:36:26.760 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_5
2025-07-26 14:36:26.761 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_5
2025-07-26 14:36:26.765 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_6
2025-07-26 14:36:26.767 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_6
2025-07-26 14:36:26.774 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_7
2025-07-26 14:36:26.775 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-26 14:36:26.777 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_6
2025-07-26 14:36:26.777 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_6
2025-07-26 14:36:26.778 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByReferrerIdUsingGET_1
2025-07-26 14:36:26.780 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_7
2025-07-26 14:36:26.783 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_7
2025-07-26 14:36:26.790 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getUsageStatsUsingGET_1
2025-07-26 14:36:26.799 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_8
2025-07-26 14:36:26.800 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-26 14:36:26.800 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_7
2025-07-26 14:36:26.801 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_7
2025-07-26 14:36:26.802 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_8
2025-07-26 14:36:26.806 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_8
2025-07-26 14:36:26.810 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_9
2025-07-26 14:36:26.811 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-26 14:36:26.811 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_8
2025-07-26 14:36:26.812 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_8
2025-07-26 14:36:26.813 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_9
2025-07-26 14:36:26.816 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_9
2025-07-26 14:36:26.824 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_10
2025-07-26 14:36:26.827 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-26 14:36:26.828 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_9
2025-07-26 14:36:26.829 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_9
2025-07-26 14:36:26.831 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_10
2025-07-26 14:36:26.834 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_10
2025-07-26 14:36:26.843 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_11
2025-07-26 14:36:26.847 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-26 14:36:26.847 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_10
2025-07-26 14:36:26.848 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_10
2025-07-26 14:36:26.853 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_11
2025-07-26 14:36:26.857 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_11
2025-07-26 14:36:26.863 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_12
2025-07-26 14:36:26.864 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-26 14:36:26.865 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_11
2025-07-26 14:36:26.865 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_11
2025-07-26 14:36:26.866 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_12
2025-07-26 14:36:26.869 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_12
2025-07-26 14:36:26.876 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_13
2025-07-26 14:36:26.877 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-26 14:36:26.877 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_12
2025-07-26 14:36:26.879 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_12
2025-07-26 14:36:26.880 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_13
2025-07-26 14:36:26.885 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_13
2025-07-26 14:36:26.891 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_14
2025-07-26 14:36:26.893 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_13
2025-07-26 14:36:26.895 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_13
2025-07-26 14:36:26.896 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_13
2025-07-26 14:36:26.896 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_14
2025-07-26 14:36:26.901 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_14
2025-07-26 14:36:26.908 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_15
2025-07-26 14:36:26.909 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_14
2025-07-26 14:36:26.911 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_14
2025-07-26 14:36:26.912 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_14
2025-07-26 14:36:26.913 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_15
2025-07-26 14:36:26.917 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_15
2025-07-26 14:36:27.007 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addAudiosUsingPOST_1
2025-07-26 14:36:27.009 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addCaptionsUsingPOST_1
2025-07-26 14:36:27.012 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addEffectsUsingPOST_1
2025-07-26 14:36:27.014 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addImagesUsingPOST_1
2025-07-26 14:36:27.016 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addKeyframesUsingPOST_1
2025-07-26 14:36:27.027 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addVideosUsingPOST_1
2025-07-26 14:36:27.076 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_16
2025-07-26 14:36:27.078 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_15
2025-07-26 14:36:27.079 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_15
2025-07-26 14:36:27.081 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_15
2025-07-26 14:36:27.082 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_16
2025-07-26 14:36:27.084 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_16
2025-07-26 14:36:27.090 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_17
2025-07-26 14:36:27.092 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_16
2025-07-26 14:36:27.094 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_16
2025-07-26 14:36:27.095 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_16
2025-07-26 14:36:27.096 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_17
2025-07-26 14:36:27.100 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_17
2025-07-26 14:36:27.104 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_18
2025-07-26 14:36:27.106 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_17
2025-07-26 14:36:27.107 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_17
2025-07-26 14:36:27.107 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_17
2025-07-26 14:36:27.111 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_18
2025-07-26 14:36:27.114 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_18
2025-07-26 14:36:27.122 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_19
2025-07-26 14:36:27.123 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_18
2025-07-26 14:36:27.124 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_18
2025-07-26 14:36:27.125 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_18
2025-07-26 14:36:27.127 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_19
2025-07-26 14:36:27.133 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_19
2025-07-26 14:36:27.143 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_20
2025-07-26 14:36:27.145 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_19
2025-07-26 14:36:27.147 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_19
2025-07-26 14:36:27.149 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_19
2025-07-26 14:36:27.151 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_20
2025-07-26 14:36:27.155 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_20
2025-07-26 14:36:27.166 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_21
2025-07-26 14:36:27.168 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_20
2025-07-26 14:36:27.172 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_21
2025-07-26 14:36:27.173 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_20
2025-07-26 14:36:27.175 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_20
2025-07-26 14:36:27.176 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_21
2025-07-26 14:36:27.189 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_22
2025-07-26 14:36:27.192 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_22
2025-07-26 14:36:27.193 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_21
2025-07-26 14:36:27.194 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_21
2025-07-26 14:36:27.198 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_21
2025-07-26 14:36:27.208 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_22
2025-07-26 14:36:27.237 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getReferralStatsUsingGET_1
2025-07-26 14:36:27.259 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendEmailCodeUsingPOST_1
2025-07-26 14:36:27.261 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendSmsCodeUsingPOST_1
2025-07-26 14:36:27.263 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: verifyCodeUsingPOST_1
2025-07-26 14:36:28.046 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 14.466 seconds (JVM running for 15.516)
2025-07-26 14:36:28.053 [main] INFO  org.jeecg.JeecgSystemApplication:39 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------
2025-07-26 14:36:28.666 [RMI TCP Connection(1)-**********] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 14:36:28.666 [RMI TCP Connection(1)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-26 14:36:28.682 [RMI TCP Connection(1)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 15 ms
2025-07-26 14:36:36.059 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 14:36:36.059 [http-nio-8080-exec-3] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 14:36:36.059 [http-nio-8080-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 14:36:36.065 [http-nio-8080-exec-3] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 14:36:36.065 [http-nio-8080-exec-6] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 14:36:36.069 [http-nio-8080-exec-4] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 14:36:36.540 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 12
2025-07-26 14:36:36.540 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-26 14:36:36.540 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 13
2025-07-26 14:36:36.541 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:36:36.541 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:36:36.541 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:36:38.117 [http-nio-8080-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:535 - --查询规则-->status = 1
2025-07-26 14:36:38.117 [http-nio-8080-exec-8] INFO  org.jeecg.common.system.query.QueryGenerator:227 - 排序规则>>列:null,排序方式:null
2025-07-26 14:36:38.359 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 11
2025-07-26 14:36:38.360 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:36:43.442 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:169 - 官网用户登录: admin, 登录类型: website
2025-07-26 14:36:43.444 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:785 - === 开始统一登录处理 ===
2025-07-26 14:36:43.444 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:786 - 用户：admin，登录类型：website
2025-07-26 14:36:43.444 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.util.RoleChecker:39 - === 开始检查用户admin角色 ===
2025-07-26 14:36:43.444 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.util.RoleChecker:40 - 用户ID：e9ca23d68d884d4ebb19d07889727dae
2025-07-26 14:36:43.459 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.util.RoleChecker:47 - 用户 e9ca23d68d884d4ebb19d07889727dae 的角色数量：1
2025-07-26 14:36:43.470 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.util.RoleChecker:58 - 用户 e9ca23d68d884d4ebb19d07889727dae 具有角色：管理员 (role_code: admin)
2025-07-26 14:36:43.471 [http-nio-8080-exec-4] INFO  org.jeecg.modules.system.util.RoleChecker:60 - *** 用户 e9ca23d68d884d4ebb19d07889727dae 具有admin角色 ***
2025-07-26 14:36:43.471 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:808 - admin用户，允许多设备登录：admin
2025-07-26 14:36:43.524 [http-nio-8080-exec-4] INFO  o.j.c.modules.redis.writer.JeecgRedisCacheWriter:104 - redis remove key:sys:cache:user::admin
2025-07-26 14:36:43.642 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:944 - 开始记录用户在线状态 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 会话ID: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuOTc3NjY2NTUzMDA3MDE3NCwiZXhwIjoxNzUzNTU1MDAzLCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUzNTExODAzNTMzfQ.KhWttog3VCtKz8gaOuuWoDx-6GK3IkDNS-T80BUZ7nQ
2025-07-26 14:36:43.677 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:948 - 设置用户 e9ca23d68d884d4ebb19d07889727dae 的其他会话为离线，影响行数: 6
2025-07-26 14:36:43.679 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:960 - 准备插入在线用户记录: AicgOnlineUsers(id=null, userId=e9ca23d68d884d4ebb19d07889727dae, sessionId=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuOTc3NjY2NTUzMDA3MDE3NCwiZXhwIjoxNzUzNTU1MDAzLCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUzNTExODAzNTMzfQ.KhWttog3VCtKz8gaOuuWoDx-6GK3IkDNS-T80BUZ7nQ, loginTime=Sat Jul 26 14:36:43 CST 2025, lastActiveTime=Sat Jul 26 14:36:43 CST 2025, ipAddress=null, userAgent=null, status=true, createTime=Sat Jul 26 14:36:43 CST 2025, updateTime=Sat Jul 26 14:36:43 CST 2025)
2025-07-26 14:36:43.703 [http-nio-8080-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:966 - 用户 e9ca23d68d884d4ebb19d07889727dae 在线状态记录成功，插入行数: 1, 生成ID: 1948995882665967617
2025-07-26 14:36:43.723 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:79 -  LogContent length : 16
2025-07-26 14:36:43.724 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:80 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:36:44.027 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-26 14:36:44.027 [http-nio-8080-exec-8] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:38:34.978 [http-nio-8080-exec-9] INFO  o.jeecg.modules.system.controller.LoginController:169 - 官网用户登录: admin, 登录类型: website
2025-07-26 14:38:34.979 [http-nio-8080-exec-9] INFO  o.jeecg.modules.system.controller.LoginController:785 - === 开始统一登录处理 ===
2025-07-26 14:38:34.979 [http-nio-8080-exec-9] INFO  o.jeecg.modules.system.controller.LoginController:786 - 用户：admin，登录类型：website
2025-07-26 14:38:34.979 [http-nio-8080-exec-9] INFO  org.jeecg.modules.system.util.RoleChecker:39 - === 开始检查用户admin角色 ===
2025-07-26 14:38:34.980 [http-nio-8080-exec-9] INFO  org.jeecg.modules.system.util.RoleChecker:40 - 用户ID：e9ca23d68d884d4ebb19d07889727dae
2025-07-26 14:38:34.985 [http-nio-8080-exec-9] INFO  org.jeecg.modules.system.util.RoleChecker:47 - 用户 e9ca23d68d884d4ebb19d07889727dae 的角色数量：1
2025-07-26 14:38:34.989 [http-nio-8080-exec-9] INFO  org.jeecg.modules.system.util.RoleChecker:58 - 用户 e9ca23d68d884d4ebb19d07889727dae 具有角色：管理员 (role_code: admin)
2025-07-26 14:38:34.990 [http-nio-8080-exec-9] INFO  org.jeecg.modules.system.util.RoleChecker:60 - *** 用户 e9ca23d68d884d4ebb19d07889727dae 具有admin角色 ***
2025-07-26 14:38:34.990 [http-nio-8080-exec-9] INFO  o.jeecg.modules.system.controller.LoginController:808 - admin用户，允许多设备登录：admin
2025-07-26 14:38:35.003 [http-nio-8080-exec-9] INFO  o.j.c.modules.redis.writer.JeecgRedisCacheWriter:104 - redis remove key:sys:cache:user::admin
2025-07-26 14:38:35.043 [http-nio-8080-exec-9] INFO  o.jeecg.modules.system.controller.LoginController:944 - 开始记录用户在线状态 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 会话ID: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuODAwMjk4MTE2MDMzNzEwOSwiZXhwIjoxNzUzNTU1MTE1LCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUzNTExOTE1MDA1fQ.S37BKGFKpCV3f-tdA38uJWl3FBLhOpUMhpu1640uFRM
2025-07-26 14:38:35.051 [http-nio-8080-exec-9] INFO  o.jeecg.modules.system.controller.LoginController:948 - 设置用户 e9ca23d68d884d4ebb19d07889727dae 的其他会话为离线，影响行数: 7
2025-07-26 14:38:35.052 [http-nio-8080-exec-9] INFO  o.jeecg.modules.system.controller.LoginController:960 - 准备插入在线用户记录: AicgOnlineUsers(id=null, userId=e9ca23d68d884d4ebb19d07889727dae, sessionId=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuODAwMjk4MTE2MDMzNzEwOSwiZXhwIjoxNzUzNTU1MTE1LCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUzNTExOTE1MDA1fQ.S37BKGFKpCV3f-tdA38uJWl3FBLhOpUMhpu1640uFRM, loginTime=Sat Jul 26 14:38:35 CST 2025, lastActiveTime=Sat Jul 26 14:38:35 CST 2025, ipAddress=null, userAgent=null, status=true, createTime=Sat Jul 26 14:38:35 CST 2025, updateTime=Sat Jul 26 14:38:35 CST 2025)
2025-07-26 14:38:35.063 [http-nio-8080-exec-9] INFO  o.jeecg.modules.system.controller.LoginController:966 - 用户 e9ca23d68d884d4ebb19d07889727dae 在线状态记录成功，插入行数: 1, 生成ID: 1948996349764632578
2025-07-26 14:38:35.066 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:79 -  LogContent length : 16
2025-07-26 14:38:35.067 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:80 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:38:35.381 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-26 14:38:35.382 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:42:59.974 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:218 - Shutting down ExecutorService 'jmReportTaskExecutor'
2025-07-26 14:42:59.977 [SpringContextShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:845 - Shutting down Quartz Scheduler
2025-07-26 14:42:59.977 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J17535******** shutting down.
2025-07-26 14:42:59.977 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J17535******** paused.
2025-07-26 14:42:59.978 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J17535******** shutdown complete.
2025-07-26 14:42:59.984 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-07-26 14:42:59.985 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-07-26 14:42:59.991 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-07-26 14:42:59.991 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-07-26 14:43:27.691 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-26 14:43:27.717 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 25692 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-26 14:43:27.718 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-26 14:43:28.107 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-26 14:43:29.367 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 14:43:29.369 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 14:43:29.503 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 124ms. Found 0 Redis repository interfaces.
2025-07-26 14:43:29.671 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-26 14:43:29.671 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-26 14:43:29.672 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-26 14:43:29.751 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-26 14:43:29.752 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-26 14:43:29.752 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-26 14:43:29.752 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-26 14:43:29.752 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-26 14:43:29.752 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-26 14:43:29.752 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-26 14:43:29.752 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-26 14:43:29.752 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-26 14:43:29.753 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-26 14:43:29.934 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.937 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.938 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.939 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.939 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.940 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.941 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.941 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.942 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.943 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.944 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.945 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.946 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.947 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.948 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.949 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.950 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.951 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.951 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.952 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.970 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:29.974 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.035 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.087 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.091 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$dac4cbd0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.127 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.565 [main] INFO  org.jeecg.config.shiro.ShiroConfig:218 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-26 14:43:30.566 [main] INFO  org.jeecg.config.shiro.ShiroConfig:236 - ===============(2)创建RedisManager,连接Redis..
2025-07-26 14:43:30.570 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.573 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.607 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.770 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.776 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$e516f4a8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.785 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.797 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$f826dc00] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.834 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$8ea16187] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:30.837 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:43:31.097 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-26 14:43:31.104 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-26 14:43:31.105 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-26 14:43:31.105 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-26 14:43:31.258 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-26 14:43:31.258 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3497 ms
2025-07-26 14:43:31.850 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-26 14:43:31.852 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-26 14:43:31.852 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-26 14:43:33.167 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:76 -  --- redis config init --- 
2025-07-26 14:43:34.017 [main] INFO  org.jeecg.modules.jianying.service.TosService:86 - 开始初始化TOS客户端...
2025-07-26 14:43:34.017 [main] INFO  org.jeecg.modules.jianying.service.TosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-26 14:43:34.117 [main] INFO  org.jeecg.modules.jianying.service.TosService:115 - 外网TOS客户端初始化成功！
2025-07-26 14:43:34.121 [main] INFO  org.jeecg.modules.jianying.service.TosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-26 14:43:34.121 [main] INFO  org.jeecg.modules.jianying.service.TosService:591 - 正在测试TOS连接...
2025-07-26 14:43:34.121 [main] INFO  org.jeecg.modules.jianying.service.TosService:593 - TOS连接测试成功！
2025-07-26 14:43:34.935 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:86 - 开始初始化TOS客户端...
2025-07-26 14:43:34.936 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-26 14:43:34.937 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:115 - 外网TOS客户端初始化成功！
2025-07-26 14:43:34.937 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-26 14:43:34.937 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:627 - 正在测试TOS连接...
2025-07-26 14:43:34.939 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:629 - TOS连接测试成功！
2025-07-26 14:43:35.086 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:48 - 🔍 开始初始化敏感词库...
2025-07-26 14:43:35.320 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:64 - ✅ 敏感词库初始化完成
2025-07-26 14:43:35.664 [main] INFO  o.j.m.jianying.service.JianyingEffectSearchService:86 - 初始化预定义特效映射完成，共 4 个特效
2025-07-26 14:43:35.671 [main] INFO  org.jeecg.modules.jianying.service.CozeApiService:64 - RestTemplate初始化完成，支持TOS文件下载
2025-07-26 14:43:35.698 [main] INFO  o.j.m.jianying.service.JianyingMaskSearchService:73 - 剪映蒙版搜索服务初始化完成
2025-07-26 14:43:35.728 [main] INFO  o.j.m.j.s.internal.JianyingProEffectSearchService:87 - 超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效
2025-07-26 14:43:35.733 [main] INFO  o.j.m.j.service.internal.JianyingProCozeApiService:59 - 超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载
2025-07-26 14:43:36.092 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-26 14:43:36.094 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-26 14:43:36.101 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-26 14:43:36.101 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-26 14:43:36.104 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-26 14:43:36.105 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-26 14:43:36.106 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-G0NDD8J1753512216093'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-26 14:43:36.106 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-26 14:43:36.107 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-26 14:43:36.107 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5d260494
2025-07-26 14:43:38.002 [main] INFO  o.j.m.jmreport.config.JimuReportConfiguration:55 -  --- Init JimuReport Config --- 
2025-07-26 14:43:38.702 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-26 14:43:38.793 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  代码生成器数据库连接，使用application.yml的DB配置 ###################
2025-07-26 14:43:38.814 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-26 14:43:38.879 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:21 - ---创建线程池---
2025-07-26 14:43:38.880 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-26 14:43:38.882 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'jmReportTaskExecutor'
2025-07-26 14:43:39.584 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-07-26 14:43:39.606 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/jeecg-boot'
2025-07-26 14:43:39.607 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:93 - Documentation plugins bootstrapped
2025-07-26 14:43:39.610 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:79 - Found 1 custom documentation plugin(s)
2025-07-26 14:43:39.740 [main] INFO  s.d.spring.web.scanners.ApiListingReferenceScanner:44 - Scanning for api listing references
2025-07-26 14:43:39.882 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_1
2025-07-26 14:43:39.888 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_1
2025-07-26 14:43:39.891 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_1
2025-07-26 14:43:39.901 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_2
2025-07-26 14:43:39.903 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-26 14:43:39.904 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_1
2025-07-26 14:43:39.905 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_1
2025-07-26 14:43:39.906 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_1
2025-07-26 14:43:39.907 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_2
2025-07-26 14:43:39.910 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_2
2025-07-26 14:43:39.918 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_3
2025-07-26 14:43:39.921 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-26 14:43:39.922 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_2
2025-07-26 14:43:39.924 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_2
2025-07-26 14:43:39.925 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_2
2025-07-26 14:43:39.929 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_3
2025-07-26 14:43:39.935 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_3
2025-07-26 14:43:39.945 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_4
2025-07-26 14:43:39.947 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-26 14:43:39.948 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_3
2025-07-26 14:43:39.950 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_3
2025-07-26 14:43:39.953 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_4
2025-07-26 14:43:39.957 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_4
2025-07-26 14:43:39.966 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_5
2025-07-26 14:43:39.967 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-26 14:43:39.968 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_4
2025-07-26 14:43:39.968 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_4
2025-07-26 14:43:39.969 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_5
2025-07-26 14:43:39.973 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_5
2025-07-26 14:43:39.979 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_6
2025-07-26 14:43:39.981 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-26 14:43:39.982 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_5
2025-07-26 14:43:39.983 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_5
2025-07-26 14:43:39.986 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_6
2025-07-26 14:43:39.989 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_6
2025-07-26 14:43:39.995 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_7
2025-07-26 14:43:39.998 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-26 14:43:39.999 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_6
2025-07-26 14:43:40.000 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_6
2025-07-26 14:43:40.001 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByReferrerIdUsingGET_1
2025-07-26 14:43:40.003 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_7
2025-07-26 14:43:40.008 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_7
2025-07-26 14:43:40.015 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getUsageStatsUsingGET_1
2025-07-26 14:43:40.024 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_8
2025-07-26 14:43:40.026 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-26 14:43:40.028 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_7
2025-07-26 14:43:40.029 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_7
2025-07-26 14:43:40.031 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_8
2025-07-26 14:43:40.034 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_8
2025-07-26 14:43:40.039 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_9
2025-07-26 14:43:40.040 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-26 14:43:40.042 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_8
2025-07-26 14:43:40.043 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_8
2025-07-26 14:43:40.044 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_9
2025-07-26 14:43:40.047 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_9
2025-07-26 14:43:40.055 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_10
2025-07-26 14:43:40.058 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-26 14:43:40.059 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_9
2025-07-26 14:43:40.060 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_9
2025-07-26 14:43:40.062 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_10
2025-07-26 14:43:40.067 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_10
2025-07-26 14:43:40.075 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_11
2025-07-26 14:43:40.078 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-26 14:43:40.079 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_10
2025-07-26 14:43:40.080 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_10
2025-07-26 14:43:40.085 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_11
2025-07-26 14:43:40.090 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_11
2025-07-26 14:43:40.096 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_12
2025-07-26 14:43:40.097 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-26 14:43:40.098 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_11
2025-07-26 14:43:40.099 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_11
2025-07-26 14:43:40.100 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_12
2025-07-26 14:43:40.103 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_12
2025-07-26 14:43:40.109 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_13
2025-07-26 14:43:40.110 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-26 14:43:40.111 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_12
2025-07-26 14:43:40.112 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_12
2025-07-26 14:43:40.113 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_13
2025-07-26 14:43:40.117 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_13
2025-07-26 14:43:40.122 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_14
2025-07-26 14:43:40.124 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_13
2025-07-26 14:43:40.125 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_13
2025-07-26 14:43:40.126 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_13
2025-07-26 14:43:40.126 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_14
2025-07-26 14:43:40.130 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_14
2025-07-26 14:43:40.136 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_15
2025-07-26 14:43:40.138 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_14
2025-07-26 14:43:40.139 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_14
2025-07-26 14:43:40.140 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_14
2025-07-26 14:43:40.142 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_15
2025-07-26 14:43:40.145 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_15
2025-07-26 14:43:40.225 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addAudiosUsingPOST_1
2025-07-26 14:43:40.229 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addCaptionsUsingPOST_1
2025-07-26 14:43:40.230 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addEffectsUsingPOST_1
2025-07-26 14:43:40.232 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addImagesUsingPOST_1
2025-07-26 14:43:40.233 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addKeyframesUsingPOST_1
2025-07-26 14:43:40.240 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addVideosUsingPOST_1
2025-07-26 14:43:40.273 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_16
2025-07-26 14:43:40.275 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_15
2025-07-26 14:43:40.276 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_15
2025-07-26 14:43:40.277 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_15
2025-07-26 14:43:40.277 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_16
2025-07-26 14:43:40.279 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_16
2025-07-26 14:43:40.283 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_17
2025-07-26 14:43:40.284 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_16
2025-07-26 14:43:40.285 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_16
2025-07-26 14:43:40.286 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_16
2025-07-26 14:43:40.286 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_17
2025-07-26 14:43:40.290 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_17
2025-07-26 14:43:40.294 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_18
2025-07-26 14:43:40.295 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_17
2025-07-26 14:43:40.296 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_17
2025-07-26 14:43:40.297 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_17
2025-07-26 14:43:40.297 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_18
2025-07-26 14:43:40.302 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_18
2025-07-26 14:43:40.305 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_19
2025-07-26 14:43:40.306 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_18
2025-07-26 14:43:40.307 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_18
2025-07-26 14:43:40.308 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_18
2025-07-26 14:43:40.308 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_19
2025-07-26 14:43:40.311 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_19
2025-07-26 14:43:40.317 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_20
2025-07-26 14:43:40.319 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_19
2025-07-26 14:43:40.319 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_19
2025-07-26 14:43:40.320 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_19
2025-07-26 14:43:40.321 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_20
2025-07-26 14:43:40.324 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_20
2025-07-26 14:43:40.331 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_21
2025-07-26 14:43:40.333 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_20
2025-07-26 14:43:40.340 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_21
2025-07-26 14:43:40.342 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_20
2025-07-26 14:43:40.343 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_20
2025-07-26 14:43:40.345 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_21
2025-07-26 14:43:40.356 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_22
2025-07-26 14:43:40.358 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_22
2025-07-26 14:43:40.360 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_21
2025-07-26 14:43:40.361 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_21
2025-07-26 14:43:40.362 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_21
2025-07-26 14:43:40.376 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_22
2025-07-26 14:43:40.400 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getReferralStatsUsingGET_1
2025-07-26 14:43:40.415 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendEmailCodeUsingPOST_1
2025-07-26 14:43:40.416 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendSmsCodeUsingPOST_1
2025-07-26 14:43:40.417 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: verifyCodeUsingPOST_1
2025-07-26 14:43:41.255 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 13.995 seconds (JVM running for 15.067)
2025-07-26 14:43:41.260 [main] INFO  org.jeecg.JeecgSystemApplication:39 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------
2025-07-26 14:43:41.387 [RMI TCP Connection(2)-**********] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 14:43:41.387 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-26 14:43:41.398 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 11 ms
2025-07-26 14:44:03.171 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-26 14:44:03.172 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:44:03.222 [http-nio-8080-exec-7] INFO  o.j.m.d.u.controller.UserCenterDataController:765 - 生成推荐链接成功 - 用户: admin, 链接: http://localhost:8080?ref=e9ca23d68d884d4ebb19d07889727dae
2025-07-26 14:44:03.303 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-26 14:44:03.304 [http-nio-8080-exec-7] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:44:09.701 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-26 14:44:09.703 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:44:09.771 [http-nio-8080-exec-4] INFO  o.j.m.d.u.controller.UserCenterDataController:765 - 生成推荐链接成功 - 用户: admin, 链接: http://localhost:8080?ref=e9ca23d68d884d4ebb19d07889727dae
2025-07-26 14:44:09.772 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-26 14:44:09.774 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:44:12.627 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-26 14:44:12.628 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:44:12.721 [http-nio-8080-exec-5] INFO  o.j.m.d.u.controller.UserCenterDataController:765 - 生成推荐链接成功 - 用户: admin, 链接: http://localhost:8080?ref=e9ca23d68d884d4ebb19d07889727dae
2025-07-26 14:44:12.722 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-26 14:44:12.723 [http-nio-8080-exec-5] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:51:22.515 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:218 - Shutting down ExecutorService 'jmReportTaskExecutor'
2025-07-26 14:51:22.519 [SpringContextShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:845 - Shutting down Quartz Scheduler
2025-07-26 14:51:22.519 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753512216093 shutting down.
2025-07-26 14:51:22.519 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753512216093 paused.
2025-07-26 14:51:22.521 [SpringContextShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_DESKTOP-G0NDD8J1753512216093 shutdown complete.
2025-07-26 14:51:22.529 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-07-26 14:51:22.534 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-07-26 14:51:22.543 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-07-26 14:51:22.543 [SpringContextShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-07-26 14:51:31.975 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-26 14:51:32.008 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 21516 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-26 14:51:32.008 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-26 14:51:32.403 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-26 14:51:33.532 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 14:51:33.534 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 14:51:33.667 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 123ms. Found 0 Redis repository interfaces.
2025-07-26 14:51:33.795 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-26 14:51:33.795 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-26 14:51:33.796 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-26 14:51:33.865 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-26 14:51:33.865 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-26 14:51:33.866 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-26 14:51:33.866 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-26 14:51:33.866 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-26 14:51:33.866 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-26 14:51:33.866 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-26 14:51:33.867 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-26 14:51:33.867 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-26 14:51:33.867 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-26 14:51:34.034 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.037 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.038 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.038 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.039 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.040 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.040 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.041 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.041 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.042 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.043 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.044 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.044 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.045 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.046 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.047 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.047 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.048 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.049 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.050 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.067 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.071 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.125 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.177 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.179 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$dac4cbd0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.216 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.573 [main] INFO  org.jeecg.config.shiro.ShiroConfig:218 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-26 14:51:34.575 [main] INFO  org.jeecg.config.shiro.ShiroConfig:236 - ===============(2)创建RedisManager,连接Redis..
2025-07-26 14:51:34.578 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.581 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.612 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.763 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.769 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$e516f4a8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.776 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.788 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$f826dc00] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.831 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$8ea16187] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:34.834 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 14:51:35.070 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-26 14:51:35.077 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-26 14:51:35.077 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-26 14:51:35.078 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-26 14:51:35.191 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-26 14:51:35.191 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3139 ms
2025-07-26 14:51:35.705 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-26 14:51:35.706 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-26 14:51:35.707 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-26 14:51:36.954 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:76 -  --- redis config init --- 
2025-07-26 14:51:37.769 [main] INFO  org.jeecg.modules.jianying.service.TosService:86 - 开始初始化TOS客户端...
2025-07-26 14:51:37.770 [main] INFO  org.jeecg.modules.jianying.service.TosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-26 14:51:37.870 [main] INFO  org.jeecg.modules.jianying.service.TosService:115 - 外网TOS客户端初始化成功！
2025-07-26 14:51:37.873 [main] INFO  org.jeecg.modules.jianying.service.TosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-26 14:51:37.873 [main] INFO  org.jeecg.modules.jianying.service.TosService:591 - 正在测试TOS连接...
2025-07-26 14:51:37.873 [main] INFO  org.jeecg.modules.jianying.service.TosService:593 - TOS连接测试成功！
2025-07-26 14:51:38.589 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:86 - 开始初始化TOS客户端...
2025-07-26 14:51:38.590 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-26 14:51:38.591 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:115 - 外网TOS客户端初始化成功！
2025-07-26 14:51:38.593 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-26 14:51:38.593 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:627 - 正在测试TOS连接...
2025-07-26 14:51:38.593 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:629 - TOS连接测试成功！
2025-07-26 14:51:38.910 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:48 - 🔍 开始初始化敏感词库...
2025-07-26 14:51:39.149 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:64 - ✅ 敏感词库初始化完成
2025-07-26 14:51:39.438 [main] INFO  o.j.m.jianying.service.JianyingEffectSearchService:86 - 初始化预定义特效映射完成，共 4 个特效
2025-07-26 14:51:39.444 [main] INFO  org.jeecg.modules.jianying.service.CozeApiService:64 - RestTemplate初始化完成，支持TOS文件下载
2025-07-26 14:51:39.472 [main] INFO  o.j.m.jianying.service.JianyingMaskSearchService:73 - 剪映蒙版搜索服务初始化完成
2025-07-26 14:51:39.501 [main] INFO  o.j.m.j.s.internal.JianyingProEffectSearchService:87 - 超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效
2025-07-26 14:51:39.506 [main] INFO  o.j.m.j.service.internal.JianyingProCozeApiService:59 - 超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载
2025-07-26 14:51:39.850 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-26 14:51:39.851 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-26 14:51:39.857 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-26 14:51:39.858 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-26 14:51:39.861 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-26 14:51:39.863 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-26 14:51:39.864 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-G0NDD8J1753512699851'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-26 14:51:39.864 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-26 14:51:39.864 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-26 14:51:39.864 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@404566e7
2025-07-26 14:51:41.443 [main] INFO  o.j.m.jmreport.config.JimuReportConfiguration:55 -  --- Init JimuReport Config --- 
2025-07-26 14:51:42.138 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-26 14:51:42.229 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  代码生成器数据库连接，使用application.yml的DB配置 ###################
2025-07-26 14:51:42.251 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-26 14:51:42.317 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:21 - ---创建线程池---
2025-07-26 14:51:42.318 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-26 14:51:42.320 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'jmReportTaskExecutor'
2025-07-26 14:51:42.938 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-07-26 14:51:42.958 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/jeecg-boot'
2025-07-26 14:51:42.958 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:93 - Documentation plugins bootstrapped
2025-07-26 14:51:42.962 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:79 - Found 1 custom documentation plugin(s)
2025-07-26 14:51:43.053 [main] INFO  s.d.spring.web.scanners.ApiListingReferenceScanner:44 - Scanning for api listing references
2025-07-26 14:51:43.185 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_1
2025-07-26 14:51:43.192 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_1
2025-07-26 14:51:43.197 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_1
2025-07-26 14:51:43.206 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_2
2025-07-26 14:51:43.207 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-26 14:51:43.208 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_1
2025-07-26 14:51:43.210 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_1
2025-07-26 14:51:43.211 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_1
2025-07-26 14:51:43.213 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_2
2025-07-26 14:51:43.216 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_2
2025-07-26 14:51:43.224 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_3
2025-07-26 14:51:43.225 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-26 14:51:43.226 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_2
2025-07-26 14:51:43.227 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_2
2025-07-26 14:51:43.227 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_2
2025-07-26 14:51:43.231 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_3
2025-07-26 14:51:43.234 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_3
2025-07-26 14:51:43.244 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_4
2025-07-26 14:51:43.245 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-26 14:51:43.246 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_3
2025-07-26 14:51:43.248 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_3
2025-07-26 14:51:43.251 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_4
2025-07-26 14:51:43.256 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_4
2025-07-26 14:51:43.263 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_5
2025-07-26 14:51:43.264 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-26 14:51:43.265 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_4
2025-07-26 14:51:43.266 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_4
2025-07-26 14:51:43.266 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_5
2025-07-26 14:51:43.271 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_5
2025-07-26 14:51:43.277 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_6
2025-07-26 14:51:43.278 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-26 14:51:43.281 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_5
2025-07-26 14:51:43.283 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_5
2025-07-26 14:51:43.287 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_6
2025-07-26 14:51:43.289 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_6
2025-07-26 14:51:43.297 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_7
2025-07-26 14:51:43.300 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-26 14:51:43.301 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_6
2025-07-26 14:51:43.302 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_6
2025-07-26 14:51:43.303 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByReferrerIdUsingGET_1
2025-07-26 14:51:43.304 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_7
2025-07-26 14:51:43.307 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_7
2025-07-26 14:51:43.315 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getUsageStatsUsingGET_1
2025-07-26 14:51:43.322 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_8
2025-07-26 14:51:43.323 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-26 14:51:43.323 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_7
2025-07-26 14:51:43.324 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_7
2025-07-26 14:51:43.326 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_8
2025-07-26 14:51:43.328 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_8
2025-07-26 14:51:43.331 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_9
2025-07-26 14:51:43.332 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-26 14:51:43.333 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_8
2025-07-26 14:51:43.334 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_8
2025-07-26 14:51:43.334 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_9
2025-07-26 14:51:43.337 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_9
2025-07-26 14:51:43.346 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_10
2025-07-26 14:51:43.348 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-26 14:51:43.349 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_9
2025-07-26 14:51:43.350 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_9
2025-07-26 14:51:43.352 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_10
2025-07-26 14:51:43.355 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_10
2025-07-26 14:51:43.363 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_11
2025-07-26 14:51:43.366 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-26 14:51:43.366 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_10
2025-07-26 14:51:43.367 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_10
2025-07-26 14:51:43.372 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_11
2025-07-26 14:51:43.374 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_11
2025-07-26 14:51:43.377 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_12
2025-07-26 14:51:43.379 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-26 14:51:43.380 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_11
2025-07-26 14:51:43.380 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_11
2025-07-26 14:51:43.381 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_12
2025-07-26 14:51:43.383 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_12
2025-07-26 14:51:43.387 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_13
2025-07-26 14:51:43.388 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-26 14:51:43.389 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_12
2025-07-26 14:51:43.390 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_12
2025-07-26 14:51:43.390 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_13
2025-07-26 14:51:43.394 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_13
2025-07-26 14:51:43.398 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_14
2025-07-26 14:51:43.399 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_13
2025-07-26 14:51:43.400 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_13
2025-07-26 14:51:43.401 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_13
2025-07-26 14:51:43.401 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_14
2025-07-26 14:51:43.405 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_14
2025-07-26 14:51:43.409 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_15
2025-07-26 14:51:43.410 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_14
2025-07-26 14:51:43.411 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_14
2025-07-26 14:51:43.412 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_14
2025-07-26 14:51:43.412 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_15
2025-07-26 14:51:43.415 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_15
2025-07-26 14:51:43.490 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addAudiosUsingPOST_1
2025-07-26 14:51:43.493 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addCaptionsUsingPOST_1
2025-07-26 14:51:43.494 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addEffectsUsingPOST_1
2025-07-26 14:51:43.497 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addImagesUsingPOST_1
2025-07-26 14:51:43.499 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addKeyframesUsingPOST_1
2025-07-26 14:51:43.506 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addVideosUsingPOST_1
2025-07-26 14:51:43.538 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_16
2025-07-26 14:51:43.539 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_15
2025-07-26 14:51:43.540 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_15
2025-07-26 14:51:43.541 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_15
2025-07-26 14:51:43.542 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_16
2025-07-26 14:51:43.544 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_16
2025-07-26 14:51:43.548 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_17
2025-07-26 14:51:43.549 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_16
2025-07-26 14:51:43.550 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_16
2025-07-26 14:51:43.550 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_16
2025-07-26 14:51:43.551 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_17
2025-07-26 14:51:43.553 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_17
2025-07-26 14:51:43.556 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_18
2025-07-26 14:51:43.556 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_17
2025-07-26 14:51:43.557 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_17
2025-07-26 14:51:43.558 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_17
2025-07-26 14:51:43.559 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_18
2025-07-26 14:51:43.563 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_18
2025-07-26 14:51:43.566 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_19
2025-07-26 14:51:43.567 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_18
2025-07-26 14:51:43.567 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_18
2025-07-26 14:51:43.569 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_18
2025-07-26 14:51:43.569 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_19
2025-07-26 14:51:43.572 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_19
2025-07-26 14:51:43.577 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_20
2025-07-26 14:51:43.578 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_19
2025-07-26 14:51:43.578 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_19
2025-07-26 14:51:43.579 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_19
2025-07-26 14:51:43.579 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_20
2025-07-26 14:51:43.581 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_20
2025-07-26 14:51:43.587 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_21
2025-07-26 14:51:43.589 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_20
2025-07-26 14:51:43.593 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_21
2025-07-26 14:51:43.595 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_20
2025-07-26 14:51:43.596 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_20
2025-07-26 14:51:43.598 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_21
2025-07-26 14:51:43.605 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_22
2025-07-26 14:51:43.607 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_22
2025-07-26 14:51:43.609 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_21
2025-07-26 14:51:43.610 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_21
2025-07-26 14:51:43.611 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_21
2025-07-26 14:51:43.624 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_22
2025-07-26 14:51:43.647 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getReferralStatsUsingGET_1
2025-07-26 14:51:43.662 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendEmailCodeUsingPOST_1
2025-07-26 14:51:43.663 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendSmsCodeUsingPOST_1
2025-07-26 14:51:43.665 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: verifyCodeUsingPOST_1
2025-07-26 14:51:44.414 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 12.904 seconds (JVM running for 13.818)
2025-07-26 14:51:44.420 [main] INFO  org.jeecg.JeecgSystemApplication:39 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------
2025-07-26 14:51:44.604 [RMI TCP Connection(2)-**********] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 14:51:44.604 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-26 14:51:44.616 [RMI TCP Connection(2)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 11 ms
2025-07-26 14:51:59.796 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-26 14:51:59.797 [http-nio-8080-exec-2] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 14:51:59.873 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:767 - 生成推荐链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=e9ca23d68d884d4ebb19d07889727dae
2025-07-26 14:51:59.940 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-26 14:51:59.940 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 15:06:04.987 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.1.6.Final
2025-07-26 15:06:05.020 [main] INFO  org.jeecg.JeecgSystemApplication:55 - Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 35240 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)
2025-07-26 15:06:05.021 [main] INFO  org.jeecg.JeecgSystemApplication:655 - The following profiles are active: dev
2025-07-26 15:06:05.459 [background-preinit] WARN  o.s.h.converter.json.Jackson2ObjectMapperBuilder:127 - For Jackson Kotlin classes support please add "com.fasterxml.jackson.module:jackson-module-kotlin" to the classpath
2025-07-26 15:06:06.643 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:249 - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-26 15:06:06.645 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:127 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-26 15:06:06.776 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:187 - Finished Spring Data repository scanning in 122ms. Found 0 Redis repository interfaces.
2025-07-26 15:06:06.892 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:23 -  ******************* init miniDao config [ begin ] *********************** 
2025-07-26 15:06:06.892 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:25 -  ------ minidao.base-package ------- org.jeecg.modules.jmreport.*
2025-07-26 15:06:06.893 [main] INFO  o.j.minidao.auto.MinidaoAutoConfiguration:42 -  *******************  init miniDao config  [ end ] *********************** 
2025-07-26 15:06:06.982 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }
2025-07-26 15:06:06.983 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }
2025-07-26 15:06:06.983 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }
2025-07-26 15:06:06.983 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }
2025-07-26 15:06:06.983 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }
2025-07-26 15:06:06.983 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }
2025-07-26 15:06:06.985 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }
2025-07-26 15:06:06.985 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }
2025-07-26 15:06:06.985 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }
2025-07-26 15:06:06.985 [main] INFO  o.j.minidao.factory.MiniDaoClassPathMapperScanner:48 - register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }
2025-07-26 15:06:07.148 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.151 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.152 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#1' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.153 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDataSourceDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.153 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#2' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.154 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.155 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#3' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.155 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbFieldDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.156 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#4' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.157 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDbParamDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.158 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#5' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.160 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.160 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#6' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.161 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportDictItemDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.161 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#7' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.162 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportLinkDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.162 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#8' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.163 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportMapDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.163 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean '(inner bean)#5fef0c19#9' of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.164 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'jimuReportShareDao' of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.181 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.184 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.237 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.289 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.291 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$dac4cbd0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.327 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.669 [main] INFO  org.jeecg.config.shiro.ShiroConfig:218 - ===============(1)创建缓存管理器RedisCacheManager
2025-07-26 15:06:07.670 [main] INFO  org.jeecg.config.shiro.ShiroConfig:236 - ===============(2)创建RedisManager,连接Redis..
2025-07-26 15:06:07.673 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.675 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.708 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.853 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.858 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$e516f4a8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.863 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.875 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$f826dc00] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.909 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$8ea16187] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:07.913 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:335 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-26 15:06:08.154 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-07-26 15:06:08.162 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-26 15:06:08.163 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-07-26 15:06:08.163 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.39]
2025-07-26 15:06:08.304 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring embedded WebApplicationContext
2025-07-26 15:06:08.304 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:285 - Root WebApplicationContext: initialization completed in 3232 ms
2025-07-26 15:06:08.857 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-07-26 15:06:08.858 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-07-26 15:06:08.858 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-26 15:06:10.161 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:76 -  --- redis config init --- 
2025-07-26 15:06:10.967 [main] INFO  org.jeecg.modules.jianying.service.TosService:86 - 开始初始化TOS客户端...
2025-07-26 15:06:10.967 [main] INFO  org.jeecg.modules.jianying.service.TosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-26 15:06:11.068 [main] INFO  org.jeecg.modules.jianying.service.TosService:115 - 外网TOS客户端初始化成功！
2025-07-26 15:06:11.071 [main] INFO  org.jeecg.modules.jianying.service.TosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-26 15:06:11.072 [main] INFO  org.jeecg.modules.jianying.service.TosService:591 - 正在测试TOS连接...
2025-07-26 15:06:11.073 [main] INFO  org.jeecg.modules.jianying.service.TosService:593 - TOS连接测试成功！
2025-07-26 15:06:11.863 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:86 - 开始初始化TOS客户端...
2025-07-26 15:06:11.863 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:105 - TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos
2025-07-26 15:06:11.865 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:115 - 外网TOS客户端初始化成功！
2025-07-26 15:06:11.866 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:125 - 内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com
2025-07-26 15:06:11.866 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:627 - 正在测试TOS连接...
2025-07-26 15:06:11.866 [main] INFO  o.j.m.j.service.internal.JianyingProTosService:629 - TOS连接测试成功！
2025-07-26 15:06:12.203 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:48 - 🔍 开始初始化敏感词库...
2025-07-26 15:06:12.446 [main] INFO  o.j.m.system.service.impl.SensitiveWordServiceImpl:64 - ✅ 敏感词库初始化完成
2025-07-26 15:06:12.755 [main] INFO  o.j.m.jianying.service.JianyingEffectSearchService:86 - 初始化预定义特效映射完成，共 4 个特效
2025-07-26 15:06:12.763 [main] INFO  org.jeecg.modules.jianying.service.CozeApiService:64 - RestTemplate初始化完成，支持TOS文件下载
2025-07-26 15:06:12.787 [main] INFO  o.j.m.jianying.service.JianyingMaskSearchService:73 - 剪映蒙版搜索服务初始化完成
2025-07-26 15:06:12.817 [main] INFO  o.j.m.j.s.internal.JianyingProEffectSearchService:87 - 超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效
2025-07-26 15:06:12.823 [main] INFO  o.j.m.j.service.internal.JianyingProCozeApiService:59 - 超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载
2025-07-26 15:06:13.176 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-07-26 15:06:13.178 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-07-26 15:06:13.187 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-26 15:06:13.187 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-07-26 15:06:13.190 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-07-26 15:06:13.191 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-07-26 15:06:13.192 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-G0NDD8J1753513573178'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-07-26 15:06:13.192 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-07-26 15:06:13.192 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-07-26 15:06:13.192 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@59cb10e0
2025-07-26 15:06:14.816 [main] INFO  o.j.m.jmreport.config.JimuReportConfiguration:55 -  --- Init JimuReport Config --- 
2025-07-26 15:06:15.490 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-07-26 15:06:15.578 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  代码生成器数据库连接，使用application.yml的DB配置 ###################
2025-07-26 15:06:15.598 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping:69 - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-07-26 15:06:15.653 [main] INFO  o.j.modules.jmreport.config.JmReportExecutorConfig:21 - ---创建线程池---
2025-07-26 15:06:15.654 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService
2025-07-26 15:06:15.655 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor:181 - Initializing ExecutorService 'jmReportTaskExecutor'
2025-07-26 15:06:16.342 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-07-26 15:06:16.363 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/jeecg-boot'
2025-07-26 15:06:16.364 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:93 - Documentation plugins bootstrapped
2025-07-26 15:06:16.367 [main] INFO  s.d.s.web.plugins.DocumentationPluginsBootstrapper:79 - Found 1 custom documentation plugin(s)
2025-07-26 15:06:16.465 [main] INFO  s.d.spring.web.scanners.ApiListingReferenceScanner:44 - Scanning for api listing references
2025-07-26 15:06:16.610 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_1
2025-07-26 15:06:16.616 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_1
2025-07-26 15:06:16.619 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_1
2025-07-26 15:06:16.628 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_2
2025-07-26 15:06:16.630 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_1
2025-07-26 15:06:16.631 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_1
2025-07-26 15:06:16.632 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_1
2025-07-26 15:06:16.633 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_1
2025-07-26 15:06:16.635 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_2
2025-07-26 15:06:16.638 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_2
2025-07-26 15:06:16.645 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_3
2025-07-26 15:06:16.647 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_2
2025-07-26 15:06:16.647 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_2
2025-07-26 15:06:16.649 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_2
2025-07-26 15:06:16.650 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByUserIdUsingGET_2
2025-07-26 15:06:16.654 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_3
2025-07-26 15:06:16.658 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_3
2025-07-26 15:06:16.667 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_4
2025-07-26 15:06:16.669 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_3
2025-07-26 15:06:16.669 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_3
2025-07-26 15:06:16.671 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_3
2025-07-26 15:06:16.673 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_4
2025-07-26 15:06:16.678 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_4
2025-07-26 15:06:16.686 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_5
2025-07-26 15:06:16.687 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_4
2025-07-26 15:06:16.687 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_4
2025-07-26 15:06:16.688 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_4
2025-07-26 15:06:16.689 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_5
2025-07-26 15:06:16.694 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_5
2025-07-26 15:06:16.699 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_6
2025-07-26 15:06:16.700 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_5
2025-07-26 15:06:16.701 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_5
2025-07-26 15:06:16.701 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_5
2025-07-26 15:06:16.706 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_6
2025-07-26 15:06:16.710 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_6
2025-07-26 15:06:16.715 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_7
2025-07-26 15:06:16.717 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_6
2025-07-26 15:06:16.718 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_6
2025-07-26 15:06:16.718 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_6
2025-07-26 15:06:16.719 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getByReferrerIdUsingGET_1
2025-07-26 15:06:16.721 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_7
2025-07-26 15:06:16.724 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_7
2025-07-26 15:06:16.731 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getUsageStatsUsingGET_1
2025-07-26 15:06:16.740 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_8
2025-07-26 15:06:16.740 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_7
2025-07-26 15:06:16.741 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_7
2025-07-26 15:06:16.742 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_7
2025-07-26 15:06:16.742 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_8
2025-07-26 15:06:16.745 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_8
2025-07-26 15:06:16.748 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_9
2025-07-26 15:06:16.749 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_8
2025-07-26 15:06:16.750 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_8
2025-07-26 15:06:16.751 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_8
2025-07-26 15:06:16.752 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_9
2025-07-26 15:06:16.755 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_9
2025-07-26 15:06:16.763 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_10
2025-07-26 15:06:16.766 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_9
2025-07-26 15:06:16.766 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_9
2025-07-26 15:06:16.767 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_9
2025-07-26 15:06:16.770 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_10
2025-07-26 15:06:16.773 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_10
2025-07-26 15:06:16.782 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_11
2025-07-26 15:06:16.787 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_10
2025-07-26 15:06:16.787 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_10
2025-07-26 15:06:16.788 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_10
2025-07-26 15:06:16.792 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_11
2025-07-26 15:06:16.796 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_11
2025-07-26 15:06:16.799 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_12
2025-07-26 15:06:16.800 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_11
2025-07-26 15:06:16.801 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_11
2025-07-26 15:06:16.801 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_11
2025-07-26 15:06:16.802 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_12
2025-07-26 15:06:16.804 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_12
2025-07-26 15:06:16.809 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_13
2025-07-26 15:06:16.809 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_12
2025-07-26 15:06:16.810 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_12
2025-07-26 15:06:16.812 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_12
2025-07-26 15:06:16.814 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_13
2025-07-26 15:06:16.818 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_13
2025-07-26 15:06:16.823 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_14
2025-07-26 15:06:16.824 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_13
2025-07-26 15:06:16.825 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_13
2025-07-26 15:06:16.826 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_13
2025-07-26 15:06:16.828 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_14
2025-07-26 15:06:16.831 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_14
2025-07-26 15:06:16.836 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_15
2025-07-26 15:06:16.837 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_14
2025-07-26 15:06:16.838 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_14
2025-07-26 15:06:16.839 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_14
2025-07-26 15:06:16.840 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_15
2025-07-26 15:06:16.843 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_15
2025-07-26 15:06:16.928 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addAudiosUsingPOST_1
2025-07-26 15:06:16.932 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addCaptionsUsingPOST_1
2025-07-26 15:06:16.935 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addEffectsUsingPOST_1
2025-07-26 15:06:16.937 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addImagesUsingPOST_1
2025-07-26 15:06:16.939 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addKeyframesUsingPOST_1
2025-07-26 15:06:16.947 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addVideosUsingPOST_1
2025-07-26 15:06:16.985 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_16
2025-07-26 15:06:16.986 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_15
2025-07-26 15:06:16.987 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_15
2025-07-26 15:06:16.988 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_15
2025-07-26 15:06:16.988 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_16
2025-07-26 15:06:16.990 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_16
2025-07-26 15:06:16.993 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_17
2025-07-26 15:06:16.994 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_16
2025-07-26 15:06:16.995 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_16
2025-07-26 15:06:16.996 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_16
2025-07-26 15:06:16.997 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_17
2025-07-26 15:06:17.000 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_17
2025-07-26 15:06:17.002 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_18
2025-07-26 15:06:17.004 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_17
2025-07-26 15:06:17.005 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_17
2025-07-26 15:06:17.005 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_17
2025-07-26 15:06:17.006 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_18
2025-07-26 15:06:17.008 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_18
2025-07-26 15:06:17.011 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_19
2025-07-26 15:06:17.012 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_18
2025-07-26 15:06:17.012 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_18
2025-07-26 15:06:17.013 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_18
2025-07-26 15:06:17.014 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_19
2025-07-26 15:06:17.016 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_19
2025-07-26 15:06:17.021 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_20
2025-07-26 15:06:17.022 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_19
2025-07-26 15:06:17.023 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_19
2025-07-26 15:06:17.024 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_19
2025-07-26 15:06:17.025 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_20
2025-07-26 15:06:17.028 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_20
2025-07-26 15:06:17.037 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_21
2025-07-26 15:06:17.038 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_20
2025-07-26 15:06:17.042 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_21
2025-07-26 15:06:17.045 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_20
2025-07-26 15:06:17.047 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_20
2025-07-26 15:06:17.050 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_21
2025-07-26 15:06:17.060 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryPageListUsingGET_22
2025-07-26 15:06:17.063 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: addUsingPOST_22
2025-07-26 15:06:17.064 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteUsingDELETE_21
2025-07-26 15:06:17.065 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: deleteBatchUsingDELETE_21
2025-07-26 15:06:17.067 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: editUsingPUT_21
2025-07-26 15:06:17.080 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: queryByIdUsingGET_22
2025-07-26 15:06:17.105 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: getReferralStatsUsingGET_1
2025-07-26 15:06:17.122 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendEmailCodeUsingPOST_1
2025-07-26 15:06:17.123 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: sendSmsCodeUsingPOST_1
2025-07-26 15:06:17.126 [main] INFO  s.d.s.w.r.operation.CachingOperationNameGenerator:41 - Generating unique operation named: verifyCodeUsingPOST_1
2025-07-26 15:06:17.890 [main] INFO  org.jeecg.JeecgSystemApplication:61 - Started JeecgSystemApplication in 13.413 seconds (JVM running for 14.426)
2025-07-26 15:06:17.895 [main] INFO  org.jeecg.JeecgSystemApplication:39 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------
2025-07-26 15:06:18.386 [RMI TCP Connection(3)-**********] INFO  o.a.c.c.C.[Tomcat].[localhost].[/jeecg-boot]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-26 15:06:18.386 [RMI TCP Connection(3)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-26 15:06:18.398 [RMI TCP Connection(3)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 12 ms
2025-07-26 15:07:08.259 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-26 15:07:08.259 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 15:07:08.397 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:767 - 为用户 admin 生成新的邀请码: 0BL6UKB8
2025-07-26 15:07:08.398 [http-nio-8080-exec-3] INFO  o.j.m.d.u.controller.UserCenterDataController:779 - 生成推荐链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=0BL6UKB8
2025-07-26 15:07:08.465 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-26 15:07:08.465 [http-nio-8080-exec-3] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 15:07:13.615 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-26 15:07:13.615 [http-nio-8080-exec-4] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 15:07:13.678 [http-nio-8080-exec-6] INFO  o.j.m.d.u.controller.UserCenterDataController:779 - 生成推荐链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=0BL6UKB8
2025-07-26 15:07:13.679 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-26 15:07:13.679 [http-nio-8080-exec-6] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 15:08:49.230 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 8
2025-07-26 15:08:49.231 [http-nio-8080-exec-9] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
2025-07-26 15:08:49.371 [http-nio-8080-exec-1] INFO  o.j.m.d.u.controller.UserCenterDataController:779 - 生成推荐链接成功 - 用户: admin, 链接: http://localhost:8080/login?ref=0BL6UKB8
2025-07-26 15:08:49.371 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:41 -  LogContent length : 6
2025-07-26 15:08:49.376 [http-nio-8080-exec-1] WARN  o.j.m.base.service.impl.BaseCommonServiceImpl:42 - Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog
