const { ipcMain } = require('electron');
const DraftDurationCalculator = require('../utils/draftDurationCalculator');
const DraftManager = require('./draftManager');
const path = require('path');

/**
 * Duration相关的IPC处理器
 * 提供给渲染进程调用的duration计算和修正功能
 */
class DurationIpcHandlers {
    /**
     * 注册所有duration相关的IPC处理器
     */
    static registerHandlers() {
        console.log('[DurationIpcHandlers] Registering duration IPC handlers...');
        
        // 修正单个草稿的duration
        ipcMain.handle('fix-draft-duration', async (event, draftPath) => {
            try {
                console.log(`[DurationIpcHandlers] Fixing single draft: ${draftPath}`);
                const wasFixed = await DraftDurationCalculator.fixDraftFile(draftPath);
                
                return {
                    success: true,
                    wasFixed,
                    message: wasFixed ? 'Duration fixed successfully' : 'Duration was already correct'
                };
            } catch (error) {
                console.error('[DurationIpcHand<PERSON>] Failed to fix draft duration:', error);
                return {
                    success: false,
                    error: error.message,
                    message: 'Failed to fix draft duration'
                };
            }
        });
        
        // 批量修正所有草稿的duration
        ipcMain.handle('fix-all-drafts', async (event, draftsDirectory) => {
            try {
                console.log(`[DurationIpcHandlers] Fixing all drafts in: ${draftsDirectory}`);
                const fixedCount = await DraftDurationCalculator.fixAllDraftsInDirectory(draftsDirectory);
                
                return {
                    success: true,
                    fixedCount,
                    message: `Fixed ${fixedCount} drafts successfully`
                };
            } catch (error) {
                console.error('[DurationIpcHandlers] Failed to fix all drafts:', error);
                return {
                    success: false,
                    error: error.message,
                    message: 'Failed to fix all drafts'
                };
            }
        });
        
        // 计算草稿的duration（不保存）
        ipcMain.handle('calculate-draft-duration', async (event, draftPath) => {
            try {
                console.log(`[DurationIpcHandlers] Calculating duration for: ${draftPath}`);
                
                const draftContent = await DraftManager.loadDraft(draftPath, false);
                const calculatedDuration = DraftDurationCalculator.calculateDuration(draftContent);
                const details = DraftDurationCalculator.getDurationDetails(draftContent);
                
                return {
                    success: true,
                    originalDuration: draftContent.duration || 0,
                    calculatedDuration,
                    needsFix: calculatedDuration !== (draftContent.duration || 0),
                    details,
                    message: 'Duration calculated successfully'
                };
            } catch (error) {
                console.error('[DurationIpcHandlers] Failed to calculate draft duration:', error);
                return {
                    success: false,
                    error: error.message,
                    message: 'Failed to calculate draft duration'
                };
            }
        });
        
        // 验证草稿文件
        ipcMain.handle('validate-draft', async (event, draftPath) => {
            try {
                console.log(`[DurationIpcHandlers] Validating draft: ${draftPath}`);
                const validation = await DraftManager.validateDraft(draftPath);
                
                return {
                    success: true,
                    validation,
                    message: 'Draft validation completed'
                };
            } catch (error) {
                console.error('[DurationIpcHandlers] Failed to validate draft:', error);
                return {
                    success: false,
                    error: error.message,
                    message: 'Failed to validate draft'
                };
            }
        });
        
        // 获取草稿列表
        ipcMain.handle('list-drafts', async (event, draftsDirectory) => {
            try {
                console.log(`[DurationIpcHandlers] Listing drafts in: ${draftsDirectory}`);
                const drafts = await DraftManager.listDrafts(draftsDirectory);
                
                return {
                    success: true,
                    drafts,
                    count: drafts.length,
                    message: `Found ${drafts.length} drafts`
                };
            } catch (error) {
                console.error('[DurationIpcHandlers] Failed to list drafts:', error);
                return {
                    success: false,
                    error: error.message,
                    message: 'Failed to list drafts'
                };
            }
        });
        
        // 保存草稿（自动修正duration）
        ipcMain.handle('save-draft-with-duration-fix', async (event, draftPath, draftContent) => {
            try {
                console.log(`[DurationIpcHandlers] Saving draft with duration fix: ${draftPath}`);
                const savedContent = await DraftManager.saveDraft(draftPath, draftContent);
                
                return {
                    success: true,
                    savedContent,
                    originalDuration: draftContent.duration || 0,
                    fixedDuration: savedContent.duration,
                    message: 'Draft saved with duration fix'
                };
            } catch (error) {
                console.error('[DurationIpcHandlers] Failed to save draft with duration fix:', error);
                return {
                    success: false,
                    error: error.message,
                    message: 'Failed to save draft with duration fix'
                };
            }
        });
        
        // 复制草稿（自动修正duration）
        ipcMain.handle('copy-draft-with-duration-fix', async (event, sourcePath, targetPath) => {
            try {
                console.log(`[DurationIpcHandlers] Copying draft with duration fix: ${sourcePath} -> ${targetPath}`);
                const copiedContent = await DraftManager.copyDraft(sourcePath, targetPath);
                
                return {
                    success: true,
                    copiedContent,
                    message: 'Draft copied with duration fix'
                };
            } catch (error) {
                console.error('[DurationIpcHandlers] Failed to copy draft with duration fix:', error);
                return {
                    success: false,
                    error: error.message,
                    message: 'Failed to copy draft with duration fix'
                };
            }
        });
        
        // 获取默认草稿目录路径
        ipcMain.handle('get-default-drafts-directory', async (event) => {
            try {
                // 根据项目结构返回默认草稿目录
                const defaultPath = path.join(__dirname, '../../coze-plugins/草稿');
                console.log(`[DurationIpcHandlers] Default drafts directory: ${defaultPath}`);
                
                return {
                    success: true,
                    path: defaultPath,
                    message: 'Default drafts directory retrieved'
                };
            } catch (error) {
                console.error('[DurationIpcHandlers] Failed to get default drafts directory:', error);
                return {
                    success: false,
                    error: error.message,
                    message: 'Failed to get default drafts directory'
                };
            }
        });
        
        console.log('[DurationIpcHandlers] All duration IPC handlers registered successfully');
    }
    
    /**
     * 注销所有duration相关的IPC处理器
     */
    static unregisterHandlers() {
        console.log('[DurationIpcHandlers] Unregistering duration IPC handlers...');
        
        const handlers = [
            'fix-draft-duration',
            'fix-all-drafts',
            'calculate-draft-duration',
            'validate-draft',
            'list-drafts',
            'save-draft-with-duration-fix',
            'copy-draft-with-duration-fix',
            'get-default-drafts-directory'
        ];
        
        handlers.forEach(handler => {
            ipcMain.removeHandler(handler);
        });
        
        console.log('[DurationIpcHandlers] All duration IPC handlers unregistered');
    }
}

module.exports = DurationIpcHandlers;
