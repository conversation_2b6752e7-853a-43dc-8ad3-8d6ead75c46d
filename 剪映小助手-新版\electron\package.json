{"name": "jianying-duration-calculator", "version": "1.0.0", "description": "剪映小助手 - Duration计算器，统一处理所有草稿的总时长计算", "main": "main/main.js", "scripts": {"start": "electron .", "dev": "NODE_ENV=development electron .", "test": "node test/testDurationCalculator.js", "test-duration": "node test/testDurationCalculator.js", "fix-all-drafts": "node -e \"const calc = require('./utils/draftDurationCalculator'); calc.fixAllDraftsInDirectory('./coze-plugins/草稿').then(count => console.log('Fixed', count, 'drafts'));\"", "build": "electron-builder", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "keywords": ["jianying", "video-editor", "duration-calculator", "electron", "draft-manager"], "author": "剪映小助手团队", "license": "MIT", "devDependencies": {"electron": "^22.0.0", "electron-builder": "^24.0.0"}, "dependencies": {"fs-extra": "^11.0.0"}, "build": {"appId": "com.jianying.duration-calculator", "productName": "剪映Duration计算器", "directories": {"output": "dist"}, "files": ["main/**/*", "utils/**/*", "renderer/**/*", "assets/**/*", "package.json"], "mac": {"category": "public.app-category.video", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "repository": {"type": "git", "url": "https://github.com/your-repo/jianying-duration-calculator.git"}, "bugs": {"url": "https://github.com/your-repo/jianying-duration-calculator/issues"}, "homepage": "https://github.com/your-repo/jianying-duration-calculator#readme"}