<template>
  <div class="referral-page">
    <div class="page-header">
      <h1 class="page-title">推荐奖励</h1>
      <p class="page-description">邀请好友注册，获得丰厚奖励</p>
    </div>

    <div class="referral-content">
      <!-- 推荐统计 -->
      <div class="referral-stats">
        <StatsCard
          :value="referralStats.totalReferrals"
          label="推荐人数"
          icon="anticon anticon-team"
          icon-color="#7c8aed"
          :loading="loading"
        />
        
        <StatsCard
          :value="referralStats.totalRewards"
          unit="元"
          label="累计奖励"
          icon="anticon anticon-gift"
          icon-color="#10b981"
          :loading="loading"
        />
        
        <StatsCard
          :value="referralStats.availableRewards"
          unit="元"
          label="可提现金额"
          icon="anticon anticon-wallet"
          icon-color="#f59e0b"
          :loading="loading"
        />
        
        <StatsCard
          :value="referralStats.monthlyReferrals"
          label="本月推荐"
          icon="anticon anticon-calendar"
          icon-color="#ef4444"
          :trend="monthlyTrend"
          :loading="loading"
        />
      </div>

      <!-- 推荐链接生成 -->
      <div class="referral-link-section">
        <h3 class="section-title">我的推荐链接</h3>
        <div class="link-generator">
          <div class="link-display">
            <div class="link-input">
              <a-input
                :value="referralLink"
                readonly
                size="large"
                placeholder="点击生成推荐链接"
              />
              <a-button 
                type="primary" 
                size="large"
                :loading="linkLoading"
                @click="handleCopyLink"
              >
                <i class="anticon anticon-copy"></i>
                复制链接
              </a-button>
            </div>
            
            <div class="link-actions">
              <a-button @click="handleGenerateLink" :loading="linkLoading">
                <i class="anticon anticon-reload"></i>
                重新生成
              </a-button>
              <a-button @click="handleGenerateQRCode">
                <i class="anticon anticon-qrcode"></i>
                生成二维码
              </a-button>
              <a-button @click="handleShareToSocial">
                <i class="anticon anticon-share-alt"></i>
                分享到社交媒体
              </a-button>
            </div>
          </div>
          
          <div class="referral-tips">
            <h4>推荐奖励规则</h4>
            <ul>
              <li>好友通过您的链接注册并订阅会员，您可获得其订阅金额的 <strong>{{ currentCommissionRate }}%</strong> 佣金</li>
              <li>普通用户：{{ normalRate }}% 基础佣金，邀请10人升至{{ normalHighRate }}%，邀请30人升至{{ normalTopRate }}%</li>
              <li>VIP会员：{{ vipRate }}% 基础佣金，邀请10人升至{{ vipHighRate }}%，邀请30人升至{{ vipTopRate }}%</li>
              <li>SVIP会员：直接享受 {{ svipRate }}% 最高佣金</li>
              <li>佣金将在好友完成订阅后24小时内到账</li>
              <li>累计佣金满100元即可申请提现</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 推荐记录 -->
      <div class="referral-records">
        <div class="records-header">
          <h3 class="section-title">推荐记录</h3>
          <div class="records-filters">
            <a-select 
              v-model="recordFilters.status" 
              placeholder="状态筛选"
              style="width: 120px"
              @change="handleRecordFilterChange"
            >
              <a-select-option value="">全部状态</a-select-option>
              <a-select-option value="pending">待确认</a-select-option>
              <a-select-option value="confirmed">已确认</a-select-option>
              <a-select-option value="rewarded">已奖励</a-select-option>
            </a-select>
            
            <a-range-picker 
              v-model="recordFilters.dateRange"
              style="width: 240px"
              @change="handleRecordFilterChange"
            />
          </div>
        </div>
        
        <a-table
          :columns="recordColumns"
          :data-source="referralRecords"
          :loading="recordLoading"
          :pagination="recordPagination"
          row-key="id"
          @change="handleRecordTableChange"
        >
          <!-- 好友信息列 -->
          <template #friendInfo="{ record }">
            <div class="friend-info">
              <div class="friend-avatar">
                <img :src="record.friendAvatar || defaultAvatar" :alt="record.friendNickname" />
              </div>
              <div class="friend-details">
                <div class="friend-name">{{ record.friendNickname || '新用户' }}</div>
                <div class="friend-email">{{ maskEmail(record.friendEmail) }}</div>
              </div>
            </div>
          </template>
          
          <!-- 奖励金额列 -->
          <template #rewardAmount="{ text }">
            <span class="reward-amount">¥{{ formatNumber(text) }}</span>
          </template>
          
          <!-- 状态列 -->
          <template #status="{ text }">
            <span class="record-status" :class="getRecordStatusClass(text)">
              {{ getRecordStatusText(text) }}
            </span>
          </template>
          
          <!-- 时间列 -->
          <template #time="{ text }">
            <span class="record-time">{{ formatDateTime(text) }}</span>
          </template>
        </a-table>
      </div>

      <!-- 奖励提现 -->
      <div class="withdrawal-section">
        <h3 class="section-title">奖励提现</h3>
        <div class="withdrawal-card">
          <div class="withdrawal-info">
            <div class="balance-display">
              <div class="balance-label">可提现余额</div>
              <div class="balance-amount">¥{{ formatNumber(referralStats.availableRewards) }}</div>
            </div>
            
            <div class="withdrawal-rules">
              <h4>提现规则</h4>
              <ul>
                <li>最低提现金额：100元</li>
                <li>提现手续费：2%</li>
                <li>到账时间：1-3个工作日</li>
                <li>每月最多提现3次</li>
              </ul>
            </div>
          </div>
          
          <div class="withdrawal-form">
            <a-form layout="vertical">
              <a-form-item label="提现金额">
                <a-input-number
                  v-model="withdrawalAmount"
                  :min="100"
                  :max="referralStats.availableRewards"
                  :step="10"
                  placeholder="请输入提现金额"
                  size="large"
                  style="width: 100%"
                />
                <div class="amount-tips">
                  <span>最低100元</span>
                  <a @click="withdrawalAmount = referralStats.availableRewards">全部提现</a>
                </div>
              </a-form-item>
              
              <a-form-item label="提现方式">
                <a-radio-group v-model="withdrawalMethod">
                  <a-radio value="alipay">支付宝</a-radio>
                  <a-radio value="wechat">微信</a-radio>
                  <a-radio value="bank">银行卡</a-radio>
                </a-radio-group>
              </a-form-item>
              
              <a-form-item label="收款账户">
                <a-input
                  v-model="withdrawalAccount"
                  placeholder="请输入收款账户"
                  size="large"
                />
              </a-form-item>
              
              <div class="withdrawal-summary">
                <div class="summary-row">
                  <span>提现金额：</span>
                  <span>¥{{ formatNumber(withdrawalAmount) }}</span>
                </div>
                <div class="summary-row">
                  <span>手续费：</span>
                  <span>¥{{ formatNumber(withdrawalAmount * 0.02) }}</span>
                </div>
                <div class="summary-row total">
                  <span>实际到账：</span>
                  <span>¥{{ formatNumber(withdrawalAmount * 0.98) }}</span>
                </div>
              </div>
              
              <a-button 
                type="primary" 
                size="large"
                :disabled="!canWithdraw"
                :loading="withdrawalLoading"
                @click="handleWithdrawal"
                block
              >
                申请提现
              </a-button>
            </a-form>
          </div>
        </div>
      </div>

      <!-- 提现记录 -->
      <div class="withdrawal-history">
        <h3 class="section-title">提现记录</h3>
        <a-table
          :columns="withdrawalColumns"
          :data-source="withdrawalHistory"
          :loading="withdrawalHistoryLoading"
          :pagination="withdrawalPagination"
          row-key="id"
          @change="handleWithdrawalTableChange"
        >
          <!-- 金额列 -->
          <template #amount="{ record }">
            <div class="withdrawal-amount-info">
              <div class="amount">¥{{ formatNumber(record.amount) }}</div>
              <div class="fee">手续费: ¥{{ formatNumber(record.fee) }}</div>
            </div>
          </template>
          
          <!-- 状态列 -->
          <template #status="{ text }">
            <span class="withdrawal-status" :class="getWithdrawalStatusClass(text)">
              {{ getWithdrawalStatusText(text) }}
            </span>
          </template>
          
          <!-- 时间列 -->
          <template #time="{ text }">
            <span class="withdrawal-time">{{ formatDateTime(text) }}</span>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 二维码模态框 -->
    <a-modal
      v-model="showQRModal"
      title="推荐二维码"
      :footer="null"
      width="400px"
    >
      <div class="qr-modal">
        <div class="qr-code" ref="qrCodeContainer">
          <!-- 二维码将在这里生成 -->
        </div>
        <div class="qr-tips">
          <p>扫描二维码或分享链接给好友</p>
          <p>好友注册并充值后您将获得奖励</p>
        </div>
        <div class="qr-actions">
          <a-button @click="handleDownloadQR">下载二维码</a-button>
          <a-button type="primary" @click="handleCopyLink">复制链接</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import StatsCard from '../components/StatsCard.vue'
import {
  getReferralStats,
  generateReferralLink,
  getReferralList,
  requestWithdrawal,
  getWithdrawalHistory
} from '@/api/usercenter'

export default {
  name: 'UserCenterReferral',
  components: {
    StatsCard
  },
  data() {
    return {
      loading: true,
      linkLoading: false,
      recordLoading: false,
      withdrawalLoading: false,
      withdrawalHistoryLoading: false,
      
      defaultAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
      
      // 推荐统计
      referralStats: {
        totalReferrals: 0,
        totalRewards: 0,
        availableRewards: 0,
        monthlyReferrals: 0
      },
      
      // 佣金比例配置
      commissionConfig: {
        userType: 'NORMAL', // 当前用户类型
        commissionLevel: 1, // 当前佣金等级
        inviteCount: 0 // 有效邀请数量
      },
      
      // 佣金比例数据
      normalRate: 30,
      normalHighRate: 40,
      normalTopRate: 50,
      vipRate: 35,
      vipHighRate: 45,
      vipTopRate: 50,
      svipRate: 50,
      currentCommissionRate: 30,
      
      // 推荐链接
      referralLink: '',
      showQRModal: false,
      
      // 推荐记录
      recordFilters: {
        status: '',
        dateRange: []
      },
      referralRecords: [],
      recordPagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      recordColumns: [
        {
          title: '好友信息',
          key: 'friendInfo',
          width: 200,
          scopedSlots: { customRender: 'friendInfo' }
        },
        {
          title: '注册时间',
          dataIndex: 'registerTime',
          key: 'registerTime',
          width: 150,
          scopedSlots: { customRender: 'time' }
        },
        {
          title: '奖励金额',
          dataIndex: 'rewardAmount',
          key: 'rewardAmount',
          width: 120,
          align: 'right',
          scopedSlots: { customRender: 'rewardAmount' }
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 100,
          scopedSlots: { customRender: 'status' }
        }
      ],
      
      // 提现相关
      withdrawalAmount: 100,
      withdrawalMethod: 'alipay',
      withdrawalAccount: '',
      withdrawalHistory: [],
      withdrawalPagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      withdrawalColumns: [
        {
          title: '提现金额',
          key: 'amount',
          width: 150,
          scopedSlots: { customRender: 'amount' }
        },
        {
          title: '提现方式',
          dataIndex: 'method',
          key: 'method',
          width: 100
        },
        {
          title: '收款账户',
          dataIndex: 'account',
          key: 'account',
          ellipsis: true
        },
        {
          title: '申请时间',
          dataIndex: 'createTime',
          key: 'createTime',
          width: 150,
          scopedSlots: { customRender: 'time' }
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 100,
          scopedSlots: { customRender: 'status' }
        }
      ]
    }
  },
  computed: {
    monthlyTrend() {
      return {
        type: 'up',
        value: 20.5,
        text: '较上月增长20.5%'
      }
    },
    
    canWithdraw() {
      return this.withdrawalAmount >= 100 && 
             this.withdrawalAmount <= this.referralStats.availableRewards &&
             this.withdrawalAccount.trim() !== ''
    }
  },
  async mounted() {
    await this.loadData()
  },
  methods: {
    async loadData() {
      try {
        this.loading = true
        
        await Promise.all([
          this.loadReferralStats(),
          this.loadReferralLink(),
          this.loadReferralRecords(),
          this.loadWithdrawalHistory(),
          this.loadCommissionConfig()
        ])
      } catch (error) {
        console.error('加载推荐数据失败:', error)
        this.$message.error('加载数据失败，请刷新重试')
      } finally {
        this.loading = false
      }
    },
    
    async loadReferralStats() {
      try {
        const response = await getReferralStats()
        if (response.success) {
          // 修复：使用 result 字段而不是 data 字段
          this.referralStats = response.result || {}
        }
      } catch (error) {
        console.error('加载推荐统计失败:', error)
      }
    },
    
    async loadReferralLink() {
      try {
        // 如果已有链接则不重新生成
        if (!this.referralLink) {
          await this.handleGenerateLink()
        }
      } catch (error) {
        console.error('加载推荐链接失败:', error)
      }
    },
    
    async loadReferralRecords() {
      try {
        this.recordLoading = true
        
        const params = {
          current: this.recordPagination.current,
          size: this.recordPagination.pageSize,
          ...this.recordFilters
        }
        
        const response = await getReferralList(params)
        if (response.success) {
          // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法
          this.referralRecords = (response.result && response.result.records) || []
          this.recordPagination.total = (response.result && response.result.total) || 0
        }
      } catch (error) {
        console.error('加载推荐记录失败:', error)
        this.referralRecords = []
      } finally {
        this.recordLoading = false
      }
    },
    
    async loadCommissionConfig() {
      try {
        // 获取用户佣金配置信息
        const response = await this.$http.get('/api/user/commission-config')
        if (response.data && response.data.success) {
          this.commissionConfig = response.data.result || {}
          this.calculateCurrentCommissionRate()
        }
      } catch (error) {
        console.error('加载佣金配置失败:', error)
        // 使用默认配置
        this.commissionConfig = {
          userType: 'NORMAL',
          commissionLevel: 1,
          inviteCount: 0
        }
        this.calculateCurrentCommissionRate()
      }
    },
    
    calculateCurrentCommissionRate() {
      const { userType, inviteCount } = this.commissionConfig
      
      if (userType === 'SVIP') {
        this.currentCommissionRate = this.svipRate
      } else if (userType === 'VIP') {
        if (inviteCount >= 30) {
          this.currentCommissionRate = this.vipTopRate
        } else if (inviteCount >= 10) {
          this.currentCommissionRate = this.vipHighRate
        } else {
          this.currentCommissionRate = this.vipRate
        }
      } else {
        // NORMAL用户
        if (inviteCount >= 30) {
          this.currentCommissionRate = this.normalTopRate
        } else if (inviteCount >= 10) {
          this.currentCommissionRate = this.normalHighRate
        } else {
          this.currentCommissionRate = this.normalRate
        }
      }
    },
    
    async loadWithdrawalHistory() {
      try {
        this.withdrawalHistoryLoading = true
        
        const params = {
          current: this.withdrawalPagination.current,
          size: this.withdrawalPagination.pageSize
        }

        const response = await getWithdrawalHistory(params)
        if (response.success) {
          // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法
          this.withdrawalHistory = (response.result && response.result.records) || []
          this.withdrawalPagination.total = (response.result && response.result.total) || 0
        } else {
          this.withdrawalHistory = []
        }
      } catch (error) {
        console.error('加载提现记录失败:', error)
      } finally {
        this.withdrawalHistoryLoading = false
      }
    },
    
    // 推荐链接相关方法
    async handleGenerateLink() {
      try {
        this.linkLoading = true
        
        const response = await generateReferralLink()
        if (response.success) {
          // 修复：使用 result 字段而不是 data 字段，使用ES5兼容语法
          this.referralLink = (response.result && response.result.link) || ''
          this.$message.success('推荐链接生成成功')
        }
      } catch (error) {
        console.error('生成推荐链接失败:', error)
        this.$message.error('生成链接失败，请重试')
      } finally {
        this.linkLoading = false
      }
    },
    
    handleCopyLink() {
      if (!this.referralLink) {
        this.$message.warning('请先生成推荐链接')
        return
      }
      
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.referralLink).then(() => {
          this.$message.success('推荐链接已复制到剪贴板')
        }).catch(() => {
          this.fallbackCopyTextToClipboard(this.referralLink)
        })
      } else {
        this.fallbackCopyTextToClipboard(this.referralLink)
      }
    },
    
    fallbackCopyTextToClipboard(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      try {
        document.execCommand('copy')
        this.$message.success('推荐链接已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
      
      document.body.removeChild(textArea)
    },
    
    handleGenerateQRCode() {
      if (!this.referralLink) {
        this.$message.warning('请先生成推荐链接')
        return
      }
      
      this.showQRModal = true
      this.$nextTick(() => {
        this.renderQRCode()
      })
    },
    
    renderQRCode() {
      // TODO: 使用 QRCode.js 生成二维码
      console.log('生成二维码:', this.referralLink)
    },
    
    handleDownloadQR() {
      this.$message.info('下载二维码功能开发中...')
    },
    
    handleShareToSocial() {
      this.$message.info('分享到社交媒体功能开发中...')
    },
    
    // 提现相关方法
    async handleWithdrawal() {
      if (!this.canWithdraw) {
        this.$message.warning('请检查提现信息')
        return
      }
      
      try {
        this.withdrawalLoading = true
        
        const withdrawalData = {
          amount: this.withdrawalAmount,
          method: this.withdrawalMethod,
          account: this.withdrawalAccount
        }
        
        const response = await requestWithdrawal(withdrawalData)
        if (response.success) {
          this.$message.success('提现申请提交成功，请等待审核')
          
          // 重置表单
          this.withdrawalAmount = 100
          this.withdrawalAccount = ''
          
          // 重新加载数据
          await this.loadData()
        } else {
          this.$message.error(response.message || '提现申请失败')
        }
      } catch (error) {
        console.error('提现申请失败:', error)
        this.$message.error('提现申请失败，请重试')
      } finally {
        this.withdrawalLoading = false
      }
    },
    
    // 表格相关方法
    handleRecordFilterChange() {
      this.recordPagination.current = 1
      this.loadReferralRecords()
    },
    
    handleRecordTableChange({ pagination }) {
      this.recordPagination = { ...this.recordPagination, ...pagination }
      this.loadReferralRecords()
    },
    
    handleWithdrawalTableChange({ pagination }) {
      this.withdrawalPagination = { ...this.withdrawalPagination, ...pagination }
      this.loadWithdrawalHistory()
    },
    
    // 工具方法
    maskEmail(email) {
      if (!email) return ''
      
      const [username, domain] = email.split('@')
      if (username.length <= 3) {
        return `${username[0]}***@${domain}`
      }
      
      return `${username.substring(0, 3)}***@${domain}`
    },
    
    getRecordStatusClass(status) {
      const classMap = {
        pending: 'status-pending',
        confirmed: 'status-confirmed',
        rewarded: 'status-rewarded'
      }
      return classMap[status] || ''
    },
    
    getRecordStatusText(status) {
      const textMap = {
        pending: '待确认',
        confirmed: '已确认',
        rewarded: '已奖励'
      }
      return textMap[status] || '未知状态'
    },
    
    getWithdrawalStatusClass(status) {
      const classMap = {
        pending: 'status-pending',
        processing: 'status-processing',
        completed: 'status-completed',
        rejected: 'status-rejected'
      }
      return classMap[status] || ''
    },
    
    getWithdrawalStatusText(status) {
      const textMap = {
        pending: '待审核',
        processing: '处理中',
        completed: '已完成',
        rejected: '已拒绝'
      }
      return textMap[status] || '未知状态'
    },
    
    formatNumber(number) {
      if (!number) return '0.00'
      return parseFloat(number).toFixed(2)
    },
    
    formatDateTime(dateString) {
      if (!dateString) return '-'
      
      try {
        const date = new Date(dateString)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return '-'
      }
    }
  }
}
</script>

<style scoped>
.referral-page {
  padding: 2rem;
}

.page-header {
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: #334155;
  margin: 0 0 0.5rem 0;
}

.page-description {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

.referral-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #334155;
  margin: 0 0 2rem 0;
}

/* 推荐统计 */
.referral-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

/* 推荐链接生成 */
.referral-link-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 138, 237, 0.1);
}

.link-generator {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
}

.link-display {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.link-input {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.link-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.referral-tips {
  background: rgba(124, 138, 237, 0.05);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(124, 138, 237, 0.1);
}

.referral-tips h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #334155;
  margin: 0 0 1rem 0;
}

.referral-tips ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.referral-tips li {
  margin-bottom: 0.75rem;
  color: #64748b;
  line-height: 1.5;
  position: relative;
  padding-left: 1.5rem;
}

.referral-tips li::before {
  content: '•';
  color: #7c8aed;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.referral-tips strong {
  color: #7c8aed;
  font-weight: 600;
}

/* 推荐记录 */
.referral-records {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 138, 237, 0.1);
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.records-filters {
  display: flex;
  gap: 1rem;
}

.friend-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.friend-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.friend-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.friend-details {
  flex: 1;
}

.friend-name {
  font-weight: 600;
  color: #334155;
  margin-bottom: 0.25rem;
}

.friend-email {
  font-size: 0.9rem;
  color: #64748b;
  font-family: 'Courier New', monospace;
}

.reward-amount {
  font-weight: 600;
  color: #10b981;
  font-family: 'Courier New', monospace;
}

.record-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-pending {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.status-confirmed {
  background: rgba(124, 138, 237, 0.1);
  color: #7c8aed;
}

.status-rewarded {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.record-time {
  color: #64748b;
  font-size: 0.9rem;
}

/* 奖励提现 */
.withdrawal-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 138, 237, 0.1);
}

.withdrawal-card {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.withdrawal-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.balance-display {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, rgba(124, 138, 237, 0.1) 0%, rgba(139, 95, 191, 0.1) 100%);
  border-radius: 16px;
  border: 1px solid rgba(124, 138, 237, 0.1);
}

.balance-label {
  font-size: 1rem;
  color: #64748b;
  margin-bottom: 1rem;
}

.balance-amount {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #7c8aed 0%, #8b5fbf 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: 'Courier New', monospace;
}

.withdrawal-rules {
  background: rgba(124, 138, 237, 0.05);
  border-radius: 16px;
  padding: 1.5rem;
  border: 1px solid rgba(124, 138, 237, 0.1);
}

.withdrawal-rules h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #334155;
  margin: 0 0 1rem 0;
}

.withdrawal-rules ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.withdrawal-rules li {
  margin-bottom: 0.5rem;
  color: #64748b;
  font-size: 0.9rem;
  position: relative;
  padding-left: 1.5rem;
}

.withdrawal-rules li::before {
  content: '•';
  color: #7c8aed;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.withdrawal-form {
  background: rgba(124, 138, 237, 0.02);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(124, 138, 237, 0.1);
}

.amount-tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.amount-tips span {
  color: #64748b;
}

.amount-tips a {
  color: #7c8aed;
  cursor: pointer;
}

.amount-tips a:hover {
  text-decoration: underline;
}

.withdrawal-summary {
  background: rgba(124, 138, 237, 0.05);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-row.total {
  padding-top: 1rem;
  border-top: 1px solid rgba(124, 138, 237, 0.1);
  font-weight: 600;
  font-size: 1.1rem;
}

.summary-row span:first-child {
  color: #64748b;
}

.summary-row span:last-child {
  color: #334155;
  font-weight: 500;
}

/* 提现记录 */
.withdrawal-history {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 138, 237, 0.1);
}

.withdrawal-amount-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.withdrawal-amount-info .amount {
  font-weight: 600;
  color: #334155;
  font-family: 'Courier New', monospace;
}

.withdrawal-amount-info .fee {
  font-size: 0.8rem;
  color: #64748b;
}

.withdrawal-status {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-processing {
  background: rgba(124, 138, 237, 0.1);
  color: #7c8aed;
}

.status-completed {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-rejected {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.withdrawal-time {
  color: #64748b;
  font-size: 0.9rem;
}

/* 二维码模态框 */
.qr-modal {
  text-align: center;
  padding: 1rem 0;
}

.qr-code {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background: rgba(124, 138, 237, 0.05);
  border-radius: 12px;
  margin-bottom: 2rem;
  border: 1px solid rgba(124, 138, 237, 0.1);
}

.qr-tips {
  margin-bottom: 2rem;
}

.qr-tips p {
  color: #64748b;
  margin: 0.5rem 0;
}

.qr-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .referral-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .link-generator {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .withdrawal-card {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .referral-page {
    padding: 1rem;
  }

  .referral-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .link-input {
    flex-direction: column;
    gap: 0.75rem;
  }

  .link-actions {
    justify-content: center;
  }

  .records-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .records-filters {
    flex-direction: column;
    gap: 0.5rem;
  }

  .balance-amount {
    font-size: 2rem;
  }

  .withdrawal-form {
    padding: 1.5rem;
  }
}
</style>
