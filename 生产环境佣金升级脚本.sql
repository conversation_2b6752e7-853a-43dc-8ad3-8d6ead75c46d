-- =====================================================
-- 智界Aigc佣金功能数据库升级脚本 - 生产环境版
-- 版本: v1.0-PROD
-- 创建时间: 2025-07-26
-- 说明: 安全添加佣金相关字段，不影响现有数据
-- 执行方式: mysql -u root -p AigcView < 生产环境佣金升级脚本.sql
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 阶段一：安全检查并添加字段
-- =====================================================

-- 1. 检查并添加 commission_level 字段
SET @sql = '';
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND COLUMN_NAME = 'commission_level';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE aicg_user_profile ADD COLUMN commission_level TINYINT(1) DEFAULT 1 COMMENT "佣金等级：1-新手，2-高级，3-顶级";',
    'SELECT "commission_level字段已存在，跳过" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 检查并添加 valid_invite_count 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND COLUMN_NAME = 'valid_invite_count';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE aicg_user_profile ADD COLUMN valid_invite_count INT DEFAULT 0 COMMENT "有效邀请数量";',
    'SELECT "valid_invite_count字段已存在，跳过" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 检查并添加 total_commission 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND COLUMN_NAME = 'total_commission';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE aicg_user_profile ADD COLUMN total_commission DECIMAL(10,2) DEFAULT 0.00 COMMENT "累计佣金";',
    'SELECT "total_commission字段已存在，跳过" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 检查并添加 available_commission 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND COLUMN_NAME = 'available_commission';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE aicg_user_profile ADD COLUMN available_commission DECIMAL(10,2) DEFAULT 0.00 COMMENT "可提现佣金";',
    'SELECT "available_commission字段已存在，跳过" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 阶段二：添加索引（性能优化）
-- =====================================================

-- 检查并添加 commission_level 索引
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND INDEX_NAME = 'idx_commission_level';

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE aicg_user_profile ADD INDEX idx_commission_level (commission_level);',
    'SELECT "idx_commission_level索引已存在，跳过" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 阶段三：数据初始化（安全更新）
-- =====================================================

-- 为现有用户安全初始化佣金等级（仅更新NULL值）
UPDATE aicg_user_profile 
SET commission_level = 1 
WHERE commission_level IS NULL;

-- 为现有用户安全初始化有效邀请数（仅更新NULL值）
UPDATE aicg_user_profile 
SET valid_invite_count = 0 
WHERE valid_invite_count IS NULL;

-- 为现有用户安全初始化累计佣金（仅更新NULL值）
UPDATE aicg_user_profile 
SET total_commission = 0.00 
WHERE total_commission IS NULL;

-- 为现有用户安全初始化可用佣金（仅更新NULL值）
UPDATE aicg_user_profile 
SET available_commission = 0.00 
WHERE available_commission IS NULL;

-- =====================================================
-- 阶段四：验证升级结果
-- =====================================================

-- 验证字段是否添加成功
SELECT '=== 字段验证结果 ===' as info;
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许NULL',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND COLUMN_NAME IN ('commission_level', 'valid_invite_count', 'total_commission', 'available_commission')
ORDER BY ORDINAL_POSITION;

-- 验证数据初始化情况
SELECT '=== 数据初始化验证 ===' as info;
SELECT 
    COUNT(*) as '总用户数',
    COUNT(commission_level) as '有佣金等级用户数',
    COUNT(valid_invite_count) as '有邀请数用户数',
    AVG(commission_level) as '平均佣金等级'
FROM aicg_user_profile;

-- 查看示例数据
SELECT '=== 示例数据 ===' as info;
SELECT 
    user_id as '用户ID',
    username as '用户名',
    commission_level as '佣金等级',
    valid_invite_count as '有效邀请数',
    total_commission as '累计佣金',
    available_commission as '可用佣金'
FROM aicg_user_profile 
WHERE user_id IS NOT NULL
LIMIT 5;

-- 提交事务
COMMIT;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 升级完成提示
SELECT '=== 升级完成 ===' as info;
SELECT 
    '佣金功能数据库升级已完成' as '状态',
    NOW() as '完成时间',
    '请重启应用服务并测试功能' as '下一步操作';

-- =====================================================
-- 升级脚本执行完毕
-- 请按照以下步骤继续：
-- 1. 重启应用服务
-- 2. 测试佣金配置API: GET /sys/user/getCommissionConfig
-- 3. 验证前端页面显示
-- 4. 记录升级日志
-- =====================================================