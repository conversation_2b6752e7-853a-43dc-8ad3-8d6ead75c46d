-- =====================================================
-- 佣金功能升级后验证脚本
-- 用途: 验证生产环境升级是否成功
-- 执行方式: mysql -u root -p AigcView < 升级后验证脚本.sql
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;

SELECT '========================================' as '';
SELECT '佣金功能数据库升级验证报告' as '验证项目';
SELECT '========================================' as '';

-- 1. 验证表结构
SELECT '1. 表结构验证' as '验证项目';
SELECT 
    CASE 
        WHEN COUNT(*) = 4 THEN '✅ 通过 - 所有佣金字段已添加'
        ELSE CONCAT('❌ 失败 - 缺少字段，当前只有', COUNT(*), '个字段')
    END as '验证结果'
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND COLUMN_NAME IN ('commission_level', 'valid_invite_count', 'total_commission', 'available_commission');

-- 2. 验证字段详情
SELECT '2. 字段详情验证' as '验证项目';
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许NULL',
    COLUMN_DEFAULT as '默认值',
    CASE 
        WHEN COLUMN_NAME = 'commission_level' AND DATA_TYPE = 'tinyint' AND COLUMN_DEFAULT = '1' THEN '✅'
        WHEN COLUMN_NAME = 'valid_invite_count' AND DATA_TYPE = 'int' AND COLUMN_DEFAULT = '0' THEN '✅'
        WHEN COLUMN_NAME = 'total_commission' AND DATA_TYPE = 'decimal' AND COLUMN_DEFAULT = '0.00' THEN '✅'
        WHEN COLUMN_NAME = 'available_commission' AND DATA_TYPE = 'decimal' AND COLUMN_DEFAULT = '0.00' THEN '✅'
        ELSE '❌'
    END as '状态'
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND COLUMN_NAME IN ('commission_level', 'valid_invite_count', 'total_commission', 'available_commission')
ORDER BY 
    CASE COLUMN_NAME 
        WHEN 'commission_level' THEN 1
        WHEN 'valid_invite_count' THEN 2
        WHEN 'total_commission' THEN 3
        WHEN 'available_commission' THEN 4
    END;

-- 3. 验证索引
SELECT '3. 索引验证' as '验证项目';
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 通过 - commission_level索引已创建'
        ELSE '❌ 失败 - commission_level索引未创建'
    END as '验证结果'
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND INDEX_NAME = 'idx_commission_level';

-- 4. 验证数据初始化
SELECT '4. 数据初始化验证' as '验证项目';
SELECT 
    COUNT(*) as '总用户数',
    COUNT(commission_level) as '有佣金等级用户数',
    COUNT(valid_invite_count) as '有邀请数用户数',
    CASE 
        WHEN COUNT(*) = COUNT(commission_level) AND COUNT(*) = COUNT(valid_invite_count) THEN '✅ 通过'
        ELSE '❌ 失败 - 存在NULL值'
    END as '初始化状态'
FROM aicg_user_profile;

-- 5. 验证默认值设置
SELECT '5. 默认值验证' as '验证项目';
SELECT 
    '佣金等级为1的用户数' as '检查项',
    COUNT(*) as '数量',
    CASE WHEN COUNT(*) > 0 THEN '✅ 正常' ELSE '❌ 异常' END as '状态'
FROM aicg_user_profile 
WHERE commission_level = 1
UNION ALL
SELECT 
    '邀请数为0的用户数' as '检查项',
    COUNT(*) as '数量',
    CASE WHEN COUNT(*) > 0 THEN '✅ 正常' ELSE '❌ 异常' END as '状态'
FROM aicg_user_profile 
WHERE valid_invite_count = 0
UNION ALL
SELECT 
    '累计佣金为0的用户数' as '检查项',
    COUNT(*) as '数量',
    CASE WHEN COUNT(*) > 0 THEN '✅ 正常' ELSE '❌ 异常' END as '状态'
FROM aicg_user_profile 
WHERE total_commission = 0.00;

-- 6. 示例数据展示
SELECT '6. 示例数据展示' as '验证项目';
SELECT 
    user_id as '用户ID',
    username as '用户名',
    commission_level as '佣金等级',
    valid_invite_count as '有效邀请数',
    total_commission as '累计佣金',
    available_commission as '可用佣金',
    create_time as '创建时间'
FROM aicg_user_profile 
WHERE user_id IS NOT NULL
ORDER BY create_time DESC
LIMIT 3;

-- 7. 数据完整性检查
SELECT '7. 数据完整性检查' as '验证项目';
SELECT 
    'NULL值检查' as '检查项',
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 通过 - 无NULL值'
        ELSE CONCAT('❌ 失败 - 发现', COUNT(*), '个NULL值')
    END as '检查结果'
FROM aicg_user_profile 
WHERE commission_level IS NULL 
   OR valid_invite_count IS NULL 
   OR total_commission IS NULL 
   OR available_commission IS NULL;

-- 8. 总体验证结果
SELECT '========================================' as '';
SELECT '总体验证结果' as '验证项目';
SELECT 
    CASE 
        WHEN (
            -- 检查字段数量
            (SELECT COUNT(*) FROM information_schema.COLUMNS 
             WHERE TABLE_SCHEMA = DATABASE() 
             AND TABLE_NAME = 'aicg_user_profile' 
             AND COLUMN_NAME IN ('commission_level', 'valid_invite_count', 'total_commission', 'available_commission')) = 4
            AND
            -- 检查索引
            (SELECT COUNT(*) FROM information_schema.STATISTICS 
             WHERE TABLE_SCHEMA = DATABASE() 
             AND TABLE_NAME = 'aicg_user_profile' 
             AND INDEX_NAME = 'idx_commission_level') > 0
            AND
            -- 检查数据初始化
            (SELECT COUNT(*) FROM aicg_user_profile WHERE commission_level IS NULL OR valid_invite_count IS NULL) = 0
        ) THEN '🎉 升级成功！所有验证项目均通过'
        ELSE '⚠️  升级可能存在问题，请检查上述验证项目'
    END as '最终结果';

SELECT '========================================' as '';
SELECT CONCAT('验证完成时间: ', NOW()) as '验证信息';
SELECT '下一步: 重启应用服务并测试API功能' as '操作建议';
SELECT '========================================' as '';

-- =====================================================
-- 验证脚本执行完毕
-- 
-- 如果所有验证项目都显示 ✅，说明升级成功
-- 如果有 ❌ 项目，请检查具体问题并联系技术支持
-- 
-- 下一步操作：
-- 1. 重启应用服务
-- 2. 测试API: curl -X GET "http://your-domain/jeecg-boot/sys/user/getCommissionConfig"
-- 3. 检查前端页面显示
-- =====================================================