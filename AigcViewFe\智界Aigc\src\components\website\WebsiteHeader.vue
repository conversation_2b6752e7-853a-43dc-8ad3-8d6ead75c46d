<template>
  <nav class="website-navbar" :class="{ 'scrolled': isScrolled, 'transparent': isTransparent }" ref="navbar">
    <div class="nav-container">
      <div class="nav-brand" ref="navBrand" @click="goHome">
        <LogoImage
          size="medium"
          :hover="true"
          container-class="brand-logo-container"
          image-class="brand-logo-image"
          fallback-class="brand-logo-fallback"
        />
        <span class="brand-text">智界AIGC</span>
      </div>

      <div class="nav-menu" ref="navMenu">
        <component
          v-for="item in menuItems"
          :key="item.name"
          :is="item.path && item.path !== '' ? 'router-link' : 'span'"
          :to="item.path && item.path !== '' ? item.path : undefined"
          class="nav-link"
          :class="{
            'active': $route.path === item.path,
            'nav-link-disabled': !item.path || item.path === ''
          }"
          @click="(!item.path || item.path === '') ? handleDevelopingClick(item.name) : undefined"
        >
          <a-icon :type="item.icon" class="nav-icon" />
          <span class="nav-text">{{ item.name }}</span>
        </component>
      </div>

      <div class="nav-actions" ref="navActions">
        <button v-if="!isLoggedIn" class="btn-secondary" @click="handleLogin">登录</button>
        <button v-else-if="isAdmin" class="btn-admin" @click="goToAdmin">
          <a-icon type="dashboard" />
          后台管理
        </button>
      </div>

      <button class="mobile-menu-btn" @click="toggleMobileMenu" ref="mobileMenuBtn">
        <a-icon :type="mobileMenuOpen ? 'close' : 'menu'" />
      </button>
    </div>

    <!-- 移动端菜单 -->
    <div class="mobile-menu" :class="{ 'open': mobileMenuOpen }" ref="mobileMenu">
      <component
        v-for="item in menuItems"
        :key="item.name"
        :is="item.path && item.path !== '' ? 'router-link' : 'span'"
        :to="item.path && item.path !== '' ? item.path : undefined"
        class="mobile-nav-link"
        :class="{ 'mobile-nav-link-disabled': !item.path || item.path === '' }"
        @click="handleMobileMenuClick(item)"
      >
        <a-icon :type="item.icon" class="mobile-nav-icon" />
        <span class="mobile-nav-text">{{ item.name }}</span>
      </component>
      <div class="mobile-actions">
        <button v-if="!isLoggedIn" class="mobile-btn-login" @click="handleLogin">登录</button>
        <button v-else-if="isAdmin" class="mobile-btn-admin" @click="goToAdmin">
          <a-icon type="dashboard" />
          后台管理
        </button>
      </div>
    </div>
  </nav>
</template>

<script>
import { gsap } from 'gsap'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { isAdmin, getUserRole } from '@/utils/roleUtils'
import Vue from 'vue'
import LogoImage from '@/components/common/LogoImage.vue'

export default {
  name: 'WebsiteHeader',
  components: {
    LogoImage
  },
  props: {
    // 是否使用透明背景（首页专用）
    transparent: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isScrolled: false,
      mobileMenuOpen: false,
      menuItems: [],
      userInfo: {},
      isLoggedIn: false,
      isAdmin: false
    }
  },
  computed: {
    isTransparent() {
      return this.transparent && !this.isScrolled
    }
  },
  async mounted() {
    await this.loadMenuData()
    await this.checkUserStatus()
    this.initScrollListener()
    this.initNavbarAnimations()
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    async loadMenuData() {
      try {
        // TODO: 从API获取菜单数据
        // const response = await this.$http.get('/api/website/header/menu')
        // this.menuItems = response.data
        
        // 临时数据，后续替换为API调用
        this.menuItems = [
          { name: '首页', path: '/home', icon: 'home' },
          { name: '插件中心', path: '/market', icon: 'shop' },
          // { name: '客户案例', path: '/cases', icon: 'trophy' },
          // { name: '教程中心', path: '/tutorials', icon: 'book' },
          // { name: '签到奖励', path: '/signin', icon: 'gift' },
          // { name: '订阅会员', path: '/membership', icon: 'crown' },
          { name: '客户案例', path: '', icon: 'trophy' },
          { name: '教程中心', path: '', icon: 'book' },
          { name: '签到奖励', path: '', icon: 'gift' },
          { name: '订阅会员', path: '', icon: 'crown' },
          { name: '分销推广', path: '/affiliate', icon: 'team' },
          { name: '个人中心', path: '/usercenter', icon: 'user' }
        ]
      } catch (error) {
        console.error('加载菜单数据失败:', error)
        // 降级方案
        this.menuItems = [
          { name: '首页', path: '/', icon: 'home' },
          { name: '插件中心', path: '/market', icon: 'shop' },
          { name: '个人中心', path: '/usercenter', icon: 'user' }
        ]
      }
    },
    
    initScrollListener() {
      window.addEventListener('scroll', this.handleScroll)
    },
    
    handleScroll() {
      this.isScrolled = window.scrollY > 50
    },
    
    toggleMobileMenu() {
      this.mobileMenuOpen = !this.mobileMenuOpen
    },
    
    closeMobileMenu() {
      this.mobileMenuOpen = false
    },

    // 🔥 处理桌面端开发中功能点击
    handleDevelopingClick(featureName) {
      console.log('🎯 桌面端开发中功能点击:', featureName)
      this.$message.info(`${featureName}功能正在开发中，敬请期待！`, 3)
    },

    // 🔥 处理移动端菜单点击
    handleMobileMenuClick(item) {
      console.log('🎯 移动端菜单点击:', item.name, 'path:', item.path)
      if (!item.path || item.path === '') {
        console.log('🎯 移动端开发中功能点击:', item.name)
        this.$message.info(`${item.name}功能正在开发中，敬请期待！`, 3)
      } else {
        // 有效路径，进行跳转
        this.$router.push(item.path)
      }
      this.closeMobileMenu()
    },

    goHome() {
      this.$router.push('/')
    },
    
    handleLogin() {
      this.$router.push('/login')
    },

    async checkUserStatus() {
      try {
        // 检查是否有TOKEN
        const token = Vue.ls.get(ACCESS_TOKEN)
        if (!token) {
          this.isLoggedIn = false
          this.isAdmin = false
          return
        }

        this.isLoggedIn = true

        // 检查用户角色
        const userRole = await getUserRole()
        this.isAdmin = await isAdmin()

        // 获取用户信息
        this.userInfo = {
          username: this.$store.getters.username || '用户',
          role: userRole
        }

        console.log('🔍 WebsiteHeader用户状态:', {
          isLoggedIn: this.isLoggedIn,
          isAdmin: this.isAdmin,
          userInfo: this.userInfo
        })

      } catch (error) {
        console.error('检查用户状态失败:', error)
        this.isLoggedIn = false
        this.isAdmin = false
      }
    },

    goToAdmin() {
      // 跳转到后台管理首页
      this.$router.push('/dashboard/analysis')
      this.closeMobileMenu()
    },

    initNavbarAnimations() {
      // 导航栏入场动画 - 添加refs存在检查
      this.$nextTick(() => {
        const elements = [this.$refs.navBrand, this.$refs.navMenu, this.$refs.navActions].filter(el => el)
        if (elements.length > 0) {
          gsap.from(elements, {
            duration: 0.4,
            y: -20,
            opacity: 0,
            ease: "power2.out",
            stagger: 0.05
          })
        }
      })
    }
  }
}
</script>

<style scoped>
/* 官网导航栏样式 - 支持透明和普通两种模式 */
.website-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 1.75rem 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(25px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.15);
  box-shadow: 0 8px 40px rgba(59, 130, 246, 0.08), 0 2px 8px rgba(0, 0, 0, 0.04);
  /* 添加微妙的渐变边框 */
  border-image: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent) 1;
}

/* 透明模式（首页专用） */
.website-navbar.transparent {
  background: transparent;
  backdrop-filter: none;
  border-bottom: none;
  box-shadow: none;
}

.website-navbar.scrolled {
  background: rgba(255, 255, 255, 0.99);
  backdrop-filter: blur(30px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 12px 50px rgba(59, 130, 246, 0.12), 0 4px 16px rgba(0, 0, 0, 0.06);
  padding: 1.5rem 0;
  /* 滚动后增强效果 */
  border-image: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent) 1;
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 品牌Logo区域 */
.nav-brand {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  margin-right: 1rem;
  flex-shrink: 0;
  position: relative;
}

/* 透明模式下的品牌样式 */
.website-navbar.transparent .nav-brand {
  color: #ffffff;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.nav-brand:hover {
  transform: translateY(-2px);
}

/* Logo容器样式 */
.brand-logo-container {
  width: 48px;
  height: 48px;
  border-radius: 14px;
  overflow: hidden;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25), 0 2px 8px rgba(139, 92, 246, 0.15);
}

.brand-logo-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.brand-logo-image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  border-radius: 14px;
}

/* Fallback样式（当logo图片加载失败时） */
.brand-logo-fallback {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.4rem;
}

/* 悬停效果 */
.nav-brand:hover .brand-logo-container {
  transform: scale(1.08) rotate(8deg);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.35), 0 4px 12px rgba(139, 92, 246, 0.2);
}

.nav-brand:hover .brand-logo-container::before {
  left: 100%;
}

.brand-text {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.nav-brand:hover .brand-text {
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: translateY(-1px);
}

/* 透明模式下的品牌文字 */
.website-navbar.transparent .brand-text {
  color: #ffffff;
  background: none;
  -webkit-text-fill-color: #ffffff;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 导航菜单 */
.nav-menu {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: nowrap;
  flex: 1;
  justify-content: center;
}

.nav-link {
  color: rgba(30, 41, 59, 0.8);
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  padding: 0.875rem 1.125rem;
  border-radius: 12px;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  overflow: hidden;
}

/* 透明模式下的导航链接 */
.website-navbar.transparent .nav-link {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.6s ease;
  z-index: -1;
}

.nav-link:hover,
.nav-link.active {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.nav-link:hover::before {
  left: 100%;
}

/* 透明模式下的导航链接悬停 */
.website-navbar.transparent .nav-link:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 85%;
}

/* 导航图标和文字 */
.nav-icon {
  font-size: 0.9rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-text {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link:hover .nav-icon {
  transform: scale(1.1) rotate(5deg);
}

.nav-link:hover .nav-text {
  transform: translateX(2px);
}

.nav-link:hover .nav-icon,
.nav-link.active .nav-icon {
  color: #3b82f6;
  transform: scale(1.1);
}

.nav-link:hover .nav-text,
.nav-link.active .nav-text {
  color: #3b82f6;
}

/* 🔥 开发中菜单项样式 */
.nav-link-disabled {
  color: rgba(30, 41, 59, 0.6) !important;
  cursor: pointer;
}

.nav-link-disabled:hover {
  color: rgba(30, 41, 59, 0.8) !important;
  background: rgba(249, 115, 22, 0.1);
}

.website-navbar.transparent .nav-link-disabled {
  color: rgba(255, 255, 255, 0.7) !important;
}

.website-navbar.transparent .nav-link-disabled:hover {
  color: rgba(255, 255, 255, 0.9) !important;
  background: rgba(249, 115, 22, 0.2);
}

/* 透明模式下的图标和文字悬停 */
.website-navbar.transparent .nav-link:hover .nav-icon,
.website-navbar.transparent .nav-link:hover .nav-text {
  color: #ffffff;
}

/* 右侧操作区 */
.nav-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.btn-secondary,
.btn-admin {
  padding: 0.875rem 1.75rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.btn-secondary {
  background: transparent;
  border: 2px solid rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}

.btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.6s ease;
}

.btn-admin {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
}

/* 透明模式下的按钮 */
.website-navbar.transparent .btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  backdrop-filter: blur(10px);
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

.website-navbar.transparent .btn-admin {
  background: rgba(16, 185, 129, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-secondary:hover {
  background: rgba(59, 130, 246, 0.12);
  border-color: rgba(59, 130, 246, 0.6);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25);
}

.btn-secondary:hover::before {
  left: 100%;
}

.btn-admin:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.35);
}

/* 透明模式下的按钮悬停 */
.website-navbar.transparent .btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.website-navbar.transparent .btn-admin:hover {
  background: rgba(5, 150, 105, 0.95);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: #1e293b;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* 透明模式下的移动端按钮 */
.website-navbar.transparent .mobile-menu-btn {
  color: #ffffff;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

.mobile-menu-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

/* 透明模式下的移动端按钮悬停 */
.website-navbar.transparent .mobile-menu-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

/* 移动端菜单 */
.mobile-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  box-shadow: 0 4px 30px rgba(59, 130, 246, 0.12);
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  padding: 1rem 0;
}

.mobile-menu.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2rem;
  color: rgba(30, 41, 59, 0.8);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.mobile-nav-link:hover {
  background: rgba(59, 130, 246, 0.08);
  color: #3b82f6;
}

.mobile-nav-icon {
  font-size: 1.1rem;
}

/* 🔥 移动端开发中菜单项样式 */
.mobile-nav-link-disabled {
  color: rgba(30, 41, 59, 0.6) !important;
  cursor: pointer;
}

.mobile-nav-link-disabled:hover {
  background: rgba(249, 115, 22, 0.1) !important;
  color: rgba(30, 41, 59, 0.8) !important;
}

.mobile-actions {
  padding: 1rem 2rem;
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  margin-top: 1rem;
}

.mobile-btn-login,
.mobile-btn-admin {
  width: 100%;
  padding: 0.875rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.mobile-btn-login {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
}

.mobile-btn-admin {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.mobile-btn-login:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.mobile-btn-admin:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}



/* 响应式设计 */
@media (max-width: 1024px) {
  .nav-container {
    padding: 0 1.5rem;
  }
  
  .nav-menu {
    gap: 0.3rem;
  }
}

@media (max-width: 768px) {
  .nav-menu,
  .nav-actions {
    display: none;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
  
  .nav-container {
    padding: 0 1rem;
  }
}
</style>
