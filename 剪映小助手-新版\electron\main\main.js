const { app, BrowserWindow, Menu, dialog } = require('electron');
const path = require('path');
const DurationIpcHandlers = require('./durationIpcHandlers');
const DraftDurationCalculator = require('../utils/draftDurationCalculator');

/**
 * Electron主进程入口
 * 集成Duration计算功能
 */
class MainProcess {
    constructor() {
        this.mainWindow = null;
        this.isDev = process.env.NODE_ENV === 'development';
    }
    
    /**
     * 创建主窗口
     */
    createWindow() {
        console.log('[MainProcess] Creating main window...');
        
        this.mainWindow = new BrowserWindow({
            width: 1200,
            height: 800,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                enableRemoteModule: true
            },
            icon: path.join(__dirname, '../assets/icon.png'), // 如果有图标的话
            title: '剪映小助手 - Duration计算器'
        });
        
        // 加载页面
        if (this.isDev) {
            this.mainWindow.loadURL('http://localhost:3000');
            this.mainWindow.webContents.openDevTools();
        } else {
            this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
        }
        
        // 窗口关闭事件
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
        
        console.log('[MainProcess] Main window created successfully');
    }
    
    /**
     * 创建应用菜单（包含Duration调试工具）
     */
    createMenu() {
        console.log('[MainProcess] Creating application menu...');
        
        const template = [
            {
                label: '文件',
                submenu: [
                    {
                        label: '退出',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: 'Duration工具',
                submenu: [
                    {
                        label: '修正所有草稿Duration',
                        click: async () => {
                            await this.fixAllDraftsWithDialog();
                        }
                    },
                    {
                        label: '选择草稿文件修正',
                        click: async () => {
                            await this.fixSingleDraftWithDialog();
                        }
                    },
                    {
                        label: '验证草稿文件',
                        click: async () => {
                            await this.validateDraftWithDialog();
                        }
                    },
                    {
                        type: 'separator'
                    },
                    {
                        label: '打开草稿目录',
                        click: async () => {
                            await this.openDraftsDirectory();
                        }
                    }
                ]
            },
            {
                label: '帮助',
                submenu: [
                    {
                        label: '关于',
                        click: () => {
                            dialog.showMessageBox(this.mainWindow, {
                                type: 'info',
                                title: '关于',
                                message: '剪映小助手 - Duration计算器',
                                detail: '统一处理所有草稿的总时长计算，确保以最长轨道为准 + 2秒缓冲'
                            });
                        }
                    }
                ]
            }
        ];
        
        // macOS特殊处理
        if (process.platform === 'darwin') {
            template.unshift({
                label: app.getName(),
                submenu: [
                    { role: 'about' },
                    { type: 'separator' },
                    { role: 'services' },
                    { type: 'separator' },
                    { role: 'hide' },
                    { role: 'hideothers' },
                    { role: 'unhide' },
                    { type: 'separator' },
                    { role: 'quit' }
                ]
            });
        }
        
        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
        
        console.log('[MainProcess] Application menu created successfully');
    }
    
    /**
     * 修正所有草稿（带对话框）
     */
    async fixAllDraftsWithDialog() {
        try {
            console.log('[MainProcess] Starting fix all drafts with dialog...');
            
            // 选择草稿目录
            const result = await dialog.showOpenDialog(this.mainWindow, {
                title: '选择草稿目录',
                defaultPath: path.join(__dirname, '../../coze-plugins/草稿'),
                properties: ['openDirectory']
            });
            
            if (result.canceled || !result.filePaths.length) {
                return;
            }
            
            const draftsDirectory = result.filePaths[0];
            
            // 显示确认对话框
            const confirmResult = await dialog.showMessageBox(this.mainWindow, {
                type: 'question',
                title: '确认修正',
                message: `确定要修正目录下所有草稿的Duration吗？`,
                detail: `目录：${draftsDirectory}`,
                buttons: ['确定', '取消'],
                defaultId: 0,
                cancelId: 1
            });
            
            if (confirmResult.response !== 0) {
                return;
            }
            
            // 执行修正
            const fixedCount = await DraftDurationCalculator.fixAllDraftsInDirectory(draftsDirectory);
            
            // 显示结果
            await dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: '修正完成',
                message: `批量修正完成！`,
                detail: `共修正了 ${fixedCount} 个草稿文件`
            });
            
        } catch (error) {
            console.error('[MainProcess] Fix all drafts failed:', error);
            await dialog.showErrorBox('错误', `修正失败：${error.message}`);
        }
    }
    
    /**
     * 修正单个草稿（带对话框）
     */
    async fixSingleDraftWithDialog() {
        try {
            console.log('[MainProcess] Starting fix single draft with dialog...');
            
            // 选择草稿文件
            const result = await dialog.showOpenDialog(this.mainWindow, {
                title: '选择草稿文件',
                defaultPath: path.join(__dirname, '../../coze-plugins/草稿'),
                filters: [
                    { name: '草稿文件', extensions: ['json'] }
                ],
                properties: ['openFile']
            });
            
            if (result.canceled || !result.filePaths.length) {
                return;
            }
            
            const draftPath = result.filePaths[0];
            
            // 执行修正
            const wasFixed = await DraftDurationCalculator.fixDraftFile(draftPath);
            
            // 显示结果
            await dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: '修正完成',
                message: wasFixed ? '草稿Duration已修正！' : '草稿Duration无需修正',
                detail: `文件：${path.basename(draftPath)}`
            });
            
        } catch (error) {
            console.error('[MainProcess] Fix single draft failed:', error);
            await dialog.showErrorBox('错误', `修正失败：${error.message}`);
        }
    }
    
    /**
     * 验证草稿文件（带对话框）
     */
    async validateDraftWithDialog() {
        try {
            console.log('[MainProcess] Starting validate draft with dialog...');
            
            // 选择草稿文件
            const result = await dialog.showOpenDialog(this.mainWindow, {
                title: '选择要验证的草稿文件',
                defaultPath: path.join(__dirname, '../../coze-plugins/草稿'),
                filters: [
                    { name: '草稿文件', extensions: ['json'] }
                ],
                properties: ['openFile']
            });
            
            if (result.canceled || !result.filePaths.length) {
                return;
            }
            
            const draftPath = result.filePaths[0];
            const DraftManager = require('./draftManager');
            
            // 执行验证
            const validation = await DraftManager.validateDraft(draftPath);
            
            // 构建详细信息
            let detail = `文件：${path.basename(draftPath)}\n`;
            detail += `状态：${validation.isValid ? '有效' : '无效'}\n`;
            detail += `错误：${validation.errors.length} 个\n`;
            detail += `警告：${validation.warnings.length} 个\n`;
            
            if (validation.details) {
                detail += `\n时长信息：\n`;
                detail += `- 原始时长：${validation.details.originalDuration / 1000000} 秒\n`;
                detail += `- 计算时长：${validation.details.finalDuration / 1000000} 秒\n`;
                detail += `- 轨道数量：${validation.details.tracks.length} 个\n`;
            }
            
            if (validation.errors.length > 0) {
                detail += `\n错误详情：\n${validation.errors.join('\n')}`;
            }
            
            if (validation.warnings.length > 0) {
                detail += `\n警告详情：\n${validation.warnings.join('\n')}`;
            }
            
            // 显示结果
            await dialog.showMessageBox(this.mainWindow, {
                type: validation.isValid ? 'info' : 'warning',
                title: '验证结果',
                message: `草稿验证${validation.isValid ? '通过' : '失败'}`,
                detail: detail
            });
            
        } catch (error) {
            console.error('[MainProcess] Validate draft failed:', error);
            await dialog.showErrorBox('错误', `验证失败：${error.message}`);
        }
    }
    
    /**
     * 打开草稿目录
     */
    async openDraftsDirectory() {
        try {
            const { shell } = require('electron');
            const draftsPath = path.join(__dirname, '../../coze-plugins/草稿');
            await shell.openPath(draftsPath);
        } catch (error) {
            console.error('[MainProcess] Open drafts directory failed:', error);
            await dialog.showErrorBox('错误', `打开目录失败：${error.message}`);
        }
    }
    
    /**
     * 初始化应用
     */
    initialize() {
        console.log('[MainProcess] Initializing application...');
        
        // 注册Duration相关的IPC处理器
        DurationIpcHandlers.registerHandlers();
        
        // 应用准备就绪
        app.whenReady().then(() => {
            this.createWindow();
            this.createMenu();
            
            console.log('[MainProcess] Application initialized successfully');
        });
        
        // 所有窗口关闭
        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });
        
        // 应用激活
        app.on('activate', () => {
            if (BrowserWindow.getAllWindows().length === 0) {
                this.createWindow();
            }
        });
        
        // 应用退出前清理
        app.on('before-quit', () => {
            console.log('[MainProcess] Application is quitting, cleaning up...');
            DurationIpcHandlers.unregisterHandlers();
        });
    }
}

// 创建并初始化主进程
const mainProcess = new MainProcess();
mainProcess.initialize();
