const fs = require('fs-extra');
const path = require('path');
const DraftDurationCalculator = require('../utils/draftDurationCalculator');

/**
 * 草稿管理器
 * 负责草稿文件的读取、保存，并自动修正duration
 */
class DraftManager {
    /**
     * 保存草稿文件（集成duration自动修正）
     * @param {string} draftPath - 草稿文件路径
     * @param {Object} draftContent - 草稿内容
     * @returns {Promise<Object>} 保存后的草稿内容
     */
    static async saveDraft(draftPath, draftContent) {
        try {
            console.log(`[DraftManager] Saving draft: ${draftPath}`);
            
            // 自动修正duration（核心功能）
            const fixedContent = DraftDurationCalculator.fixDraftDuration(draftContent);
            
            // 确保目录存在
            await fs.ensureDir(path.dirname(draftPath));
            
            // 保存到文件
            await fs.writeJson(draftPath, fixedContent, { spaces: 2 });
            
            console.log(`[DraftManager] Draft saved successfully with duration: ${fixedContent.duration} microseconds (${fixedContent.duration / 1000000} seconds)`);
            return fixedContent;
        } catch (error) {
            console.error(`[DraftManager] Failed to save draft ${draftPath}:`, error);
            throw error;
        }
    }
    
    /**
     * 读取草稿文件（可选：读取时也进行修正）
     * @param {string} draftPath - 草稿文件路径
     * @param {boolean} autoFix - 是否自动修正duration
     * @returns {Promise<Object>} 草稿内容
     */
    static async loadDraft(draftPath, autoFix = true) {
        try {
            console.log(`[DraftManager] Loading draft: ${draftPath}`);
            
            if (!await fs.pathExists(draftPath)) {
                throw new Error(`Draft file not found: ${draftPath}`);
            }
            
            const draftContent = await fs.readJson(draftPath);
            
            if (autoFix) {
                // 读取时也进行修正（可选功能）
                const fixedContent = DraftDurationCalculator.fixDraftDuration(draftContent);
                
                // 如果有修正，自动保存
                if (fixedContent.duration !== draftContent.duration) {
                    console.log(`[DraftManager] Auto-fixing duration on load: ${draftContent.duration} -> ${fixedContent.duration}`);
                    await this.saveDraft(draftPath, fixedContent);
                    return fixedContent;
                }
            }
            
            console.log(`[DraftManager] Draft loaded successfully: ${draftPath} (duration: ${draftContent.duration})`);
            return draftContent;
        } catch (error) {
            console.error(`[DraftManager] Failed to load draft ${draftPath}:`, error);
            throw error;
        }
    }
    
    /**
     * 复制草稿文件（同时修正duration）
     * @param {string} sourcePath - 源草稿文件路径
     * @param {string} targetPath - 目标草稿文件路径
     * @returns {Promise<Object>} 复制后的草稿内容
     */
    static async copyDraft(sourcePath, targetPath) {
        try {
            console.log(`[DraftManager] Copying draft: ${sourcePath} -> ${targetPath}`);
            
            // 读取源文件
            const sourceContent = await this.loadDraft(sourcePath, true);
            
            // 保存到目标位置（会自动修正duration）
            const copiedContent = await this.saveDraft(targetPath, sourceContent);
            
            console.log(`[DraftManager] Draft copied successfully`);
            return copiedContent;
        } catch (error) {
            console.error(`[DraftManager] Failed to copy draft ${sourcePath} -> ${targetPath}:`, error);
            throw error;
        }
    }
    
    /**
     * 验证草稿文件的完整性
     * @param {string} draftPath - 草稿文件路径
     * @returns {Promise<Object>} 验证结果
     */
    static async validateDraft(draftPath) {
        try {
            console.log(`[DraftManager] Validating draft: ${draftPath}`);
            
            const draftContent = await fs.readJson(draftPath);
            const details = DraftDurationCalculator.getDurationDetails(draftContent);
            
            const validation = {
                isValid: true,
                errors: [],
                warnings: [],
                details: details,
                recommendations: []
            };
            
            // 检查基本结构
            if (!draftContent.tracks || !Array.isArray(draftContent.tracks)) {
                validation.errors.push('Missing or invalid tracks array');
                validation.isValid = false;
            }
            
            // 检查duration是否正确
            if (draftContent.duration !== details.finalDuration) {
                validation.warnings.push(`Duration mismatch: current=${draftContent.duration}, calculated=${details.finalDuration}`);
                validation.recommendations.push('Run duration fix to correct the total duration');
            }
            
            // 检查空轨道
            const emptyTracks = details.tracks.filter(track => track.segmentCount === 0);
            if (emptyTracks.length > 0) {
                validation.warnings.push(`Found ${emptyTracks.length} empty tracks`);
            }
            
            // 检查异常短的duration
            if (details.maxEndTime < 1000000) { // 小于1秒
                validation.warnings.push(`Very short content duration: ${details.maxEndTime / 1000000} seconds`);
            }
            
            console.log(`[DraftManager] Draft validation complete: ${validation.isValid ? 'VALID' : 'INVALID'} (${validation.errors.length} errors, ${validation.warnings.length} warnings)`);
            return validation;
        } catch (error) {
            console.error(`[DraftManager] Failed to validate draft ${draftPath}:`, error);
            return {
                isValid: false,
                errors: [`Validation failed: ${error.message}`],
                warnings: [],
                details: null,
                recommendations: []
            };
        }
    }
    
    /**
     * 获取草稿目录下的所有草稿列表
     * @param {string} draftsDirectory - 草稿目录路径
     * @returns {Promise<Array>} 草稿列表
     */
    static async listDrafts(draftsDirectory) {
        try {
            if (!await fs.pathExists(draftsDirectory)) {
                console.warn(`[DraftManager] Drafts directory not found: ${draftsDirectory}`);
                return [];
            }
            
            const entries = await fs.readdir(draftsDirectory, { withFileTypes: true });
            const drafts = [];
            
            for (const entry of entries) {
                if (entry.isDirectory()) {
                    const draftPath = path.join(draftsDirectory, entry.name, 'draft_content.json');
                    
                    if (await fs.pathExists(draftPath)) {
                        try {
                            const stats = await fs.stat(draftPath);
                            const draftContent = await fs.readJson(draftPath);
                            
                            drafts.push({
                                name: entry.name,
                                path: draftPath,
                                id: draftContent.id || entry.name,
                                duration: draftContent.duration || 0,
                                lastModified: stats.mtime,
                                size: stats.size
                            });
                        } catch (error) {
                            console.warn(`[DraftManager] Failed to read draft ${draftPath}:`, error.message);
                        }
                    }
                }
            }
            
            // 按最后修改时间排序
            drafts.sort((a, b) => b.lastModified - a.lastModified);
            
            console.log(`[DraftManager] Found ${drafts.length} drafts in ${draftsDirectory}`);
            return drafts;
        } catch (error) {
            console.error(`[DraftManager] Failed to list drafts in ${draftsDirectory}:`, error);
            return [];
        }
    }
}

module.exports = DraftManager;
