-- 添加敏感词管理菜单权限
-- 注意：这里的ID需要确保在系统中是唯一的，建议使用UUID生成器生成

-- 1. 添加一级菜单：敏感词管理
INSERT INTO `sys_permission` (
    `id`, 
    `parent_id`, 
    `name`, 
    `url`, 
    `component`, 
    `component_name`, 
    `redirect`, 
    `menu_type`, 
    `perms`, 
    `perms_type`, 
    `sort_no`, 
    `always_show`, 
    `icon`, 
    `is_route`, 
    `is_leaf`, 
    `keep_alive`, 
    `hidden`, 
    `hide_tab`, 
    `description`, 
    `status`, 
    `del_flag`, 
    `rule_flag`, 
    `create_by`, 
    `create_time`, 
    `update_by`, 
    `update_time`, 
    `internal_or_external`
) VALUES (
    'sensitive_word_management_menu_id',  -- 菜单ID
    '',                                   -- 父菜单ID（空表示一级菜单）
    '敏感词管理',                         -- 菜单名称
    '/sensitive-word',                    -- 路由路径
    'layouts/RouteView',                  -- 组件路径
    '',                                   -- 组件名称
    '/sensitive-word/list',               -- 重定向路径
    0,                                    -- 菜单类型（0-菜单）
    '',                                   -- 权限标识
    '1',                                  -- 权限类型
    5.0,                                  -- 排序号
    0,                                    -- 是否总是显示
    'safety',                             -- 图标
    1,                                    -- 是否路由菜单
    0,                                    -- 是否叶子节点
    0,                                    -- 是否缓存
    0,                                    -- 是否隐藏
    0,                                    -- 是否隐藏Tab
    '敏感词管理模块',                     -- 描述
    '1',                                  -- 状态
    0,                                    -- 删除标志
    0,                                    -- 规则标志
    'admin',                              -- 创建人
    NOW(),                                -- 创建时间
    'admin',                              -- 更新人
    NOW(),                                -- 更新时间
    0                                     -- 内部或外部
);

-- 2. 添加子菜单：敏感词列表
INSERT INTO `sys_permission` (
    `id`, 
    `parent_id`, 
    `name`, 
    `url`, 
    `component`, 
    `component_name`, 
    `redirect`, 
    `menu_type`, 
    `perms`, 
    `perms_type`, 
    `sort_no`, 
    `always_show`, 
    `icon`, 
    `is_route`, 
    `is_leaf`, 
    `keep_alive`, 
    `hidden`, 
    `hide_tab`, 
    `description`, 
    `status`, 
    `del_flag`, 
    `rule_flag`, 
    `create_by`, 
    `create_time`, 
    `update_by`, 
    `update_time`, 
    `internal_or_external`
) VALUES (
    'sensitive_word_list_menu_id',        -- 菜单ID
    'sensitive_word_management_menu_id',  -- 父菜单ID
    '敏感词列表',                         -- 菜单名称
    '/sensitive-word/list',               -- 路由路径
    'system/SensitiveWordList',           -- 组件路径
    'SensitiveWordList',                  -- 组件名称
    '',                                   -- 重定向路径
    1,                                    -- 菜单类型（1-菜单）
    'sensitive:word:list',                -- 权限标识
    '1',                                  -- 权限类型
    1.0,                                  -- 排序号
    0,                                    -- 是否总是显示
    '',                                   -- 图标
    1,                                    -- 是否路由菜单
    1,                                    -- 是否叶子节点
    1,                                    -- 是否缓存
    0,                                    -- 是否隐藏
    0,                                    -- 是否隐藏Tab
    '敏感词列表页面',                     -- 描述
    '1',                                  -- 状态
    0,                                    -- 删除标志
    0,                                    -- 规则标志
    'admin',                              -- 创建人
    NOW(),                                -- 创建时间
    'admin',                              -- 更新人
    NOW(),                                -- 更新时间
    0                                     -- 内部或外部
);

-- 3. 添加按钮权限：添加
INSERT INTO `sys_permission` (
    `id`, 
    `parent_id`, 
    `name`, 
    `url`, 
    `component`, 
    `component_name`, 
    `redirect`, 
    `menu_type`, 
    `perms`, 
    `perms_type`, 
    `sort_no`, 
    `always_show`, 
    `icon`, 
    `is_route`, 
    `is_leaf`, 
    `keep_alive`, 
    `hidden`, 
    `hide_tab`, 
    `description`, 
    `status`, 
    `del_flag`, 
    `rule_flag`, 
    `create_by`, 
    `create_time`, 
    `update_by`, 
    `update_time`, 
    `internal_or_external`
) VALUES (
    'sensitive_word_add_btn_id',          -- 按钮ID
    'sensitive_word_list_menu_id',        -- 父菜单ID
    '添加',                               -- 按钮名称
    '',                                   -- 路由路径
    '',                                   -- 组件路径
    '',                                   -- 组件名称
    '',                                   -- 重定向路径
    2,                                    -- 菜单类型（2-按钮）
    'sensitive:word:add',                 -- 权限标识
    '1',                                  -- 权限类型
    1.0,                                  -- 排序号
    0,                                    -- 是否总是显示
    '',                                   -- 图标
    0,                                    -- 是否路由菜单
    1,                                    -- 是否叶子节点
    0,                                    -- 是否缓存
    0,                                    -- 是否隐藏
    0,                                    -- 是否隐藏Tab
    '添加敏感词',                         -- 描述
    '1',                                  -- 状态
    0,                                    -- 删除标志
    0,                                    -- 规则标志
    'admin',                              -- 创建人
    NOW(),                                -- 创建时间
    'admin',                              -- 更新人
    NOW(),                                -- 更新时间
    0                                     -- 内部或外部
);

-- 4. 添加按钮权限：编辑
INSERT INTO `sys_permission` (
    `id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, 
    `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`
) VALUES (
    'sensitive_word_edit_btn_id', 'sensitive_word_list_menu_id', '编辑', '', '', '', '', 
    2, 'sensitive:word:edit', '1', 2.0, 0, '', 
    0, 1, 0, 0, 0, '编辑敏感词', 
    '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 5. 添加按钮权限：删除
INSERT INTO `sys_permission` (
    `id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, 
    `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`
) VALUES (
    'sensitive_word_delete_btn_id', 'sensitive_word_list_menu_id', '删除', '', '', '', '', 
    2, 'sensitive:word:delete', '1', 3.0, 0, '', 
    0, 1, 0, 0, 0, '删除敏感词', 
    '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 6. 添加按钮权限：批量删除
INSERT INTO `sys_permission` (
    `id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, 
    `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`
) VALUES (
    'sensitive_word_batch_delete_btn_id', 'sensitive_word_list_menu_id', '批量删除', '', '', '', '', 
    2, 'sensitive:word:deleteBatch', '1', 4.0, 0, '', 
    0, 1, 0, 0, 0, '批量删除敏感词', 
    '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 7. 添加按钮权限：导入
INSERT INTO `sys_permission` (
    `id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, 
    `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`
) VALUES (
    'sensitive_word_import_btn_id', 'sensitive_word_list_menu_id', '导入', '', '', '', '', 
    2, 'sensitive:word:import', '1', 5.0, 0, '', 
    0, 1, 0, 0, 0, '导入敏感词', 
    '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 8. 添加按钮权限：导出
INSERT INTO `sys_permission` (
    `id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, 
    `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`
) VALUES (
    'sensitive_word_export_btn_id', 'sensitive_word_list_menu_id', '导出', '', '', '', '', 
    2, 'sensitive:word:export', '1', 6.0, 0, '', 
    0, 1, 0, 0, 0, '导出敏感词', 
    '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 9. 添加按钮权限：统计分析
INSERT INTO `sys_permission` (
    `id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, 
    `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`
) VALUES (
    'sensitive_word_statistics_btn_id', 'sensitive_word_list_menu_id', '统计分析', '', '', '', '', 
    2, 'sensitive:word:statistics', '1', 7.0, 0, '', 
    0, 1, 0, 0, 0, '敏感词统计分析', 
    '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 10. 为admin角色分配敏感词管理权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`) 
SELECT 'f6817f48af4fb3af11b9e8bf182f618b', `id` 
FROM `sys_permission` 
WHERE `id` IN (
    'sensitive_word_management_menu_id',
    'sensitive_word_list_menu_id',
    'sensitive_word_add_btn_id',
    'sensitive_word_edit_btn_id',
    'sensitive_word_delete_btn_id',
    'sensitive_word_batch_delete_btn_id',
    'sensitive_word_import_btn_id',
    'sensitive_word_export_btn_id',
    'sensitive_word_statistics_btn_id'
);
