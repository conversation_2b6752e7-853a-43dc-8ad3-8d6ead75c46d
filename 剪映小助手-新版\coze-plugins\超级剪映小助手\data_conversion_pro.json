{
  "openapi": "3.0.0",
  "info": {
    "title": "剪映小助手_超级剪映小助手 - 多模式数据转换",
    "description": "多模式数据转换",
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "https://www.aigcview.com"
    }
  ],
  "paths": {
    "/jeecg-boot/api/jianyingpro/data_conversion": {
      "post": {
        "summary": "智能数据转换",
        "description": "智能数据转换 - 支持同时输入多种数据类型，可同时提供字符串、字符串列表、对象列表",
        "operationId": "data_conversion_pro",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "access_key": {
                    "type": "string",
                    "description": "访问密钥（必填）- 用于验证API调用权限的安全密钥",
                    "example": "JianyingAPI_2025_SecureAccess_AigcView",
                    "default": "JianyingAPI_2025_SecureAccess_AigcView"
                  },
                  "string_to_list": {
                    "type": "string",
                    "description": "字符串数据（可选）- 输入字符串，系统会直接包装成数组格式\n\n📝 转换说明：\n• 输入：\"www.coze.cn\"\n• 输出：[\"www.coze.cn\"]\n• 不进行分割，直接包装成数组",
                    "example": "www.coze.cn"
                  },
                  "string_list_to_objects": {
                    "type": "array",
                    "description": "字符串列表数据（可选）- 输入字符串数组，系统会自动转换为对象格式\n\n📝 转换说明：\n• 输入：[\"苹果\", \"香蕉\", \"橙子\"]\n• 输出：[{\"output\": \"苹果\"}, {\"output\": \"香蕉\"}, {\"output\": \"橙子\"}]\n• 每个字符串转换为包含output字段的对象",
                    "items": {
                      "type": "string"
                    },
                    "example": ["苹果", "香蕉", "橙子"]
                  },
                  "objects_to_string_list": {
                    "type": "array",
                    "description": "对象列表数据（可选）- 输入对象数组，系统会智能提取字段转换为字符串列表\n\n📝 智能转换说明：\n🤖 智能模式（不指定extract_field）：\n• 单字段对象：自动提取唯一字段的值\n• 多字段对象：提取所有字段的值\n\n🎯 指定模式（指定extract_field）：\n• 只提取指定字段的值\n• 默认提取output字段\n\n💡 支持的对象格式：\n• 标准格式：{\"output\": \"内容\"}\n• 自定义格式：{\"name\": \"内容\", \"color\": \"属性\"}",
                    "items": {
                      "type": "object",
                      "properties": {
                        "output": {
                          "type": "string",
                          "description": "标准输出内容字段"
                        }
                      }
                    },
                    "example": [
                      {"output": "苹果", "color": "红色"},
                      {"name": "橙子", "price": "5元"},
                      {"title": "香蕉"}
                    ]
                  }
                },
                "required": ["access_key"],
                "additionalProperties": false
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "智能数据转换成功",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "info": {
                      "type": "object",
                      "description": "转换结果数据 - 包含所有执行的转换结果，只返回核心内容\n\n📊 数据结构：\n• 只包含实际执行的转换结果\n• 每种转换都返回核心数组，不包含统计信息",
                      "properties": {
                        "string_to_list": {
                          "type": "array",
                          "description": "字符串转列表结果 - 当提供string_to_list时出现\n\n📝 结果说明：\n• 直接返回包含输入字符串的数组\n• 不进行分割，直接包装\n\n💡 示例：\n输入\"www.coze.cn\"\n→ [\"www.coze.cn\"]",
                          "items": {"type": "string"},
                          "example": ["www.coze.cn"]
                        },
                        "string_list_to_objects": {
                          "type": "array",
                          "description": "字符串列表转对象结果 - 当提供string_list_to_objects时出现\n\n📝 结果说明：\n• 直接返回转换后的对象数组\n• 每个对象包含output字段\n\n💡 示例：\n输入[\"苹果\", \"香蕉\"]\n→ [{\"output\": \"苹果\"}, {\"output\": \"香蕉\"}]",
                          "items": {
                            "type": "object",
                            "properties": {
                                  "output": {
                                    "type": "string",
                                    "description": "原字符串内容"
                                  }
                                },
                                "required": ["output"],
                                "additionalProperties": false
                              }
                            },
                            "example": [{"output": "苹果"}, {"output": "香蕉"}]
                        },
                        "objects_to_string_list": {
                          "type": "array",
                          "description": "对象列表转字符串列表结果 - 当提供objects_to_string_list时出现\n\n📝 结果说明：\n• 直接返回从对象中提取的字符串数组\n• 使用智能提取模式\n\n💡 示例：\n输入[{\"name\": \"苹果\"}, {\"title\": \"香蕉\"}]\n→ [\"苹果\", \"香蕉\"]",
                          "items": {"type": "string"},
                          "example": ["苹果", "香蕉"]
                        }
                      }
                    }
                  },
                  "required": ["info"]
                }
              }
            }
          },
          "400": {
            "description": "请求参数错误",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "error": {
                      "type": "string",
                      "description": "错误信息 - 简洁的错误描述，与基础版API保持兼容\n\n❌ 常见错误：\n• \"参数不完整: 至少需要提供一种输入数据\"\n• \"访问密钥无效\"\n• \"数据格式错误\"",
                      "example": "参数不完整: 至少需要提供一种输入数据"
                    },
                    "error_code": {
                      "type": "string",
                      "description": "错误码 - 标准化的错误代码，便于程序处理\n\n🔍 错误码分类：\n• PARAM_*: 参数相关错误\n• BUSINESS_*: 业务逻辑错误\n• SYSTEM_*: 系统内部错误",
                      "example": "PARAM_INCOMPLETE_003"
                    },
                    "error_message": {
                      "type": "string",
                      "description": "详细错误消息 - 更详细的错误说明\n\n📝 消息内容：\n• 错误的具体原因\n• 涉及的参数或数据\n• 错误发生的上下文",
                      "example": "参数不完整"
                    },
                    "error_details": {
                      "type": "string",
                      "description": "错误解决方案 - 具体的修复建议和操作指导\n\n💡 解决方案类型：\n• 参数修正建议\n• 数据格式要求\n• 操作步骤指导\n• 联系方式（如需要）",
                      "example": "请提供 input_string、input_string_list 或 input_object_list 中的至少一种"
                    }
                  },
                  "required": ["error", "error_code"]
                }
              }
            }
          },
          "500": {
            "description": "服务器内部错误",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "error": {
                      "type": "string",
                      "description": "错误信息",
                      "example": "数据转换失败: 服务器内部错误"
                    }
                  },
                  "required": ["error"]
                }
              }
            }
          }
        }
      }
    }
  }
}
