<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="306e7930-831d-4433-a8b7-b70d74b9abf2" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianying/service/JianyingDataboxService.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianying/service/JianyingDataboxService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService.java" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/java/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$10.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$10.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$11.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$12.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$12.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$13.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$13.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$14.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$14.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$15.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$15.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$16.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$16.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$17.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$17.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$18.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$18.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$19$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$19$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$19.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$19.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$20.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$20.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$21.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$21.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$22.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$22.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$6.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$6.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$7.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$7.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$8.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$8.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$9$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$9$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$9.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/CozeApiService$9.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianying/service/JianyingDataboxService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$12.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$14.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$14.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$15.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$15.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$16.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$16.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$17.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$17.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$18.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$18.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$19.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$19.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$20.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$21.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$21.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$22.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$22.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$23.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$23.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$24.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$25.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$25.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$26.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$26.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$27.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$27.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$28.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$28.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$29.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$29.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$30.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$30.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$31.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$32.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$32.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$33.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$33.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$34.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$35.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$35.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$36.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$36.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$37.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$37.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$38.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$38.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$39.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$39.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$40.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$40.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$41.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$41.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$6.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$7.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$7.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$8.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$8.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$9.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$9.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$KeywordRange.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl$KeywordRange.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/impl/JianyingProServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$10.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$10.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$11.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$11.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$20.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$20.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$21.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$21.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$22$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$22$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$22.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$22.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$23.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$23.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$24.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$24.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$25.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$25.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$26.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$27.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$27.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$29.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$29.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$30.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$30.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$31.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$31.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$6.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$7.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$7.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$8.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$8.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProAssistantService$9.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$1.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$1.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$2.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$2.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$3.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$3.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$4.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$4.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$5.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService$5.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService.class" beforeDir="false" afterPath="$PROJECT_DIR$/jeecg-boot-module-system/target/classes/org/jeecg/modules/jianyingpro/service/internal/JianyingProDataboxService.class" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="explicitlyEnabledProfiles" value="dev" />
    <option name="explicitlyDisabledProfiles" value="prod" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2ySsI0hrVVD7Z6hSzII5gP8jPBf" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.jeecg-boot-module-system [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-module-system [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-parent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-parent [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jeecg-boot-parent [validate].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.JeecgSystemApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/AigcView/AigcViewRd&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;advanced.settings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="JeecgSystemApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="jeecg-boot-module-system" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.jeecg.JeecgSystemApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="306e7930-831d-4433-a8b7-b70d74b9abf2" name="更改" comment="" />
      <created>1749839225426</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749839225426</updated>
      <workItem from="1749839226610" duration="2126000" />
      <workItem from="1749846796291" duration="37818000" />
      <workItem from="1750127497474" duration="664000" />
      <workItem from="1750147622680" duration="8219000" />
      <workItem from="1750182992775" duration="828000" />
      <workItem from="1750219207386" duration="34463000" />
      <workItem from="1750395165498" duration="16469000" />
      <workItem from="1750481146931" duration="18858000" />
      <workItem from="1750576054427" duration="13926000" />
      <workItem from="1750662956240" duration="24712000" />
      <workItem from="1750844199724" duration="10000" />
      <workItem from="1750924195138" duration="28172000" />
      <workItem from="1750964802742" duration="9819000" />
      <workItem from="1750999295568" duration="9206000" />
      <workItem from="1751010645542" duration="31817000" />
      <workItem from="1751184251064" duration="3114000" />
      <workItem from="1751308217715" duration="5256000" />
      <workItem from="1751366071895" duration="82680000" />
      <workItem from="1751786686767" duration="103917000" />
      <workItem from="1752333468590" duration="3861000" />
      <workItem from="1752422504660" duration="9302000" />
      <workItem from="1752506359093" duration="3126000" />
      <workItem from="1752633991731" duration="11995000" />
      <workItem from="1752716420676" duration="1695000" />
      <workItem from="1752752996018" duration="4905000" />
      <workItem from="1752833179798" duration="17286000" />
      <workItem from="1752909924462" duration="2263000" />
      <workItem from="1753102236494" duration="60366000" />
      <workItem from="1753502491755" duration="4485000" />
      <workItem from="1753638631113" duration="661000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>