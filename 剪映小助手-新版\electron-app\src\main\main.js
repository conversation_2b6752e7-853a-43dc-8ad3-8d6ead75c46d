const { app, BrowserWindow, ipcMain, dialog, shell, clipboard, globalShortcut, Menu } = require('electron')
const path = require('path')
const fs = require('fs')
const os = require('os')
const https = require('https')
const { spawn, exec } = require('child_process')
const Store = require('electron-store')
const yauzl = require('yauzl')
const { TosClient } = require('@volcengine/tos-sdk')

// 设置控制台编码为UTF-8，解决中文乱码问题
if (process.platform === 'win32') {
  try {
    process.stdout.setEncoding('utf8')
    process.stderr.setEncoding('utf8')
  } catch (error) {
    // 忽略编码设置错误
  }
}

// 导入版本检查管理器
const UpdateManager = require('../utils/UpdateManager')

// 初始化配置存储
const store = new Store()

// 初始化版本检查管理器
const updateManager = new UpdateManager({
  apiBaseUrl: 'https://aigcview.com/jeecg-boot',
  programType: 'desktop',
  enableBackgroundCheck: true,
  backgroundCheckInterval: 4 * 60 * 60 * 1000, // 4小时检查一次
  showOptionalUpdates: true
})

// 🔒 剪映专用TOS配置（华东上海，专用桶，无CDN回源）
const tosConfig = {
  accessKeyId: process.env.TOS_ACCESS_KEY_ID || 'AKLTMDlkOTAxNWJjZDYyNGEyYWFkODIzOGM3N2UxMGMyZTM',
  accessKeySecret: process.env.TOS_ACCESS_KEY_SECRET || 'TXpJeU1UZGhZVEU0TW1WaU5EQTJNR0UzTnpFeE5HTmpaV00wTTJabVpURQ==',
  region: process.env.TOS_REGION || 'cn-shanghai',
  bucket: process.env.TOS_BUCKET || 'aigcview-jianying',  // 🔧 修复：使用剪映专用桶
  endpoint: process.env.TOS_ENDPOINT || 'tos-cn-shanghai.volces.com'
}

// 🆕 CDN配置 - 剪映小助手禁用CDN，直接使用TOS SDK
const cdnConfig = {
  enabled: false,  // 🔒 禁用CDN，剪映专用桶直接使用TOS SDK
  domain: 'https://cdn.aigcview.com'
}

// 🆕 TOS URL前缀配置
const tosUrlPrefix = `https://${tosConfig.bucket}.${tosConfig.endpoint}`

// 创建TOS客户端（优化网络配置和安全设置）
// TOS配置信息包含敏感密钥，不在控制台输出

const tosClient = new TosClient({
  accessKeyId: tosConfig.accessKeyId,
  accessKeySecret: tosConfig.accessKeySecret,
  region: tosConfig.region,
  endpoint: tosConfig.endpoint,
  // 网络超时配置（优化用户体验）
  connectionTimeout: 30000,    // 连接超时30秒
  requestTimeout: 120000,      // 请求超时120秒（2分钟）
  // 重试配置（减少重试次数）
  maxRetryCount: 3,            // 最大重试3次
  // SSL配置（启用安全验证）
  enableVerifySSL: true,       // 启用SSL验证
  // 连接池配置
  maxConnections: 50,          // 最大连接数
  idleConnectionTime: 30000,   // 空闲连接时间30秒
  // 启用CRC校验（提高数据完整性）
  enableCRC: true              // 启用CRC校验
})

console.log('TOS客户端创建成功')

// 测试TOS连接
async function testTosConnection() {
  try {
    console.log('测试TOS连接...')
    // 尝试列出bucket来测试连接
    const buckets = await tosClient.listBuckets()
    console.log('TOS连接测试成功:', buckets)
  } catch (error) {
    console.error('TOS连接测试失败:')
    console.error('错误类型:', typeof error)
    console.error('错误对象:', error)
    console.error('错误消息:', error.message)
    console.error('错误代码:', error.code)

    // 如果主窗口已创建，发送错误信息到渲染进程
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.send('import-progress', `🔴 TOS连接测试失败`)
      mainWindow.webContents.send('import-progress', `错误: ${error.message || '未知错误'}`)
    }
  }
}

// 启动时测试连接（延迟到主窗口创建后）
setTimeout(() => {
  if (mainWindow && mainWindow.webContents) {
    testTosConnection()
  }
}, 3000) // 3秒后测试，确保主窗口已创建

// Mac系统路径分隔符转换函数
function convertPathSeparatorsForMac(jsonObj) {
  console.log('开始转换Mac路径分隔符...')

  // 深度克隆对象，避免修改原始数据
  const clonedObj = JSON.parse(JSON.stringify(jsonObj))

  // 递归转换所有path字段中的反斜杠为正斜杠
  function convertPathsInObject(obj) {
    if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        if (key === 'path' && typeof obj[key] === 'string') {
          // 转换路径分隔符：反斜杠 -> 正斜杠
          const originalPath = obj[key]
          obj[key] = originalPath.replace(/\\/g, '/')
          console.log(`路径转换: ${originalPath} -> ${obj[key]}`)
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          // 递归处理嵌套对象和数组
          convertPathsInObject(obj[key])
        }
      }
    }
  }

  convertPathsInObject(clonedObj)
  console.log('Mac路径分隔符转换完成')
  return clonedObj
}

// 文件夹属性分析函数
function analyzeFolderProperties(folderPath, label = '') {
  try {
    const stats = fs.statSync(folderPath)
    console.log(`=== 文件夹属性分析 ${label} ===`)
    console.log('路径:', folderPath)
    console.log('是否为目录:', stats.isDirectory())
    console.log('权限模式:', stats.mode.toString(8))
    console.log('用户ID:', stats.uid)
    console.log('组ID:', stats.gid)
    console.log('大小:', stats.size)
    console.log('访问时间:', stats.atime.toISOString())
    console.log('修改时间:', stats.mtime.toISOString())
    console.log('状态改变时间:', stats.ctime.toISOString())
    console.log('创建时间:', stats.birthtime.toISOString())

    // Windows特定属性检查
    if (process.platform === 'win32') {
      exec(`dir "${folderPath}" /Q /T:C`, (error, stdout, stderr) => {
        if (!error) {
          console.log('Windows目录信息:', stdout)
        }
      })

      exec(`attrib "${folderPath}"`, (error, stdout, stderr) => {
        if (!error) {
          console.log('Windows文件属性:', stdout.trim())
        }
      })
    }

    return {
      isDirectory: stats.isDirectory(),
      mode: stats.mode,
      uid: stats.uid,
      gid: stats.gid,
      size: stats.size,
      atime: stats.atime,
      mtime: stats.mtime,
      ctime: stats.ctime,
      birthtime: stats.birthtime
    }
  } catch (error) {
    console.error(`分析文件夹属性失败 ${label}:`, error)
    return null
  }
}

// 改进的文件夹创建函数
function createDraftFolderWithCorrectProperties(folderPath) {
  try {
    console.log('使用改进方法创建文件夹:', folderPath)

    // 1. 创建文件夹（使用默认权限）
    fs.mkdirSync(folderPath, { recursive: true })

    // 2. 设置时间戳（模仿自然创建的文件夹）
    const now = new Date()
    fs.utimesSync(folderPath, now, now)

    // 3. Windows特定优化
    if (process.platform === 'win32') {
      // 确保文件夹没有特殊属性
      exec(`attrib -h -s -r "${folderPath}"`, (error, stdout, stderr) => {
        if (error) {
          console.log('设置文件夹属性时出现警告:', error.message)
        } else {
          console.log('已清除文件夹特殊属性')
        }
      })

      // 设置标准的文件夹权限
      exec(`icacls "${folderPath}" /grant Everyone:(OI)(CI)F`, (error, stdout, stderr) => {
        if (error) {
          console.log('设置文件夹权限时出现警告:', error.message)
        } else {
          console.log('已设置标准文件夹权限')
        }
      })
    }

    console.log('文件夹创建完成:', folderPath)
    return true
  } catch (error) {
    console.error('创建文件夹失败:', error)
    return false
  }
}

let mainWindow

// 创建主窗口
function createWindow() {
  console.log('=== 剪映小助手启动 ===')
  console.log('开始创建主窗口...')

  mainWindow = new BrowserWindow({
    width: 1430,
    height: 1130,
    minWidth: 1000,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, '../../assets/icon.png'),
    title: '剪映小助手 - 智界AigcView',
    show: false,
    titleBarStyle: 'default'
  })

  // 加载主页面
  mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'))

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()

    // 不再默认打开开发者工具
    // setTimeout(() => {
    //   mainWindow.webContents.openDevTools({ mode: 'detach' })
    //   console.log('开发者工具已打开')
    // }, 1000)

    // 清理缓存并强制刷新
    mainWindow.webContents.session.clearCache()
    console.log('已清理Electron缓存')
    console.log('主窗口创建完成')
  })

  // 窗口关闭事件
  mainWindow.on('closed', () => {
    mainWindow = null
  })
}

// 创建中文菜单
function createMenu() {
  const template = [
    {
      label: '编辑',
      submenu: [
        {
          label: '复制',
          accelerator: 'CmdOrCtrl+C',
          role: 'copy'
        },
        {
          label: '粘贴',
          accelerator: 'CmdOrCtrl+V',
          role: 'paste'
        },
        {
          label: '全选',
          accelerator: 'CmdOrCtrl+A',
          role: 'selectall'
        },
        {
          label: '剪切',
          accelerator: 'CmdOrCtrl+X',
          role: 'cut'
        },
        {
          label: '撤销',
          accelerator: 'CmdOrCtrl+Z',
          role: 'undo'
        },
        {
          label: '重做',
          accelerator: 'CmdOrCtrl+Shift+Z',
          role: 'redo'
        }
      ]
    },
    // {
    //   label: '刷新',
    //   submenu: [
    //     {
    //       label: '重新加载',
    //       accelerator: 'CmdOrCtrl+R',
    //       click: () => {
    //         mainWindow.reload()
    //       }
    //     },
    //     {
    //       label: '开发者工具',
    //       accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
    //       click: () => {
    //         mainWindow.webContents.toggleDevTools()
    //       }
    //     },
    //     { type: 'separator' },
    //     {
    //       label: '实际大小',
    //       accelerator: 'CmdOrCtrl+0',
    //       role: 'resetzoom'
    //     },
    //     {
    //       label: '放大',
    //       accelerator: 'CmdOrCtrl+Plus',
    //       role: 'zoomin'
    //     },
    //     {
    //       label: '缩小',
    //       accelerator: 'CmdOrCtrl+-',
    //       role: 'zoomout'
    //     }
    //   ]
    // },
    // {
    //       label: '开发者工具',
    //       accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
    //       click: () => {
    //         mainWindow.webContents.toggleDevTools()
    //       }
    //     },
    {
      label: '重新加载',
      accelerator: 'CmdOrCtrl+R',
      click: () => {
        mainWindow.reload()
      }
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于剪映小助手',
          click: () => {
            // 通过IPC调用渲染进程显示关于弹窗
            if (mainWindow && !mainWindow.isDestroyed()) {
              mainWindow.webContents.send('show-about-dialog')
            }
          }
        },
        {
          label: '访问官网',
          click: () => {
            shell.openExternal('https://www.aigcview.com')
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// 全局变量，标记是否允许创建主窗口
let canCreateMainWindow = false

// 应用准备就绪
app.whenReady().then(async () => {
  console.log('=== Electron应用启动 ===')
  console.log('开始版本检查...')

  // 清除跳过的版本列表（因为现在所有更新都是强制的）
  updateManager.versionChecker.updateUserPreferences({ skippedVersions: [] });
  console.log('已清除跳过的版本列表');

  // 启动时检查版本更新
  const canContinue = await updateManager.checkOnStartup()

  if (canContinue) {
    console.log('版本检查完成，创建主窗口...')
    canCreateMainWindow = true
    createWindow()

    // 设置主窗口引用到更新管理器
    if (mainWindow) {
      updateManager.setMainWindow(mainWindow)
    }

    createMenu()
  } else {
    console.log('需要强制更新，等待用户操作...')
    canCreateMainWindow = false
    // 强制更新时不创建主窗口，等待用户选择
  }

  // macOS 特殊处理
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0 && canCreateMainWindow) {
      createWindow()
    }
  })

  // 注册全局快捷键
  registerGlobalShortcuts()

  // 不自动检查剪贴板，让用户手动操作
  // setTimeout(checkClipboardForDraftUrl, 1000)

  console.log('应用启动完成，所有事件已注册')
})

// 所有窗口关闭时退出应用 (macOS除外)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 应用退出前清理
app.on('before-quit', () => {
  console.log('应用即将退出，清理资源...')
  globalShortcut.unregisterAll()

  // 清理版本检查管理器
  if (updateManager) {
    updateManager.cleanup()
  }

  // 清理下载进度管理器
  if (downloadProgressCalculator) {
    downloadProgressCalculator.cleanup()
  }
  if (batchDownloadManager) {
    // batchDownloadManager没有cleanup方法，只需要重置
    batchDownloadManager.currentBatch = null
  }
})

// ===== 下载进度管理工具类 =====

// 进度计算工具类
class DownloadProgressCalculator {
  constructor() {
    this.downloads = new Map() // 存储每个下载的状态
  }

  // 开始新的下载
  startDownload(downloadId, fileName, totalBytes = 0, fileType = 'unknown') {
    this.downloads.set(downloadId, {
      fileName,
      fileType,
      totalBytes,
      downloadedBytes: 0,
      startTime: Date.now(),
      lastUpdateTime: Date.now(),
      lastDownloadedBytes: 0,
      speedHistory: [] // 用于计算平均速度
    })

    console.log(`开始下载进度跟踪: ${fileName} (${this.formatFileSize(totalBytes)})`)

    // 立即返回初始进度信息，确保UI能立即显示
    return {
      fileName,
      fileType,
      downloadedBytes: 0,
      totalBytes,
      progress: 0,
      downloadSpeed: 0,
      averageSpeed: 0,
      estimatedTimeRemaining: 0,
      elapsedTime: 0,
      status: 'starting'
    }
  }

  // 更新下载进度
  updateProgress(downloadId, downloadedBytes) {
    const download = this.downloads.get(downloadId)
    if (!download) return null

    try {
      const now = Date.now()
      const timeDiff = (now - download.lastUpdateTime) / 1000
      const bytesDiff = downloadedBytes - download.lastDownloadedBytes

      // 防止除零错误和异常值
      const instantSpeed = timeDiff > 0 && bytesDiff >= 0 ? bytesDiff / timeDiff : 0

      // 更新速度历史（保留最近10个数据点）
      if (instantSpeed >= 0 && instantSpeed < Number.MAX_SAFE_INTEGER) {
        download.speedHistory.push(instantSpeed)
        if (download.speedHistory.length > 10) {
          download.speedHistory.shift()
        }
      }

      // 计算平均速度（过滤异常值）
      const validSpeeds = download.speedHistory.filter(speed => speed >= 0 && speed < Number.MAX_SAFE_INTEGER)
      const averageSpeed = validSpeeds.length > 0 ? validSpeeds.reduce((a, b) => a + b, 0) / validSpeeds.length : 0

      // 计算进度百分比（确保在0-100范围内）
      let progress = 0
      if (download.totalBytes > 0 && downloadedBytes >= 0) {
        progress = Math.min(100, Math.max(0, (downloadedBytes / download.totalBytes) * 100))
      }

      // 计算预估剩余时间
      const remainingBytes = Math.max(0, download.totalBytes - downloadedBytes)
      const eta = averageSpeed > 0 && download.totalBytes > 0 && remainingBytes > 0 ? remainingBytes / averageSpeed : 0

      // 更新下载状态
      download.downloadedBytes = Math.max(0, downloadedBytes)
      download.lastUpdateTime = now
      download.lastDownloadedBytes = download.downloadedBytes

      return {
        fileName: download.fileName,
        fileType: download.fileType,
        downloadedBytes: download.downloadedBytes,
        totalBytes: download.totalBytes,
        progress: Math.round(progress),
        downloadSpeed: Math.max(0, instantSpeed),
        averageSpeed: Math.max(0, averageSpeed),
        estimatedTimeRemaining: Math.max(0, eta),
        elapsedTime: Math.max(0, (now - download.startTime) / 1000),
        status: 'downloading'
      }
    } catch (error) {
      console.error('更新下载进度时出错:', error)
      return null
    }
  }

  // 完成下载
  completeDownload(downloadId) {
    const download = this.downloads.get(downloadId)
    if (download) {
      this.downloads.delete(downloadId)
      console.log(`下载完成: ${download.fileName}`)
      return {
        fileName: download.fileName,
        fileType: download.fileType,
        downloadedBytes: download.downloadedBytes,
        totalBytes: download.totalBytes,
        progress: 100,
        status: 'completed',
        estimatedTimeRemaining: 0,
        elapsedTime: (Date.now() - download.startTime) / 1000
      }
    }
    return null
  }

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 格式化时间
  formatTime(seconds) {
    if (!seconds || seconds === Infinity) return '--'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 格式化速度
  formatSpeed(bytesPerSecond) {
    return this.formatFileSize(bytesPerSecond) + '/s'
  }

  // 清理资源
  cleanup() {
    this.downloads.clear()
  }
}

// 批量下载进度管理器
class BatchDownloadManager {
  constructor() {
    this.currentBatch = null
  }

  startBatch(fileList) {
    this.currentBatch = {
      totalFiles: fileList.length,
      fileList: fileList,
      completedFiles: 0,
      failedFiles: 0,
      startTime: Date.now()
    }

    console.log(`🚀 [BATCH] 开始批量下载: ${fileList.length} 个文件`)

    // 🆕 通知渲染进程初始化批量下载状态
    if (mainWindow && !mainWindow.isDestroyed()) {
      // 发送批量下载开始事件（保持向后兼容）
      mainWindow.webContents.send('download-batch-start', {
        totalFiles: fileList.length,
        fileList: fileList.map(f => f.fileName || f)
      })

      // 🆕 新增：通知下载进度管理器初始化批量状态
      mainWindow.webContents.send('init-batch-download', {
        totalFiles: fileList.length,
        fileList: fileList.map(f => f.fileName || f)
      })
    }
  }

  updateBatchProgress(currentFileIndex, fileProgress) {
    if (!this.currentBatch) return null

    // 计算整体进度：已完成文件 + 当前文件进度
    const completedProgress = (currentFileIndex - 1) * 100
    const currentProgress = fileProgress
    const totalProgress = (completedProgress + currentProgress) / this.currentBatch.totalFiles

    return {
      currentFileIndex,
      totalFiles: this.currentBatch.totalFiles,
      batchProgress: Math.round(totalProgress),
      completedFiles: this.currentBatch.completedFiles,
      failedFiles: this.currentBatch.failedFiles
    }
  }

  completeFile(success = true) {
    if (!this.currentBatch) return

    if (success) {
      this.currentBatch.completedFiles++
    } else {
      this.currentBatch.failedFiles++
    }

    // 计算当前总进度
    const totalProcessed = this.currentBatch.completedFiles + this.currentBatch.failedFiles
    const overallProgress = Math.round((totalProcessed / this.currentBatch.totalFiles) * 100)

    console.log(`🔄 [BATCH] 批量进度更新: ${totalProcessed}/${this.currentBatch.totalFiles} (${overallProgress}%)`)

    // 发送批量进度更新
    if (mainWindow && !mainWindow.isDestroyed()) {
      const progressData = {
        fileName: '批量下载进度',
        progress: 100, // 当前文件完成
        status: 'completed',
        currentFileIndex: totalProcessed,
        totalFiles: this.currentBatch.totalFiles,
        batchProgress: overallProgress
      }

      console.log(`📤 [BATCH] 发送批量进度数据:`, progressData)
      mainWindow.webContents.send('download-file-progress', progressData)
    }

    // 检查是否全部完成
    if (totalProcessed >= this.currentBatch.totalFiles) {
      this.completeBatch()
    }
  }

  completeBatch() {
    if (!this.currentBatch) return

    console.log(`🎉 [BATCH] 批量下载完成: 成功 ${this.currentBatch.completedFiles}，失败 ${this.currentBatch.failedFiles}`)

    // 发送最终的100%进度更新
    if (mainWindow && !mainWindow.isDestroyed()) {
      const finalProgressData = {
        fileName: '批量下载完成',
        progress: 100,
        status: 'completed',
        currentFileIndex: this.currentBatch.totalFiles,
        totalFiles: this.currentBatch.totalFiles,
        batchProgress: 100
      }

      console.log(`📤 [BATCH] 发送最终100%进度:`, finalProgressData)
      mainWindow.webContents.send('download-file-progress', finalProgressData)

      // 保存批量信息用于延迟发送
      const batchCompleteData = {
        successCount: this.currentBatch.completedFiles,
        failedCount: this.currentBatch.failedFiles,
        totalFiles: this.currentBatch.totalFiles
      }

      // 短暂延迟后发送批量完成事件
      setTimeout(() => {
        mainWindow.webContents.send('download-batch-complete', batchCompleteData)
      }, 100)
    }

    this.currentBatch = null
  }
}

// 创建全局实例
const downloadProgressCalculator = new DownloadProgressCalculator()
const batchDownloadManager = new BatchDownloadManager()

// 发送进度信息到渲染进程
function sendDownloadProgress(progressInfo, batchInfo = null) {
  try {
    if (!mainWindow || mainWindow.isDestroyed() || !progressInfo) {
      return
    }

    // 🚀 检查是否为更新下载
    const isUpdateDownload = batchInfo && batchInfo.isUpdate;

    // 获取批量进度信息
    const currentBatch = batchDownloadManager.currentBatch
    let enhancedData = { ...progressInfo }

    if (currentBatch && currentBatch.totalFiles > 0) {
      // 添加批量进度信息
      enhancedData.currentFileIndex = batchInfo?.currentFileIndex || 1
      enhancedData.totalFiles = currentBatch.totalFiles

      // 计算批量总进度（防止除零错误）
      const fileProgress = Math.max(0, Math.min(100, progressInfo.progress || 0))
      const completedFiles = Math.max(0, enhancedData.currentFileIndex - 1)
      const batchProgress = (completedFiles * 100 + fileProgress) / currentBatch.totalFiles
      enhancedData.batchProgress = Math.round(Math.max(0, Math.min(100, batchProgress)))
    }

    if (batchInfo) {
      enhancedData = { ...enhancedData, ...batchInfo }
    }

    // 🚀 根据下载类型发送不同的事件
    if (isUpdateDownload) {
      // 更新下载：发送到更新弹窗
      const updateProgressData = {
        status: 'downloading',
        fileName: progressInfo.fileName,
        progress: Math.round(progressInfo.progress || 0),
        speed: downloadProgressCalculator.formatSpeed(progressInfo.downloadSpeed || 0),
        downloaded: downloadProgressCalculator.formatFileSize(progressInfo.downloadedBytes || 0),
        total: downloadProgressCalculator.formatFileSize(progressInfo.totalBytes || 0)
      };

      mainWindow.webContents.send('download-progress', updateProgressData);
      console.log('🚀 发送更新下载进度:', updateProgressData);
    } else {
      // 普通下载：发送到下载进度管理器
      mainWindow.webContents.send('download-file-progress', enhancedData);
    }

    // 同时发送到日志（限制频率）
    const { fileName, progress, downloadSpeed, totalBytes } = progressInfo
    if (fileName && typeof progress === 'number') {
      const speedStr = downloadProgressCalculator.formatSpeed(downloadSpeed || 0)
      const sizeStr = totalBytes > 0 ? downloadProgressCalculator.formatFileSize(totalBytes) : '未知大小'

      let logMessage = `📊 ${fileName}: ${Math.round(progress)}% (${speedStr}, ${sizeStr})`
      if (enhancedData.currentFileIndex && enhancedData.totalFiles) {
        logMessage += ` [${enhancedData.currentFileIndex}/${enhancedData.totalFiles}]`
      }

      // 🚀 更新下载不发送到import-progress，避免干扰
      if (!isUpdateDownload) {
        mainWindow.webContents.send('import-progress', logMessage);
      }
    }
  } catch (error) {
    console.error('发送下载进度时出错:', error)
    // 不抛出错误，避免影响下载流程
  }
}

// 注册全局快捷键
function registerGlobalShortcuts() {
  // 不自动检查剪贴板，让用户手动粘贴
  // globalShortcut.register('CommandOrControl+V', () => {
  //   if (mainWindow && mainWindow.isFocused()) {
  //     checkClipboardForDraftUrl()
  //   }
  // })

  // Ctrl+Enter 快速导入
  globalShortcut.register('CommandOrControl+Enter', () => {
    if (mainWindow && mainWindow.isFocused()) {
      mainWindow.webContents.send('quick-import')
    }
  })
}

// 检查剪贴板中的草稿链接
function checkClipboardForDraftUrl() {
  try {
    const clipboardText = clipboard.readText()
    if (validateDraftUrl(clipboardText)) {
      mainWindow.webContents.send('clipboard-draft-detected', clipboardText)
    }
  } catch (error) {
    console.error('检查剪贴板失败:', error)
  }
}

// 从URL中提取草稿链接（支持网页URL参数）
function extractDraftUrl(inputUrl) {
  if (!inputUrl || typeof inputUrl !== 'string') return null

  // 如果是网页URL，提取draft参数
  if (inputUrl.includes('aigcview.com/JianYingDraft') && inputUrl.includes('draft=')) {
    try {
      const url = new URL(inputUrl)
      const draftParam = url.searchParams.get('draft')
      if (draftParam) {
        console.log('从网页URL提取draft参数:', draftParam)
        return decodeURIComponent(draftParam)
      }
    } catch (error) {
      console.error('解析网页URL失败:', error)
    }
  }

  // 如果直接是草稿URL，直接返回
  return inputUrl
}

// 验证草稿URL
function validateDraftUrl(inputUrl) {
  if (!inputUrl || typeof inputUrl !== 'string') return false

  // 先提取真实的草稿URL
  const draftUrl = extractDraftUrl(inputUrl)
  if (!draftUrl) return false

  // 支持火山引擎TOS的双斜杠格式（这是正常的）
  // 匹配: https://任意子域名.volces.com/一个或多个斜杠/jianying-assistant/drafts/路径/文件名.json|zip
  const tosPattern = /^https:\/\/.*\.volces\.com\/+jianying-assistant\/drafts\/.+\.(json|zip)$/

  // 支持新的系统URL格式（支持两种路径格式）
  // 匹配: https://aigcview.cn/jeecg-boot/sys/common/jianying-file/jianying-assistant/drafts/路径/文件名.json|zip
  const systemPattern = /^https:\/\/aigcview\.cn\/jeecg-boot\/sys\/common\/jianying-file\/jianying-assistant\/drafts\/.+\.(json|zip)$/

  // 🔧 兼容旧的静态文件路径格式
  // 匹配: https://aigcview.cn/jeecg-boot/sys/common/static/jianying-assistant/drafts/路径/文件名.json|zip
  const staticPattern = /^https:\/\/aigcview\.cn\/jeecg-boot\/sys\/common\/static\/jianying-assistant\/drafts\/.+\.(json|zip)$/

  // 调试信息
  console.log('原始输入URL:', inputUrl)
  console.log('提取的草稿URL:', draftUrl)
  console.log('TOS正则表达式:', tosPattern.toString())
  console.log('系统正则表达式:', systemPattern.toString())

  const tosResult = tosPattern.test(draftUrl)
  const systemResult = systemPattern.test(draftUrl)
  const staticResult = staticPattern.test(draftUrl)
  const finalResult = tosResult || systemResult || staticResult

  console.log('TOS验证结果:', tosResult)
  console.log('系统验证结果:', systemResult)
  console.log('静态文件验证结果:', staticResult)
  console.log('最终验证结果:', finalResult)

  // 额外的调试信息
  if (systemResult) {
    console.log('✅ 识别为系统URL格式（jianying-file）')
  } else if (staticResult) {
    console.log('✅ 识别为静态文件URL格式（static）')
  } else if (tosResult) {
    console.log('✅ 识别为TOS URL格式')
  } else {
    console.log('❌ 未识别的URL格式')
  }

  return finalResult
}

// 检查URL是否为ZIP文件
function isZipUrl(url) {
  return url && url.endsWith('.zip')
}

// IPC 事件处理

// 防止重复注册IPC处理器
function safeRegisterIpcHandler(channel, handler) {
  // 先移除可能存在的处理器
  ipcMain.removeHandler(channel);
  // 注册新的处理器
  ipcMain.handle(channel, handler);
}

// === 版本检查相关 ===
// 手动检查更新
safeRegisterIpcHandler('check-for-updates', async () => {
  try {
    console.log('收到手动检查更新请求')
    await updateManager.checkForUpdates(true)
    return { success: true }
  } catch (error) {
    console.error('手动检查更新失败:', error)
    return { success: false, error: error.message }
  }
})

// 获取版本信息
safeRegisterIpcHandler('get-version-info', async () => {
  try {
    const status = updateManager.getUpdateStatus()
    return { success: true, data: status }
  } catch (error) {
    console.error('获取版本信息失败:', error)
    return { success: false, error: error.message }
  }
})

// 更新用户偏好设置
safeRegisterIpcHandler('update-version-preferences', async (event, preferences) => {
  try {
    updateManager.versionChecker.updateUserPreferences(preferences)
    updateManager.updateBackgroundCheckTimer()
    return { success: true }
  } catch (error) {
    console.error('更新偏好设置失败:', error)
    return { success: false, error: error.message }
  }
})

// 获取版本检查日志
safeRegisterIpcHandler('get-version-logs', async (event, limit = 100, level = null) => {
  try {
    const logs = updateManager.versionChecker.getLogs(limit, level)
    return { success: true, data: logs }
  } catch (error) {
    console.error('获取版本日志失败:', error)
    return { success: false, error: error.message }
  }
})

// 清除版本检查日志
safeRegisterIpcHandler('clear-version-logs', async () => {
  try {
    updateManager.versionChecker.clearLogs()
    return { success: true }
  } catch (error) {
    console.error('清除版本日志失败:', error)
    return { success: false, error: error.message }
  }
})

// 导出版本检查日志
safeRegisterIpcHandler('export-version-logs', async () => {
  try {
    const logsJson = updateManager.versionChecker.exportLogs()
    return { success: true, data: logsJson }
  } catch (error) {
    console.error('导出版本日志失败:', error)
    return { success: false, error: error.message }
  }
})

// === 剪映相关 ===
// 获取剪映草稿文件夹路径
safeRegisterIpcHandler('get-jianying-path', async () => {
  return store.get('jianyingDraftsPath', '')
})

// 设置剪映草稿文件夹路径
safeRegisterIpcHandler('set-jianying-path', async (event, jianyingPath) => {
  if (jianyingPath && fs.existsSync(jianyingPath)) {
    store.set('jianyingDraftsPath', jianyingPath)
    return { success: true }
  }
  return { success: false, error: '路径不存在' }
})

// === 应用设置相关 ===
// 获取自动打开文件夹设置
safeRegisterIpcHandler('get-auto-open-folder', async () => {
  return store.get('autoOpenFolder', true) // 默认开启
})

// 设置自动打开文件夹
safeRegisterIpcHandler('set-auto-open-folder', async (event, enabled) => {
  console.log('主进程：设置自动打开文件夹:', enabled)
  store.set('autoOpenFolder', enabled)
  return { success: true, enabled }
})

// 选择剪映草稿文件夹
safeRegisterIpcHandler('select-jianying-path', async () => {
  try {
    console.log('主进程：开始选择剪映路径...')
    const result = await dialog.showOpenDialog(mainWindow, {
      title: '请选择剪映草稿文件夹',
      properties: ['openDirectory']
    })

    console.log('主进程：对话框结果:', result)

    if (!result.canceled && result.filePaths.length > 0) {
      const selectedPath = result.filePaths[0]
      console.log('主进程：选择的路径:', selectedPath)

      store.set('jianyingDraftsPath', selectedPath)
      console.log('主进程：路径已保存到store')

      return { success: true, path: selectedPath }
    }

    console.log('主进程：用户取消选择')
    return { success: false, canceled: true }
  } catch (error) {
    console.error('主进程：选择路径异常:', error)
    return { success: false, error: error.message }
  }
})

// 验证草稿URL
safeRegisterIpcHandler('validate-draft-url', async (event, url) => {
  return validateDraftUrl(url)
})

// 下载草稿文件（支持JSON和ZIP，使用TOS SDK，带重试机制）
safeRegisterIpcHandler('download-draft', async (event, inputParams) => {
  console.log('=== IPC download-draft 被调用 ===')

  // 🆕 支持新的参数格式和向后兼容
  let inputUrl, currentFileIndex, totalFiles
  if (typeof inputParams === 'string') {
    // 向后兼容：如果传入的是字符串，就是URL
    inputUrl = inputParams
    currentFileIndex = 1
    totalFiles = 1
  } else if (typeof inputParams === 'object' && inputParams.url) {
    // 新格式：包含批量下载信息
    inputUrl = inputParams.url
    currentFileIndex = inputParams.currentFileIndex || 1
    totalFiles = inputParams.totalFiles || 1
  } else {
    throw new Error('无效的参数格式')
  }

  console.log('接收到的URL:', inputUrl)
  console.log('当前文件索引:', currentFileIndex)
  console.log('总文件数:', totalFiles)

  // 发送日志到渲染进程
  mainWindow.webContents.send('import-progress', '🔍 主进程：download-draft被调用')
  mainWindow.webContents.send('import-progress', `📥 接收URL: ${inputUrl}`)
  if (totalFiles > 1) {
    mainWindow.webContents.send('import-progress', `📊 批量下载: ${currentFileIndex}/${totalFiles}`)
  }

  const maxRetries = 3
  let lastError = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`IPC下载尝试 ${attempt}/${maxRetries}`)
      mainWindow.webContents.send('import-progress', `🔄 下载尝试 ${attempt}/${maxRetries}`)

      // 验证URL并提取真实的草稿URL
      if (!validateDraftUrl(inputUrl)) {
        throw new Error('无效的草稿URL格式')
      }

      // 提取真实的草稿URL
      const url = extractDraftUrl(inputUrl)
      console.log('实际下载URL:', url)

      const isZip = isZipUrl(url)
      const fileExtension = isZip ? 'zip' : 'json'
      const fileName = `draft_${Date.now()}.${fileExtension}`
      const tempDir = app.getPath('temp')
      const savePath = path.join(tempDir, fileName)

      // 🆕 使用统一的URL处理函数
      const urlInfo = normalizeUrlForDownload(url)

      // 🆕 构建批量下载信息
      const batchInfo = totalFiles > 1 ? {
        currentFileIndex: currentFileIndex,
        totalFiles: totalFiles
      } : null

      switch (urlInfo.type) {
        case 'cdn_url':
          // CDN URL：直接HTTP下载
          console.log('检测到CDN URL，使用HTTP下载')
          mainWindow.webContents.send('import-progress', '🚀 使用CDN下载')
          await downloadFileViaHttp(urlInfo.url, savePath, fileName, batchInfo)
          break

        case 'system_url':
          // 🔒 剪映系统URL：直接使用TOS SDK
          console.log('检测到剪映系统URL，直接使用TOS SDK下载')
          mainWindow.webContents.send('import-progress', '🔒 使用剪映专用桶下载')
          await downloadFileViaTosSDK(urlInfo.objectKey, savePath, fileName, batchInfo)
          break

        case 'static_url':
          // 🔒 静态文件路径：直接使用TOS SDK
          console.log('检测到静态路径的剪映文件，直接使用TOS SDK下载')
          mainWindow.webContents.send('import-progress', '🔒 使用剪映专用桶下载（兼容模式）')
          await downloadFileViaTosSDK(urlInfo.objectKey, savePath, fileName, batchInfo)
          break

        case 'tos_url':
        default:
          // TOS URL或相对路径：直接使用TOS SDK
          console.log('检测到TOS URL或相对路径，使用TOS SDK下载')
          mainWindow.webContents.send('import-progress', '🔒 使用TOS SDK下载')
          await downloadFileViaTosSDK(urlInfo.objectKey, savePath, fileName, batchInfo)
          break
      }

      // 模拟进度更新
      mainWindow.webContents.send('download-progress', 100)

      return { success: true, path: savePath, isZip: isZip }

    } catch (error) {
      console.error(`IPC下载失败 (尝试 ${attempt}/${maxRetries}):`)
      console.error('错误类型:', typeof error)
      console.error('错误对象:', error)
      console.error('错误消息:', error.message)
      console.error('错误堆栈:', error.stack)
      console.error('错误代码:', error.code)
      console.error('错误名称:', error.name)

      lastError = error

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        const waitTime = attempt * 2000
        console.log(`等待 ${waitTime}ms 后重试...`)
        mainWindow.webContents.send('import-progress', `⏳ 等待 ${waitTime/1000}秒后重试...`)
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }
  }

  // 所有重试都失败了
  console.error('IPC下载所有重试都失败了，最后的错误:', lastError.message)
  throw new Error('下载失败: ' + lastError.message)
})

// 解压ZIP文件
async function extractZipFile(zipPath, extractDir) {
  return new Promise((resolve, reject) => {
    yauzl.open(zipPath, { lazyEntries: true }, (err, zipfile) => {
      if (err) {
        reject(err)
        return
      }

      zipfile.readEntry()

      zipfile.on('entry', (entry) => {
        if (/\/$/.test(entry.fileName)) {
          // 目录条目
          const dirPath = path.join(extractDir, entry.fileName)
          fs.mkdir(dirPath, { recursive: true }, (err) => {
            if (err) {
              reject(err)
              return
            }
            zipfile.readEntry()
          })
        } else {
          // 文件条目
          zipfile.openReadStream(entry, (err, readStream) => {
            if (err) {
              reject(err)
              return
            }

            const filePath = path.join(extractDir, entry.fileName)
            const fileDir = path.dirname(filePath)

            fs.mkdir(fileDir, { recursive: true }, (err) => {
              if (err) {
                reject(err)
                return
              }

              const writeStream = fs.createWriteStream(filePath)
              readStream.pipe(writeStream)

              writeStream.on('close', () => {
                zipfile.readEntry()
              })

              writeStream.on('error', (err) => {
                reject(err)
              })
            })
          })
        }
      })

      zipfile.on('end', () => {
        resolve()
      })

      zipfile.on('error', (err) => {
        reject(err)
      })
    })
  })
}

// 导入草稿到剪映（支持JSON和ZIP）
safeRegisterIpcHandler('import-to-jianying', async (event, draftPath, isZip = false) => {
  try {
    const jianyingDraftsPath = store.get('jianyingDraftsPath')
    if (!jianyingDraftsPath || !fs.existsSync(jianyingDraftsPath)) {
      throw new Error('剪映草稿文件夹未设置或不存在')
    }

    if (isZip) {
      // 处理ZIP文件
      mainWindow.webContents.send('import-progress', '正在解压草稿包...')

      // 创建临时解压目录
      const tempExtractDir = path.join(app.getPath('temp'), `extract_${Date.now()}`)
      await fs.promises.mkdir(tempExtractDir, { recursive: true })

      // 解压ZIP文件
      await extractZipFile(draftPath, tempExtractDir)

      // 查找解压后的草稿文件夹（第一个子目录）
      const extractedItems = await fs.promises.readdir(tempExtractDir)
      const draftFolderName = extractedItems.find(item => {
        const itemPath = path.join(tempExtractDir, item)
        return fs.statSync(itemPath).isDirectory()
      })

      if (!draftFolderName) {
        throw new Error('ZIP文件中未找到草稿文件夹')
      }

      const extractedDraftPath = path.join(tempExtractDir, draftFolderName)

      // 移动到剪映草稿目录
      const targetProjectDir = path.join(jianyingDraftsPath, draftFolderName)

      // 如果目标目录已存在，添加时间戳后缀
      let finalTargetDir = targetProjectDir
      if (fs.existsSync(targetProjectDir)) {
        finalTargetDir = path.join(jianyingDraftsPath, `${draftFolderName}_${Date.now()}`)
      }

      mainWindow.webContents.send('import-progress', '正在导入到剪映...')

      // 移动整个文件夹
      await fs.promises.rename(extractedDraftPath, finalTargetDir)

      // 清理临时目录
      await fs.promises.rmdir(tempExtractDir, { recursive: true })

      // 添加到历史记录
      addToImportHistory(draftPath, finalTargetDir)

      return { success: true, projectPath: finalTargetDir }

    } else {
      // 处理JSON文件（支持URL和本地文件）
      console.log('=== 开始处理JSON文件 ===')
      console.log('输入路径:', draftPath)
      mainWindow.webContents.send('import-progress', '正在处理草稿JSON...')

      let draftJson
      let isUrl = draftPath.startsWith('http://') || draftPath.startsWith('https://')
      console.log('是否为URL:', isUrl)

      if (isUrl) {
        // 下载JSON文件
        console.log('开始下载JSON文件...')
        mainWindow.webContents.send('import-progress', `📥 开始下载草稿JSON: ${draftPath}`)
        const startTime = Date.now()
        draftJson = await downloadJsonFile(draftPath)
        const downloadTime = Date.now() - startTime
        console.log('JSON下载完成')
        mainWindow.webContents.send('import-progress', `✅ 草稿JSON下载完成 (耗时: ${downloadTime}ms, 大小: ${JSON.stringify(draftJson).length} 字符)`)
      } else {
        // 读取本地JSON文件
        console.log('读取本地JSON文件...')
        const jsonContent = await fs.promises.readFile(draftPath, 'utf8')
        draftJson = JSON.parse(jsonContent)
        console.log('本地JSON读取完成')
      }

      console.log('开始创建草稿文件夹结构...')
      // 根据JSON内容创建完整的草稿文件夹结构
      const projectPath = await createDraftFromJson(draftJson, jianyingDraftsPath)
      console.log('草稿文件夹创建完成:', projectPath)

      // 添加到历史记录
      addToImportHistory(draftPath, projectPath)

      return { success: true, projectPath: projectPath }
    }

  } catch (error) {
    return { success: false, error: error.message }
  }
})

// 添加到导入历史
function addToImportHistory(draftPath, projectPath) {
  console.log('添加历史记录:')
  console.log('  draftPath:', draftPath)
  console.log('  projectPath:', projectPath)

  const history = store.get('importHistory', [])

  // 从项目路径中提取文件夹名称作为项目名称
  const path = require('path')
  const projectFolderName = path.basename(projectPath)

  // 检查是否已存在相同的项目路径，避免重复添加
  const existingIndex = history.findIndex(item => item.projectPath === projectPath)
  if (existingIndex !== -1) {
    console.log('  项目已存在于历史记录中，更新时间戳')
    history[existingIndex].timestamp = Date.now()
    // 移动到最前面
    const updatedItem = history.splice(existingIndex, 1)[0]
    history.unshift(updatedItem)
  } else {
    const newItem = {
      draftPath,
      projectPath,
      timestamp: Date.now(),
      name: projectFolderName // 使用实际的文件夹名称
    }

    console.log('  新记录:', JSON.stringify(newItem, null, 2))
    history.unshift(newItem)
  }

  // 只保留最近20条记录
  store.set('importHistory', history.slice(0, 20))
}

// 获取导入历史
safeRegisterIpcHandler('get-import-history', async () => {
  const history = store.get('importHistory', [])

  // 清理无效的历史记录（路径不存在的）
  const validHistory = history.filter(item => {
    if (!item.projectPath || !fs.existsSync(item.projectPath)) {
      console.log('清理无效历史记录:', item.projectPath)
      return false
    }
    return true
  })

  // 如果清理了记录，更新存储
  if (validHistory.length !== history.length) {
    console.log(`清理了 ${history.length - validHistory.length} 条无效记录`)
    store.set('importHistory', validHistory)
  }

  console.log('获取历史记录:', JSON.stringify(validHistory, null, 2))
  return validHistory
})

// 打开外部链接
safeRegisterIpcHandler('open-external', async (event, url) => {
  shell.openExternal(url)
})

// 显示消息框
safeRegisterIpcHandler('show-message', async (event, options) => {
  return dialog.showMessageBox(mainWindow, options)
})

// 打开项目文件夹
safeRegisterIpcHandler('open-project-folder', async (event, projectPath) => {
  console.log('主进程：尝试打开项目文件夹:', projectPath)
  console.log('主进程：路径类型:', typeof projectPath)
  console.log('主进程：路径长度:', projectPath ? projectPath.length : 'null')

  try {
    // 检查路径参数
    if (!projectPath || projectPath.trim() === '') {
      console.error('主进程：路径为空或未定义')
      return { success: false, error: '路径为空' }
    }

    // 清理路径（去除可能的引号和空格）
    const cleanPath = projectPath.trim().replace(/^["']|["']$/g, '')
    console.log('主进程：清理后的路径:', cleanPath)

    // 检查路径是否存在
    if (!fs.existsSync(cleanPath)) {
      console.error('主进程：路径不存在:', cleanPath)
      return { success: false, error: '文件夹不存在，可能已被删除' }
    }

    console.log('主进程：路径存在，尝试打开...')

    // 尝试打开路径
    const result = await shell.openPath(cleanPath)

    if (result) {
      // 如果有错误信息，说明打开失败
      console.error('主进程：打开路径失败:', result)
      return { success: false, error: '无法打开文件夹' }
    } else {
      console.log('主进程：路径打开成功')
      return { success: true, message: '已打开项目文件夹' }
    }

  } catch (error) {
    console.error('主进程：打开项目失败:', error)
    return { success: false, error: error.message }
  }
})

// === 版本更新相关 ===
// 应用退出（防重复处理）
let isQuitting = false;
ipcMain.on('app-quit', () => {
  if (isQuitting) {
    console.log('已经在退出过程中，忽略重复请求');
    return;
  }

  isQuitting = true;
  console.log('收到应用退出请求');

  // 关闭所有窗口
  BrowserWindow.getAllWindows().forEach(window => {
    if (!window.isDestroyed()) {
      window.close();
    }
  });

  // 强制退出应用
  app.quit();

  // 在开发环境下，如果app.quit()不起作用，使用process.exit()
  setTimeout(() => {
    console.log('强制退出进程');
    process.exit(0);
  }, 1000);
});

// 启用窗口关闭按钮
ipcMain.on('enable-window-close', () => {
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.setClosable(true);
    console.log('已启用窗口关闭按钮');
  }
});

// 获取版本信息
safeRegisterIpcHandler('get-version-info', async () => {
  try {
    const currentVersion = app.getVersion();
    console.log('主进程获取版本号:', currentVersion);

    const result = {
      success: true,
      data: {
        currentVersion: currentVersion,
        userPreferences: updateManager.versionChecker.getUserPreferences(),
        lastCheckTime: updateManager.versionChecker.store.get('lastCheckTime', 0)
      }
    };

    console.log('返回版本信息:', result);
    return result;
  } catch (error) {
    console.error('获取版本信息时出错:', error);
    return {
      success: false,
      error: error.message
    };
  }
});

// 更新版本偏好设置
safeRegisterIpcHandler('update-version-preferences', async (event, preferences) => {
  try {
    updateManager.versionChecker.updateUserPreferences(preferences);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// 打开外部链接
safeRegisterIpcHandler('open-external', async (event, url) => {
  try {
    await shell.openExternal(url);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// 🚀 应用内下载更新文件
safeRegisterIpcHandler('download-update-file', async (event, updateInfo) => {
  try {
    console.log('🚀 开始下载更新文件:', updateInfo);
    console.log('🖥️ 当前系统:', os.platform());

    const { downloadUrl, version, fileName } = updateInfo;

    // 下载到用户的下载目录
    const downloadDir = path.join(os.homedir(), 'Downloads');
    const filePath = path.join(downloadDir, fileName);

    console.log('📁 更新文件保存路径:', filePath);

    // 🚀 发送下载开始消息到更新弹窗
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('download-progress', {
        status: 'starting',
        fileName: fileName,
        progress: 0
      });
    }

    // 使用现有的HTTP下载功能，传递更新标识
    await downloadFileViaHttp(downloadUrl, filePath, fileName, {
      isUpdate: true,
      version: version,
      totalFiles: 1,
      currentFileIndex: 1
    });

    console.log('✅ 更新文件下载完成:', filePath);

    // 🚀 发送下载完成消息到更新弹窗
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('download-complete', {
        filePath: filePath,
        fileName: fileName
      });
    }

    return { success: true, filePath: filePath };

  } catch (error) {
    console.error('❌ 下载更新文件失败:', error);

    // 发送下载失败消息
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('import-progress', `❌ 下载失败: ${error.message}`);
    }

    return { success: false, error: error.message };
  }
});

// 🚀 安装提示对话框已移除 - 直接安装，无需用户确认

// 🚀 打开安装文件
safeRegisterIpcHandler('open-installer', async (event, filePath) => {
  try {
    const fs = require('fs');

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      throw new Error('安装文件不存在');
    }

    // 打开安装文件
    await shell.openPath(filePath);

    console.log('✅ 已打开安装文件:', filePath);
    return { success: true };

  } catch (error) {
    console.error('❌ 打开安装文件失败:', error);
    return { success: false, error: error.message };
  }
});

// 备用HTTP下载方法（使用curl）
async function downloadFileViaCurl(url, savePath) {
  const { spawn } = require('child_process')
  const fs = require('fs')

  return new Promise((resolve, reject) => {
    console.log('使用curl下载文件:', url)

    // 使用curl命令下载
    const curl = spawn('curl', [
      '-L', // 跟随重定向
      '-k', // 忽略SSL证书错误
      '--connect-timeout', '30',
      '--max-time', '300',
      '-o', savePath,
      url
    ])

    curl.stdout.on('data', (data) => {
      console.log('curl输出:', data.toString())
    })

    curl.stderr.on('data', (data) => {
      console.log('curl错误:', data.toString())
    })

    curl.on('close', (code) => {
      if (code === 0) {
        console.log('curl下载成功')
        resolve({ success: true })
      } else {
        console.error('curl下载失败，退出码:', code)
        fs.unlink(savePath, () => {}) // 删除不完整的文件
        reject(new Error(`curl下载失败，退出码: ${code}`))
      }
    })

    curl.on('error', (err) => {
      console.error('curl执行错误:', err)
      fs.unlink(savePath, () => {}) // 删除不完整的文件
      reject(err)
    })
  })
}

// HTTP下载文件（用于系统URL）- 增强版本，支持进度监听
async function downloadFileViaHttp(url, savePath, fileName = null, batchInfo = null) {
  const https = require('https')
  const http = require('http')
  const fs = require('fs')
  const { URL } = require('url')
  const path = require('path')

  return new Promise((resolve, reject) => {
    console.log('开始HTTP下载系统文件:', url)

    // 提取文件名
    const actualFileName = fileName || path.basename(savePath)

    // 生成下载ID
    const downloadId = `http_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const file = fs.createWriteStream(savePath)
    const parsedUrl = new URL(url)

    // 配置请求选项
    const options = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
      path: parsedUrl.pathname + parsedUrl.search,
      method: 'GET',
      headers: {
        'User-Agent': 'JianyingAssistant/1.0.0',
        'Accept': '*/*',
        'Connection': 'close'
      },
      // 添加TLS选项来处理证书问题
      rejectUnauthorized: false, // 暂时忽略证书验证
      timeout: 30000
    }

    console.log('请求选项:', options)

    // 选择HTTP或HTTPS模块
    const httpModule = parsedUrl.protocol === 'https:' ? https : http

    // 创建请求
    const request = httpModule.request(options, (response) => {
      console.log('HTTP响应状态码:', response.statusCode)
      console.log('HTTP响应头:', response.headers)

      // 处理重定向
      if (response.statusCode === 301 || response.statusCode === 302) {
        const redirectUrl = response.headers.location
        console.log('检测到重定向:', redirectUrl)
        file.close()
        fs.unlink(savePath, () => {}) // 删除空文件

        // 递归处理重定向
        downloadFileViaHttp(redirectUrl, savePath, fileName)
          .then(resolve)
          .catch(reject)
        return
      }

      if (response.statusCode !== 200) {
        file.close()
        fs.unlink(savePath, () => {}) // 删除空文件
        reject(new Error(`HTTP下载失败，状态码: ${response.statusCode}, 响应: ${response.statusMessage}`))
        return
      }

      // 获取文件总大小
      const totalBytes = parseInt(response.headers['content-length'] || '0')
      console.log(`文件总大小: ${downloadProgressCalculator.formatFileSize(totalBytes)}`)

      // 开始进度跟踪
      const initialProgress = downloadProgressCalculator.startDownload(downloadId, actualFileName, totalBytes, 'http')

      // 立即发送初始进度
      if (initialProgress) {
        console.log('📊 HTTP下载开始:', initialProgress)
        sendDownloadProgress(initialProgress, batchInfo)
      }

      let downloadedBytes = 0
      let lastProgressUpdate = Date.now()

      // 监听数据流，计算进度
      response.on('data', (chunk) => {
        downloadedBytes += chunk.length

        // 每500ms更新一次进度，避免过于频繁
        const now = Date.now()
        if (now - lastProgressUpdate >= 500) {
          const progressInfo = downloadProgressCalculator.updateProgress(downloadId, downloadedBytes)
          if (progressInfo) {
            sendDownloadProgress(progressInfo, batchInfo)
          }
          lastProgressUpdate = now
        }
      })

      // 开始下载文件内容
      response.pipe(file)

      file.on('finish', () => {
        file.close()
        console.log('HTTP下载完成:', savePath)

        // 发送最终进度
        const finalProgress = downloadProgressCalculator.completeDownload(downloadId)
        if (finalProgress) {
          sendDownloadProgress(finalProgress, batchInfo)
        }

        resolve({ success: true })
      })

      file.on('error', (err) => {
        console.error('文件写入错误:', err)
        downloadProgressCalculator.completeDownload(downloadId)
        fs.unlink(savePath, () => {}) // 删除不完整的文件
        reject(err)
      })

    })

    request.on('error', (err) => {
      console.error('HTTP请求错误:', err)
      console.error('错误详情:', {
        code: err.code,
        message: err.message,
        hostname: options.hostname,
        port: options.port
      })
      downloadProgressCalculator.completeDownload(downloadId)
      file.close()
      fs.unlink(savePath, () => {}) // 删除空文件
      reject(err)
    })

    request.on('timeout', () => {
      console.error('HTTP请求超时')
      downloadProgressCalculator.completeDownload(downloadId)
      request.destroy()
      file.close()
      fs.unlink(savePath, () => {}) // 删除空文件
      reject(new Error('HTTP请求超时'))
    })

    // 发送请求
    request.end()
  })
}

// 🆕 统一处理URL转换为TOS对象键或完整URL
function normalizeUrlForDownload(inputUrl) {
  try {
    console.log('处理URL:', inputUrl)

    // 1. 检查是否为相对路径（以/开头但不是完整URL）
    if (inputUrl.startsWith('/') && !inputUrl.startsWith('http')) {
      // 相对路径，拼接TOS前缀生成完整URL
      const objectKey = inputUrl.startsWith('/') ? inputUrl.substring(1) : inputUrl
      const fullTosUrl = `${tosUrlPrefix}/${objectKey}`
      console.log('相对路径转换为完整TOS URL:', fullTosUrl)
      return {
        type: 'tos_url',
        url: fullTosUrl,
        objectKey: objectKey
      }
    }

    // 2. 检查是否为CDN URL
    if (inputUrl.includes('cdn.aigcview.com')) {
      console.log('检测到CDN URL')
      return {
        type: 'cdn_url',
        url: inputUrl,
        objectKey: null
      }
    }

    // 3. 检查是否为系统URL
    if (inputUrl.includes('aigcview.cn') && inputUrl.includes('/sys/common/jianying-file/')) {
      const systemPath = inputUrl.substring(inputUrl.indexOf('/sys/common/jianying-file/') + '/sys/common/jianying-file'.length)
      console.log('系统URL提取对象键:', systemPath)
      return {
        type: 'system_url',
        url: inputUrl,
        objectKey: systemPath
      }
    }

    // 4. 检查是否为静态文件URL
    if (inputUrl.includes('aigcview.cn') && inputUrl.includes('/sys/common/static/')) {
      const staticPath = inputUrl.substring(inputUrl.indexOf('/sys/common/static/') + '/sys/common/static'.length)
      console.log('静态文件URL提取对象键:', staticPath)
      return {
        type: 'static_url',
        url: inputUrl,
        objectKey: staticPath
      }
    }

    // 5. 检查是否为外部URL（如火山引擎、其他CDN等）
    if (inputUrl.startsWith('http') && !inputUrl.includes('aigcview.cn')) {
      console.log('检测到外部URL，使用HTTP下载')
      return {
        type: 'cdn_url',  // 使用cdn_url类型进行HTTP下载
        url: inputUrl,
        objectKey: null
      }
    }

    // 6. 完整TOS URL，需要解析
    const url = new URL(inputUrl)
    let path = url.pathname
    if (path.startsWith('/')) {
      path = path.substring(1)
    }
    console.log('完整TOS URL提取对象键:', path)
    return {
      type: 'tos_url',
      url: inputUrl,
      objectKey: path
    }

  } catch (error) {
    console.error('URL处理失败:', inputUrl, error)
    throw new Error('URL处理失败: ' + inputUrl)
  }
}

// 从URL中提取对象键的工具函数（支持TOS URL和系统URL）- 保留兼容性
function extractObjectKeyFromUrl(fileUrl) {
  const result = normalizeUrlForDownload(fileUrl)
  return result.objectKey
}

// 下载JSON文件（使用TOS SDK，带重试机制）
async function downloadJsonFile(url) {
  const maxRetries = 3
  let lastError = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`开始下载JSON文件 (尝试 ${attempt}/${maxRetries}):`, url)

      // 🆕 使用统一的URL处理函数
      const urlInfo = normalizeUrlForDownload(url)
      const objectKey = urlInfo.objectKey
      console.log('对象键:', objectKey)
      // TOS配置信息不在控制台输出（包含敏感信息）

      // 使用TOS SDK下载文件（使用官方推荐的getObjectV2方法）
      console.log('调用TOS SDK getObjectV2...')
      const getObjectInput = {
        bucket: tosConfig.bucket,
        key: objectKey
      }
      console.log('getObjectV2参数: bucket=[隐藏], key:', objectKey)

      const result = await tosClient.getObjectV2(getObjectInput)
      console.log('TOS SDK调用成功，返回类型:', typeof result)

      console.log('TOS下载成功，开始读取内容...')
      console.log('result结构:', Object.keys(result))
      console.log('result类型:', typeof result)

      // 根据官方文档，getObjectV2返回 { data: { content } }，其中content是stream
      if (!result.data || !result.data.content) {
        console.error('TOS返回的数据格式不正确:', result)
        throw new Error('TOS返回数据格式不正确，期望 { data: { content: stream } }')
      }

      console.log('开始读取stream内容...')
      const content = result.data.content

      // 按照官方文档的方式读取stream
      let allContent = Buffer.from([])
      for await (const chunk of content) {
        allContent = Buffer.concat([allContent, chunk])
      }

      const jsonString = allContent.toString('utf8')

      console.log('下载的原始数据长度:', jsonString.length)
      console.log('下载的原始数据前100字符:', jsonString.substring(0, 100))

      // 解析JSON
      const jsonData = JSON.parse(jsonString)
      console.log('JSON文件下载成功，大小:', jsonString.length, '字符')

      return jsonData

    } catch (error) {
      console.error(`TOS下载失败 (尝试 ${attempt}/${maxRetries}):`, error.message)
      lastError = error

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries) {
        const waitTime = attempt * 2000 // 递增等待时间：2秒、4秒
        console.log(`等待 ${waitTime}ms 后重试...`)
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }
  }

  // 所有重试都失败了
  console.error('所有重试都失败了，最后的错误:', lastError.message)
  throw new Error('下载失败: ' + lastError.message)
}

// 根据JSON内容创建完整的草稿文件夹结构
async function createDraftFromJson(draftJson, jianyingDraftsPath) {
  try {
    console.log('开始根据JSON创建草稿文件夹结构')

    // 生成项目文件夹名称
    const projectId = draftJson.id || `zj_${Date.now()}`
    const projectDir = path.join(jianyingDraftsPath, projectId)

    // 创建项目目录（使用改进的方法）
    createDraftFolderWithCorrectProperties(projectDir)
    console.log('创建项目目录:', projectDir)

    // 1. 先创建基础草稿文件（保留原始字段用于下载）
    await createBaseDraftFiles(projectDir, projectId, draftJson)

    // 2. 再处理素材文件（音频、视频、图片等）
    await processMaterialFiles(projectDir, draftJson)

    // 3. 最后更新草稿文件（删除多余字段，生成最终版本）
    await updateFinalDraftFiles(projectDir, projectId, draftJson)

    console.log('草稿文件夹结构创建完成:', projectDir)
    return projectDir

  } catch (error) {
    console.error('创建草稿文件夹结构失败:', error)
    throw error
  }
}

// 完全基于竞争对手格式的草稿文件更新函数
async function updateFinalDraftFiles(projectDir, projectId, draftJson) {
  try {
    console.log('=== 开始生成竞争对手格式的草稿文件 ===')

    // 生成动态UUID（保持格式一致）
    function generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }

    // 生成动态ID
    const dynamicDraftId = generateUUID()
    const dynamicAudioId = generateUUID()
    const dynamicMetaDraftId = generateUUID().toUpperCase().replace(/-/g, '-').replace(/^(.{8})-(.{4})-(.{4})-(.{4})-(.{12})$/, '$1-$2-$3-$4-$5')

    console.log('生成的动态ID:')
    console.log('- 草稿ID:', dynamicDraftId)
    console.log('- 音频ID:', dynamicAudioId)
    console.log('- Meta草稿ID:', dynamicMetaDraftId)

    // 获取音频文件夹信息
    const projectDirContents = await fs.promises.readdir(projectDir)
    const materialFolders = projectDirContents.filter(item => {
      const itemPath = path.join(projectDir, item)
      try {
        return fs.statSync(itemPath).isDirectory() &&
               item.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)
      } catch (e) {
        return false
      }
    })

    // 获取音频文件信息
    let audioFileName = 'feebadd2-93e2-4666-8602-a5ec163e68b3.mp3'
    let audioFolderName = '039d399c-4a10-4a7c-bb87-493c4dcc193d'
    
    if (materialFolders.length > 0) {
      audioFolderName = materialFolders[0]
      const audioFolderPath = path.join(projectDir, audioFolderName)
      const audioFiles = await fs.promises.readdir(audioFolderPath)
      const mp3Files = audioFiles.filter(file => file.endsWith('.mp3'))
      if (mp3Files.length > 0) {
        audioFileName = mp3Files[0]
      }
    }

    console.log(`使用音频文件夹: ${audioFolderName}`)
    console.log(`使用音频文件: ${audioFileName}`)

    // 根据操作系统处理路径格式
    console.log('=== 处理跨平台路径格式 ===')
    console.log('当前操作系统:', process.platform)
    console.log('TOS JSON中的materials.videos数量:', draftJson.materials?.videos?.length || 0)
    console.log('TOS JSON中的materials.texts数量:', draftJson.materials?.texts?.length || 0)
    console.log('TOS JSON中的duration:', draftJson.duration)
    console.log('TOS JSON中的canvas_config:', JSON.stringify(draftJson.canvas_config))

    // 根据操作系统处理路径格式
    let competitorFormatJson = draftJson

    if (process.platform === 'darwin') {
      // Mac系统：转换路径分隔符从反斜杠到正斜杠
      console.log('检测到Mac系统，开始转换路径分隔符...')
      competitorFormatJson = convertPathSeparatorsForMac(draftJson)
      console.log('Mac路径转换完成')
    } else if (process.platform === 'win32') {
      // Windows系统：保持原有格式，不做任何修改
      console.log('检测到Windows系统，保持原有路径格式')
      competitorFormatJson = draftJson
    } else {
      // 其他系统：默认使用Mac格式
      console.log('检测到其他系统，使用Mac路径格式')
      competitorFormatJson = convertPathSeparatorsForMac(draftJson)
    }

    // 生成所有JSON文件（使用DraftHelper修正duration）
    const DraftHelper = require('../utils/draftHelper')
    const originalJsonContent = JSON.stringify(competitorFormatJson)
    const fixedJsonContent = DraftHelper.processDraftJsonString(originalJsonContent)

    await fs.promises.writeFile(path.join(projectDir, 'draft_content.json'), fixedJsonContent, 'utf8')
    await fs.promises.writeFile(path.join(projectDir, 'draft_info.json'), fixedJsonContent, 'utf8')
    await fs.promises.writeFile(path.join(projectDir, `${audioFolderName}.json`), fixedJsonContent, 'utf8')

    // 生成 draft_meta_info.json
    const currentTime = Date.now() * 1000

    // 根据操作系统生成正确的路径格式
    let draftFoldPath, draftRootPath
    if (process.platform === 'darwin') {
      // Mac系统路径格式
      draftFoldPath = `/Users/<USER>/Movies/JianyingPro/User Data/Projects/com.lveditor.draft/${projectId}`
      draftRootPath = "/Users/<USER>/Movies/JianyingPro/User Data/Projects/com.lveditor.draft"
      console.log('使用Mac系统路径格式')
    } else {
      // Windows系统路径格式（默认）
      draftFoldPath = `C:/Users/<USER>/AppData/Local/JianyingPro/User Data/Projects/com.lveditor.draft/${projectId}`
      draftRootPath = "C:\\Users\\<USER>\\AppData\\Local\\JianyingPro\\User Data\\Projects\\com.lveditor.draft"
      console.log('使用Windows系统路径格式')
    }
    const metaInfo = {
      "cloud_package_completed_time": "",
      "draft_cloud_capcut_purchase_info": "",
      "draft_cloud_last_action_download": false,
      "draft_cloud_materials": [],
      "draft_cloud_purchase_info": "",
      "draft_cloud_template_id": "",
      "draft_cloud_tutorial_info": "",
      "draft_cloud_videocut_purchase_info": "",
      "draft_cover": "draft_cover.jpg",
      "draft_deeplink_url": "",
      "draft_enterprise_info": {
        "draft_enterprise_extra": "",
        "draft_enterprise_id": "",
        "draft_enterprise_name": "",
        "enterprise_material": []
      },
      "draft_fold_path": draftFoldPath,
      "draft_id": dynamicMetaDraftId,
      "draft_is_ai_packaging_used": false,
      "draft_is_ai_shorts": false,
      "draft_is_ai_translate": false,
      "draft_is_article_video_draft": false,
      "draft_is_from_deeplink": "false",
      "draft_is_invisible": false,
      "draft_materials": [
        {"type": 0, "value": []},
        {"type": 1, "value": []},
        {"type": 2, "value": []},
        {"type": 3, "value": []},
        {"type": 6, "value": []},
        {"type": 7, "value": []},
        {"type": 8, "value": []}
      ],
      "draft_materials_copied_info": [],
      "draft_name": projectId,
      "draft_new_version": "",
      "draft_removable_storage_device": "",
      "draft_root_path": draftRootPath,
      "draft_segment_extra_info": [],
      "draft_timeline_materials_size_": 2075,
      "draft_type": "",
      "tm_draft_cloud_completed": "",
      "tm_draft_cloud_modified": 0,
      "tm_draft_create": currentTime,
      "tm_draft_modified": currentTime,
      "tm_draft_removed": 0,
      "tm_duration": 0
    }
    
    await fs.promises.writeFile(path.join(projectDir, 'draft_meta_info.json'), JSON.stringify(metaInfo), 'utf8')

    console.log('=== 竞争对手格式草稿文件生成完成 ===')

  } catch (error) {
    console.error('生成竞争对手格式草稿文件失败:', error)
    throw error
  }
}

// 创建基础草稿文件

// 创建基础草稿文件
async function createBaseDraftFiles(projectDir, projectId, draftJson) {
  try {
    console.log('创建基础草稿文件')

    // 使用DraftHelper处理JSON，自动修正duration
    const DraftHelper = require('../utils/draftHelper')
    const originalDraftContent = JSON.stringify(draftJson)
    const fixedDraftContent = DraftHelper.processDraftJsonString(originalDraftContent)

    // 创建基础的JSON文件（使用修正后的内容）
    await fs.promises.writeFile(path.join(projectDir, 'draft_content.json'), fixedDraftContent, 'utf8')
    await fs.promises.writeFile(path.join(projectDir, 'draft_info.json'), fixedDraftContent, 'utf8')

    // 创建其他必要的配置文件（匹配竞争对手格式）
    const currentTime = Date.now() * 1000 // 转换为微秒

    // 根据操作系统生成正确的路径格式
    let draftFoldPath2, draftRootPath2
    if (process.platform === 'darwin') {
      // Mac系统路径格式
      draftFoldPath2 = `/Users/<USER>/Movies/JianyingPro/User Data/Projects/com.lveditor.draft/${projectId}`
      draftRootPath2 = "/Users/<USER>/Movies/JianyingPro/User Data/Projects/com.lveditor.draft"
    } else {
      // Windows系统路径格式（默认）
      draftFoldPath2 = `C:/Users/<USER>/AppData/Local/JianyingPro/User Data/Projects/com.lveditor.draft/${projectId}`
      draftRootPath2 = "C:\\Users\\<USER>\\AppData\\Local\\JianyingPro\\User Data\\Projects\\com.lveditor.draft"
    }
    const metaInfo = {
      cloud_package_completed_time: "",
      draft_cloud_capcut_purchase_info: "",
      draft_cloud_last_action_download: false,
      draft_cloud_materials: [],
      draft_cloud_purchase_info: "",
      draft_cloud_template_id: "",
      draft_cloud_tutorial_info: "",
      draft_cloud_videocut_purchase_info: "",
      draft_cover: "draft_cover.jpg",
      draft_deeplink_url: "",
      draft_enterprise_info: {
        draft_enterprise_extra: "",
        draft_enterprise_id: "",
        draft_enterprise_name: "",
        enterprise_material: []
      },
      draft_fold_path: draftFoldPath2,
      draft_id: projectId,
      draft_is_ai_packaging_used: false,
      draft_is_ai_shorts: false,
      draft_is_ai_translate: false,
      draft_is_article_video_draft: false,
      draft_is_from_deeplink: "false",
      draft_is_invisible: false,
      draft_materials: [
        {type: 0, value: []},
        {type: 1, value: []},
        {type: 2, value: []},
        {type: 3, value: []},
        {type: 6, value: []},
        {type: 7, value: []},
        {type: 8, value: []}
      ],
      draft_materials_copied_info: [],
      draft_name: projectId,
      draft_new_version: "",
      draft_removable_storage_device: "",
      draft_root_path: draftRootPath2,
      draft_segment_extra_info: [],
      draft_timeline_materials_size_: 2075,
      draft_type: "",
      tm_draft_cloud_completed: "",
      tm_draft_cloud_modified: 0,
      tm_draft_create: currentTime,
      tm_draft_modified: currentTime,
      tm_draft_removed: 0,
      tm_duration: 0
    }
    await fs.promises.writeFile(path.join(projectDir, 'draft_meta_info.json'), JSON.stringify(metaInfo), 'utf8')

    const agencyConfig = {
      marterials: null,
      use_converter: false,
      video_resolution: 720
    }
    await fs.promises.writeFile(path.join(projectDir, 'draft_agency_config.json'), JSON.stringify(agencyConfig), 'utf8')

    const attachmentConfig = {
      pc_feature_flag: 0,
      template_item_infos: [],
      unlock_template_ids: []
    }
    await fs.promises.writeFile(path.join(projectDir, 'attachment_pc_common.json'), JSON.stringify(attachmentConfig), 'utf8')

    const template = {
      canvas_config: { height: 0, ratio: "original", width: 0 },
      color_space: -1,
      config: {
        adjust_max_index: 1,
        attachment_info: [],
        combination_max_index: 1,
        export_range: null,
        extract_audio_last_index: 1,
        lyrics_recognition_id: "",
        lyrics_sync: true,
        lyrics_taskinfo: [],
        maintrack_adsorb: true,
        material_save_mode: 0,
        original_sound_last_index: 1,
        record_audio_last_index: 1,
        sticker_max_index: 1,
        subtitle_recognition_id: "",
        subtitle_sync: true,
        subtitle_taskinfo: [],
        video_mute: false,
        zoom_info_params: null
      },
      cover: null,
      create_time: 0,
      duration: 0,
      extra_info: null,
      fps: 30,
      free_render_index_mode_on: false,
      group_container: null,
      id: projectId,
      keyframes: {
        adjusts: [],
        audios: [],
        effects: [],
        filters: [],
        handwrites: [],
        stickers: [],
        texts: [],
        videos: []
      },
      last_modified_platform: {
        app_id: 3704,
        app_source: "lv",
        app_version: "3.9.0",
        device_id: "ece93fefa6d73d0879f6cf6251e212fd",
        hard_disk_id: "da04071d52c1f66c291bc2f5b1f0e87f",
        mac_address: "864b94efa06c9456b10992695eba19c4",
        os: "mac",
        os_version: "12.5.1"
      },
      materials: {
        audio_balances: [],
        audio_effects: [],
        audio_fades: [],
        audios: [],
        beats: [],
        canvases: [],
        chromas: [],
        color_curves: [],
        drafts: [],
        effects: [],
        handwrites: [],
        hsl: [],
        images: [],
        log_color_wheels: [],
        manual_deformations: [],
        masks: [],
        material_animations: [],
        placeholders: [],
        plugin_effects: [],
        primary_color_wheels: [],
        realtime_denoises: [],
        sound_channel_mappings: [],
        speeds: [],
        stickers: [],
        tail_leaders: [],
        text_templates: [],
        texts: [],
        transitions: [],
        video_effects: [],
        video_trackings: [],
        videos: []
      },
      mutable_config: null,
      name: "",
      new_version: "69.0.0",
      platform: {
        app_id: 3704,
        app_source: "lv",
        app_version: "3.9.0",
        device_id: "ece93fefa6d73d0879f6cf6251e212fd",
        hard_disk_id: "da04071d52c1f66c291bc2f5b1f0e87f",
        mac_address: "864b94efa06c9456b10992695eba19c4",
        os: "mac",
        os_version: "12.5.1"
      },
      relationships: [],
      render_index_track_mode_on: false,
      retouch_cover: null,
      source: "default",
      static_cover_image_path: "",
      tracks: [],
      update_time: 0,
      version: 360000
    }
    await fs.promises.writeFile(path.join(projectDir, 'template.tmp'), JSON.stringify(template), 'utf8')

    // 创建使用说明文件
    await fs.promises.writeFile(path.join(projectDir, '把当前文件夹放到剪映草稿目录下'), '', 'utf8')

    // 创建素材文件夹结构（与竞争对手一致）
    await createMaterialFolders(projectDir, draftJson)

    console.log('基础草稿文件创建完成')

  } catch (error) {
    console.error('创建基础草稿文件失败:', error)
    throw error
  }
}

// 创建素材文件夹结构（与竞争对手一致）
async function createMaterialFolders(projectDir, draftJson) {
  try {
    console.log('开始创建素材文件夹结构')

    // 检查是否已经存在素材配置文件，如果存在就使用现有的ID
    const projectDirContents = await fs.promises.readdir(projectDir)
    const existingMaterialConfig = projectDirContents.find(file =>
      file.endsWith('.json') &&
      file !== 'draft_content.json' &&
      file !== 'draft_info.json' &&
      file !== 'draft_meta_info.json' &&
      file !== 'attachment_pc_common.json' &&
      file !== 'draft_agency_config.json'
    )

    let materialId
    if (existingMaterialConfig) {
      // 使用现有的素材ID
      materialId = existingMaterialConfig.replace('.json', '')
      console.log(`发现现有素材配置文件，使用ID: ${materialId}`)
    } else {
      // 生成随机UUID作为素材ID（与竞争对手一致）
      function generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
          var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
        });
      }

      materialId = generateUUID()
      console.log(`生成随机素材ID: ${materialId}`)

      // 创建素材配置文件（与草稿内容相同）
      const materialConfigPath = path.join(projectDir, `${materialId}.json`)
      await fs.promises.writeFile(materialConfigPath, JSON.stringify(draftJson), 'utf8')
      console.log(`创建素材配置文件: ${materialId}.json`)
    }

    // 创建对应的空素材文件夹（与竞争对手一致）
    const materialFolderPath = path.join(projectDir, materialId)
    if (!fs.existsSync(materialFolderPath)) {
      await fs.promises.mkdir(materialFolderPath, { recursive: true })
      console.log(`创建空素材文件夹: ${materialId}/`)
    } else {
      console.log(`素材文件夹已存在: ${materialId}/`)
    }

    console.log('素材文件夹结构创建完成')

  } catch (error) {
    console.error('创建素材文件夹结构失败:', error)
    throw error
  }
}

// 处理素材文件（下载音频、视频、图片等）
async function processMaterialFiles(projectDir, draftJson) {
  try {
    console.log('开始处理素材文件')
    console.log('草稿JSON结构:', JSON.stringify(draftJson, null, 2))

    // 检查是否有素材需要下载
    const materials = draftJson.materials
    console.log('素材信息:', materials)

    if (!materials) {
      console.log('没有素材需要处理')
      mainWindow.webContents.send('import-progress', '没有素材需要处理')
      return
    }

    // 计算总文件数量，用于批量下载进度
    let totalFiles = 0
    let allFiles = []

    // 统计音频文件（去重后）
    if (materials.audios && materials.audios.length > 0) {
      const urlMap = new Map()
      materials.audios.forEach(audio => {
        if (audio.download_url) {
          urlMap.set(audio.download_url, audio)
        }
      })
      const uniqueAudios = Array.from(urlMap.values())
      totalFiles += uniqueAudios.length
      allFiles.push(...uniqueAudios.map(a => ({
        fileName: a.file_name || a.name || '音频文件',
        type: 'audio'
      })))
    }

    // 统计视频文件
    if (materials.videos && materials.videos.length > 0) {
      totalFiles += materials.videos.length
      allFiles.push(...materials.videos.map(v => ({
        fileName: v.file_name || v.name || '视频文件',
        type: 'video'
      })))
    }

    // 统计图片文件
    if (materials.images && materials.images.length > 0) {
      totalFiles += materials.images.length
      allFiles.push(...materials.images.map(i => ({
        fileName: i.file_name || i.name || '图片文件',
        type: 'image'
      })))
    }

    // 启动批量下载进度跟踪
    if (totalFiles > 0) {
      console.log(`🔍 [BATCH] 准备启动批量下载: ${totalFiles} 个文件`)
      console.log(`🔍 [BATCH] allFiles内容:`, allFiles)
      console.log(`🔍 [BATCH] batchDownloadManager存在:`, !!batchDownloadManager)
      batchDownloadManager.startBatch(allFiles)
      console.log(`🔍 [BATCH] startBatch调用完成`)
    } else {
      console.log(`🔍 [BATCH] 没有文件需要下载，totalFiles=${totalFiles}`)
    }

    // 全局文件索引计数器
    let globalFileIndex = 0

    // 处理音频素材（根据download_url去重）
    if (materials.audios && materials.audios.length > 0) {
      console.log(`原始音频素材数量: ${materials.audios.length}`)
      mainWindow.webContents.send('import-progress', `🎵 发现 ${materials.audios.length} 个音频素材`)

      // 去重：根据download_url字段去重，保留最后一个
      const urlMap = new Map()
      materials.audios.forEach(audio => {
        if (audio.download_url) {
          urlMap.set(audio.download_url, audio) // 后面的会覆盖前面的
        }
      })

      const uniqueAudios = Array.from(urlMap.values())
      console.log(`去重后音频素材数量: ${uniqueAudios.length}`)

      if (uniqueAudios.length !== materials.audios.length) {
        mainWindow.webContents.send('import-progress', `🔄 去重后剩余 ${uniqueAudios.length} 个音频素材`)
      }

      for (let i = 0; i < uniqueAudios.length; i++) {
        const audio = uniqueAudios[i]
        globalFileIndex++

        // 检查是否有下载链接，显示不同的处理消息
        const hasDownloadUrl = audio.download_url && audio.download_url.trim() !== ''

        if (hasDownloadUrl) {
          mainWindow.webContents.send('import-progress', `🎵 正在处理音频 ${i + 1}/${uniqueAudios.length}...`)
        } else {
          mainWindow.webContents.send('import-progress', `🎵 正在处理音频 ${i + 1}/${uniqueAudios.length}（空链接占位）...`)
        }

        await processSingleMaterial(projectDir, audio, 'audio', globalFileIndex, totalFiles)
      }
    }

    // 处理视频素材
    if (materials.videos && materials.videos.length > 0) {
      mainWindow.webContents.send('import-progress', `📹 发现 ${materials.videos.length} 个视频素材`)

      for (let i = 0; i < materials.videos.length; i++) {
        const video = materials.videos[i]
        globalFileIndex++

        // 检查是否有下载链接，显示不同的处理消息
        const hasDownloadUrl = video.download_url && video.download_url.trim() !== ''

        if (hasDownloadUrl) {
          mainWindow.webContents.send('import-progress', `📹 正在处理视频 ${i + 1}/${materials.videos.length}...`)
        } else {
          mainWindow.webContents.send('import-progress', `📹 正在处理视频 ${i + 1}/${materials.videos.length}（空链接占位）...`)
        }

        await processSingleMaterial(projectDir, video, 'video', globalFileIndex, totalFiles)
      }
    }

    // 处理图片素材
    if (materials.images && materials.images.length > 0) {
      mainWindow.webContents.send('import-progress', `🖼️ 发现 ${materials.images.length} 个图片素材`)

      for (let i = 0; i < materials.images.length; i++) {
        const image = materials.images[i]
        globalFileIndex++

        // 检查是否有下载链接，显示不同的处理消息
        const hasDownloadUrl = image.download_url && image.download_url.trim() !== ''

        if (hasDownloadUrl) {
          mainWindow.webContents.send('import-progress', `🖼️ 正在处理图片 ${i + 1}/${materials.images.length}...`)
        } else {
          mainWindow.webContents.send('import-progress', `🖼️ 正在处理图片 ${i + 1}/${materials.images.length}（空链接占位）...`)
        }

        await processSingleMaterial(projectDir, image, 'image', globalFileIndex, totalFiles)
      }
    }

    console.log('素材文件处理完成')
    mainWindow.webContents.send('import-progress', '✅ 所有素材文件处理完成')

    // 通知批量下载管理器所有文件处理完成
    console.log('🎯 [BATCH] 通知批量下载管理器完成')
    batchDownloadManager.completeBatch()

  } catch (error) {
    console.error('处理素材文件失败:', error)
    mainWindow.webContents.send('import-progress', `❌ 素材文件处理失败: ${error.message}`)
    throw error
  }
}

// 处理单个素材文件
async function processSingleMaterial(projectDir, material, type, currentIndex = 1, totalCount = 1) {
  try {
    console.log(`处理${type}素材:`, material.id)

    // 从素材路径中提取文件夹ID和文件名
    const materialPath = material.path
    let materialId, fileName

    // 支持两种路径格式：
    // 1. 传统Windows路径：##_draftpath_placeholder_{UUID}_##\materialId\filename
    // 2. 外部URL：https://example.com/video.mp4
    if (materialPath && materialPath.includes('\\')) {
      // 传统Windows路径格式
      const pathParts = materialPath.split('\\')
      if (pathParts.length < 3) {
        console.log('Windows路径格式不正确，跳过:', materialPath)
        return
      }
      materialId = pathParts[1]  // JSON中的素材ID
      fileName = pathParts[2]    // JSON中的文件名
      console.log('使用Windows路径格式 - 素材ID:', materialId, '文件名:', fileName)

    } else if (materialPath && materialPath.startsWith('http')) {
      // 外部URL格式，但后端现在会生成正确的Windows路径格式
      // 需要重新检查path字段，看是否已经是Windows路径格式
      console.log('检测到外部URL，但需要检查是否有对应的Windows路径')

      // 检查是否有对应的Windows路径格式的path字段
      // 这种情况下应该跳过，让后续逻辑处理
      console.log('外部URL需要后端提供Windows路径格式，跳过处理')
      return

    } else {
      console.log('素材路径格式不支持，跳过:', materialPath)
      return
    }

    console.log(`使用JSON中的素材ID: ${materialId}`)
    console.log(`使用JSON中的文件名: ${fileName}`)

    // 创建素材文件夹（使用改进的方法）
    const materialDir = path.join(projectDir, materialId)
    createDraftFolderWithCorrectProperties(materialDir)
    console.log(`创建素材文件夹: ${materialDir}`)

    // 注意：素材ID命名的JSON文件将在 updateFinalDraftFiles 中生成，这里不重复生成

    // 下载实际的音频文件
    console.log(`检查${type}素材:`, material)
    console.log(`${type}素材download_url:`, material.download_url)

    if (material.download_url) {
      console.log(`开始下载${type}文件:`, material.download_url)

      // 根据类型选择合适的图标
      const typeIcon = type === 'audio' ? '🎵' : type === 'video' ? '📹' : '🖼️'
      mainWindow.webContents.send('import-progress', `${typeIcon} 正在下载${type === 'audio' ? '音频' : type === 'video' ? '视频' : '图片'} ${currentIndex}/${totalCount}: ${fileName}`)

      // 使用从path中提取的文件名，确保路径一致
      const audioFilePath = path.join(materialDir, fileName)

      console.log(`${type}文件保存路径:`, audioFilePath)
      console.log(`使用从path提取的文件名: ${fileName}`)

      try {
        const startTime = Date.now()

        await downloadFile(material.download_url, audioFilePath, fileName, {
          currentFileIndex: currentIndex,
          totalFiles: totalCount
        })
        const downloadTime = Date.now() - startTime
        const stats = await fs.promises.stat(audioFilePath)
        const fileSizeKB = Math.round(stats.size / 1024)
        console.log(`${type}文件下载成功:`, audioFilePath)
        mainWindow.webContents.send('import-progress', `✅ ${type === 'audio' ? '音频' : type === 'video' ? '视频' : '图片'} ${currentIndex}/${totalCount} 下载成功: ${fileName} (${fileSizeKB}KB)`)

        // 通知批量下载管理器文件完成
        batchDownloadManager.completeFile(true)

      } catch (downloadError) {
        console.error(`${type}文件下载失败:`, downloadError)
        mainWindow.webContents.send('import-progress', `❌ ${type === 'audio' ? '音频' : type === 'video' ? '视频' : '图片'} ${currentIndex}/${totalCount} 下载失败: ${fileName} - ${downloadError.message}`)

        // 通知批量下载管理器文件失败
        batchDownloadManager.completeFile(false)

        // 下载失败不影响整体流程，继续执行
      }
    } else {
      console.log(`${type}素材没有下载URL，使用占位素材`)
      const typeIcon = type === 'audio' ? '🎵' : type === 'video' ? '📹' : '🖼️'
      const typeName = type === 'audio' ? '音频' : type === 'video' ? '视频' : '图片'

      // 发送占位素材处理进度
      mainWindow.webContents.send('import-progress', `${typeIcon} ${typeName} ${currentIndex}/${totalCount} 无实际下载链接，已用占位素材占位: ${fileName}`)

      // 模拟下载进度显示
      if (mainWindow && !mainWindow.isDestroyed()) {
        // 发送开始状态
        const progressInfo = {
          fileName: fileName,
          fileType: type,
          progress: 0,
          status: 'starting',
          currentFileIndex: currentIndex,
          totalFiles: totalCount,
          downloadedBytes: 0,
          totalBytes: 0,
          downloadSpeed: 0
        }
        mainWindow.webContents.send('download-file-progress', progressInfo)

        // 短暂延迟后发送完成状态
        setTimeout(() => {
          const completedInfo = {
            ...progressInfo,
            progress: 100,
            status: 'completed'
          }
          mainWindow.webContents.send('download-file-progress', completedInfo)
        }, 100)
      }

      // 通知批量下载管理器跳过的文件也算完成
      batchDownloadManager.completeFile(true)
    }

    console.log(`${type}素材处理完成:`, materialId)

  } catch (error) {
    console.error(`处理${type}素材失败:`, error)
    throw error
  }
}

// 🆕 下载文件（优先CDN，降级TOS SDK）
async function downloadFile(url, filePath, fileName = null, batchInfo = null) {
  try {
    console.log('开始下载文件:', url, '到', filePath)

    // 提取文件名
    const path = require('path')
    const actualFileName = fileName || path.basename(filePath)

    // 🆕 使用统一的URL处理函数
    const urlInfo = normalizeUrlForDownload(url)

    switch (urlInfo.type) {
      case 'cdn_url':
        // CDN URL：直接HTTP下载
        console.log('检测到CDN URL，使用HTTP下载')
        await downloadFileViaHttp(urlInfo.url, filePath, actualFileName, batchInfo)
        break

      case 'system_url':
        // 系统URL：直接使用TOS SDK
        console.log('检测到系统URL，使用TOS SDK下载')
        await downloadFileViaTosSDK(urlInfo.objectKey, filePath, actualFileName, batchInfo)
        break

      case 'static_url':
        // 静态文件URL：直接使用TOS SDK
        console.log('检测到静态文件URL，使用TOS SDK下载')
        await downloadFileViaTosSDK(urlInfo.objectKey, filePath, actualFileName, batchInfo)
        break

      case 'tos_url':
      default:
        // TOS URL或相对路径：直接使用TOS SDK
        console.log('检测到TOS URL或相对路径，使用TOS SDK下载')
        await downloadFileViaTosSDK(urlInfo.objectKey, filePath, actualFileName, batchInfo)
        break
    }

    console.log('文件下载完成:', filePath)

  } catch (error) {
    console.error('文件下载失败:', error)
    // 删除可能存在的不完整文件
    fs.unlink(filePath, () => {})
    throw new Error('下载失败: ' + error.message)
  }
}

// 🆕 TOS SDK下载方法 - 增强版本，支持进度监听
async function downloadFileViaTosSDK(objectKey, filePath, fileName = null, batchInfo = null) {
  try {
    console.log('使用TOS SDK下载文件:', objectKey)

    const path = require('path')

    // 提取文件名
    const actualFileName = fileName || path.basename(filePath)

    // 生成下载ID
    const downloadId = `tos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    const cleanKey = objectKey.startsWith('/') ? objectKey.substring(1) : objectKey

    // 先尝试获取文件大小
    let totalBytes = 0
    try {
      const headResult = await tosClient.headObjectV2({
        bucket: tosConfig.bucket,
        key: cleanKey
      })
      totalBytes = headResult.data.contentLength || 0
      console.log('TOS HEAD请求获取文件大小:', downloadProgressCalculator.formatFileSize(totalBytes))
    } catch (headError) {
      console.log('TOS HEAD请求失败，将在下载时尝试获取大小:', headError.message)
    }

    const result = await tosClient.getObjectV2({
      bucket: tosConfig.bucket,
      key: cleanKey
    })

    if (!result.data || !result.data.content) {
      throw new Error('TOS返回数据格式不正确')
    }

    // 如果HEAD请求没有获取到大小，尝试从下载响应中获取
    if (totalBytes === 0) {
      totalBytes = result.data.contentLength ||
                  result.data['content-length'] ||
                  (result.data.headers && result.data.headers['content-length']) ||
                  0
    }

    console.log(`TOS最终文件大小: ${downloadProgressCalculator.formatFileSize(totalBytes)}`)
    console.log('TOS响应数据:', {
      contentLength: result.data.contentLength,
      headers: result.data.headers,
      keys: Object.keys(result.data)
    })

    // 开始进度跟踪
    const initialProgress = downloadProgressCalculator.startDownload(downloadId, actualFileName, totalBytes, 'tos')

    // 立即发送初始进度
    if (initialProgress) {
      console.log('📊 TOS下载开始:', initialProgress)
      sendDownloadProgress(initialProgress, batchInfo)
    }

    const content = result.data.content
    let allContent = Buffer.from([])
    let downloadedBytes = 0
    let lastProgressUpdate = Date.now()

    for await (const chunk of content) {
      allContent = Buffer.concat([allContent, chunk])
      downloadedBytes += chunk.length

      // 每200ms更新一次进度（提高频率以便小文件也能看到进度）
      const now = Date.now()
      if (now - lastProgressUpdate >= 200) {
        const progressInfo = downloadProgressCalculator.updateProgress(downloadId, downloadedBytes)
        if (progressInfo) {
          console.log('📊 TOS下载进度:', progressInfo)
          sendDownloadProgress(progressInfo, batchInfo)
        }
        lastProgressUpdate = now
      }
    }

    await fs.promises.writeFile(filePath, allContent)
    console.log('TOS SDK下载完成:', filePath)

    // 发送最终进度
    const finalProgress = downloadProgressCalculator.completeDownload(downloadId)
    if (finalProgress) {
      sendDownloadProgress(finalProgress, batchInfo)
    }

  } catch (error) {
    console.error('TOS SDK下载失败:', error)
    throw error
  }
}

// 删除了所有混合策略函数 - Electron客户端不应该修改JSON！

// 🆕 HTTP下载方法 - 增强版本，支持进度监听
async function downloadFileViaHttp(url, savePath, fileName = null, batchInfo = null) {
  try {
    console.log('开始HTTP下载:', url);

    const https = require('https');
    const http = require('http');
    const path = require('path');

    return new Promise((resolve, reject) => {
      // 提取文件名
      const actualFileName = fileName || path.basename(savePath)

      // 生成下载ID
      const downloadId = `http_v2_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      function makeRequest(currentUrl, redirectCount = 0) {
        if (redirectCount > 5) {
          downloadProgressCalculator.completeDownload(downloadId)
          reject(new Error('重定向次数过多'));
          return;
        }

        const client = currentUrl.startsWith('https:') ? https : http;

        const request = client.get(currentUrl, (response) => {
          // 处理重定向
          if (response.statusCode >= 300 && response.statusCode < 400 && response.headers.location) {
            console.log(`HTTP ${response.statusCode} 重定向到:`, response.headers.location);

            // 处理相对URL
            let redirectUrl = response.headers.location;
            if (!redirectUrl.startsWith('http')) {
              const urlObj = new URL(currentUrl);
              if (redirectUrl.startsWith('/')) {
                redirectUrl = `${urlObj.protocol}//${urlObj.host}${redirectUrl}`;
              } else {
                redirectUrl = `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}/${redirectUrl}`;
              }
            }

            makeRequest(redirectUrl, redirectCount + 1);
            return;
          }

          if (response.statusCode !== 200) {
            downloadProgressCalculator.completeDownload(downloadId)
            reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
            return;
          }

          // 获取文件总大小
          const totalBytes = parseInt(response.headers['content-length'] || '0')
          console.log(`文件总大小: ${downloadProgressCalculator.formatFileSize(totalBytes)}`)

          // 开始进度跟踪
          const initialProgress = downloadProgressCalculator.startDownload(downloadId, actualFileName, totalBytes, 'http')

          // 立即发送初始进度
          if (initialProgress) {
            console.log('📊 HTTP下载开始:', initialProgress)
            sendDownloadProgress(initialProgress, batchInfo)
          }

          let downloadedBytes = 0
          let lastProgressUpdate = Date.now()

          // 监听数据流，计算进度
          response.on('data', (chunk) => {
            downloadedBytes += chunk.length

            // 每500ms更新一次进度，避免过于频繁
            const now = Date.now()
            if (now - lastProgressUpdate >= 500) {
              const progressInfo = downloadProgressCalculator.updateProgress(downloadId, downloadedBytes)
              if (progressInfo) {
                sendDownloadProgress(progressInfo, batchInfo)
              }
              lastProgressUpdate = now
            }
          })

          const file = fs.createWriteStream(savePath);
          response.pipe(file);

          file.on('finish', () => {
            file.close();
            console.log('HTTP下载完成:', savePath);

            // 发送最终进度
            const finalProgress = downloadProgressCalculator.completeDownload(downloadId)
            if (finalProgress) {
              sendDownloadProgress(finalProgress, batchInfo)
            }

            resolve();
          });

          file.on('error', (err) => {
            downloadProgressCalculator.completeDownload(downloadId)
            fs.unlink(savePath, () => {}); // 删除不完整的文件
            reject(err);
          });

          request.on('error', (err) => {
            downloadProgressCalculator.completeDownload(downloadId)
            reject(err);
          });

          request.setTimeout(120000, () => {
            downloadProgressCalculator.completeDownload(downloadId)
            request.destroy();
            reject(new Error('下载超时'));
          });
        });
      }

      makeRequest(url);
    });

  } catch (error) {
    console.error('HTTP下载失败:', error.message);
    throw error;
  }
}

// CDN下载方法已移除 - 剪映小助手直接使用TOS SDK
