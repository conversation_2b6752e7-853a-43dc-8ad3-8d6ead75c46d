-- 添加密码修改状态字段
-- 执行时间：2025-06-20
-- 描述：为用户扩展信息表添加密码修改状态标识字段

-- 检查字段是否已存在，如果不存在则添加
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'aigc_user_profile' 
         AND COLUMN_NAME = 'password_changed') = 0,
        'ALTER TABLE aigc_user_profile ADD COLUMN password_changed TINYINT(1) DEFAULT 0 COMMENT ''是否修改过密码：0-未修改，1-已修改'' AFTER api_key;',
        'SELECT ''字段 password_changed 已存在，跳过添加'';'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有用户设置默认值（已注册的用户默认认为未修改过密码）
UPDATE aigc_user_profile 
SET password_changed = 0 
WHERE password_changed IS NULL;

-- 添加注释说明
ALTER TABLE aigc_user_profile COMMENT = '用户扩展信息表（包含密码修改状态）';
