const fs = require('fs-extra');
const path = require('path');

/**
 * 草稿Duration计算器
 * 统一处理所有草稿的总时长计算，确保以最长轨道为准 + 2秒缓冲
 */
class DraftDurationCalculator {
    /**
     * 计算草稿的总时长（基于所有轨道的最长结束时间 + 2秒缓冲）
     * @param {Object} draftContent - 草稿内容JSON
     * @returns {number} 总时长（微秒）
     */
    static calculateDuration(draftContent) {
        let maxEndTime = 0;
        const BUFFER_TIME = 2000000; // 2秒缓冲时间（微秒）
        
        if (!draftContent.tracks || !Array.isArray(draftContent.tracks)) {
            console.warn('[DurationCalculator] Draft has no valid tracks, using buffer time only');
            return BUFFER_TIME;
        }
        
        console.log(`[DurationCalculator] Processing ${draftContent.tracks.length} tracks`);
        
        // 遍历所有轨道（video、audio、text、sticker等）
        for (let i = 0; i < draftContent.tracks.length; i++) {
            const track = draftContent.tracks[i];
            const trackType = track.type || 'unknown';
            
            if (!track.segments || !Array.isArray(track.segments)) {
                console.log(`[DurationCalculator] Track ${i} (${trackType}) has no segments, skipping`);
                continue;
            }
            
            let trackMaxEndTime = 0;
            
            // 遍历轨道中的所有片段
            for (let j = 0; j < track.segments.length; j++) {
                const segment = track.segments[j];
                
                if (!segment.target_timerange) {
                    console.warn(`[DurationCalculator] Track ${i} segment ${j} missing target_timerange, skipping`);
                    continue;
                }
                
                const start = segment.target_timerange.start || 0;
                const duration = segment.target_timerange.duration || 0;
                const endTime = start + duration;
                
                trackMaxEndTime = Math.max(trackMaxEndTime, endTime);
                
                console.log(`[DurationCalculator] Track ${i} (${trackType}) segment ${j}: start=${start}, duration=${duration}, end=${endTime}`);
            }
            
            maxEndTime = Math.max(maxEndTime, trackMaxEndTime);
            console.log(`[DurationCalculator] Track ${i} (${trackType}) max end time: ${trackMaxEndTime}`);
        }
        
        // 在计算结果基础上加2秒缓冲
        const finalDuration = maxEndTime + BUFFER_TIME;
        
        console.log(`[DurationCalculator] Calculated duration: ${maxEndTime} + buffer: ${BUFFER_TIME} = final: ${finalDuration} microseconds (${finalDuration / 1000000} seconds)`);
        return finalDuration;
    }
    
    /**
     * 修正草稿的duration字段
     * @param {Object} draftContent - 草稿内容JSON
     * @returns {Object} 修正后的草稿内容
     */
    static fixDraftDuration(draftContent) {
        const calculatedDuration = this.calculateDuration(draftContent);
        const originalDuration = draftContent.duration || 0;
        
        if (calculatedDuration !== originalDuration) {
            console.log(`[DurationCalculator] Duration correction: ${originalDuration} -> ${calculatedDuration} (difference: ${calculatedDuration - originalDuration} microseconds)`);
            
            // 创建修正后的草稿内容
            const fixedContent = { ...draftContent, duration: calculatedDuration };
            return fixedContent;
        } else {
            console.log(`[DurationCalculator] Duration already correct: ${originalDuration}`);
            return draftContent;
        }
    }
    
    /**
     * 验证并修正草稿文件
     * @param {string} draftFilePath - 草稿文件路径
     * @returns {Promise<boolean>} 是否进行了修正
     */
    static async fixDraftFile(draftFilePath) {
        try {
            if (!await fs.pathExists(draftFilePath)) {
                console.error(`[DurationCalculator] Draft file not found: ${draftFilePath}`);
                return false;
            }
            
            console.log(`[DurationCalculator] Processing draft file: ${draftFilePath}`);
            
            // 读取草稿文件
            const draftContent = await fs.readJson(draftFilePath);
            const originalDuration = draftContent.duration || 0;
            
            // 修正duration
            const fixedContent = this.fixDraftDuration(draftContent);
            
            // 如果有修改，写回文件
            if (fixedContent.duration !== originalDuration) {
                await fs.writeJson(draftFilePath, fixedContent, { spaces: 2 });
                console.log(`[DurationCalculator] Draft file updated: ${draftFilePath} (${originalDuration} -> ${fixedContent.duration})`);
                return true;
            }
            
            console.log(`[DurationCalculator] Draft file already correct: ${draftFilePath}`);
            return false;
        } catch (error) {
            console.error(`[DurationCalculator] Error processing draft file ${draftFilePath}:`, error);
            return false;
        }
    }
    
    /**
     * 批量修正指定目录下的所有草稿
     * @param {string} draftsDirectory - 草稿目录路径
     * @returns {Promise<number>} 修正的文件数量
     */
    static async fixAllDraftsInDirectory(draftsDirectory) {
        try {
            if (!await fs.pathExists(draftsDirectory)) {
                console.error(`[DurationCalculator] Drafts directory not found: ${draftsDirectory}`);
                return 0;
            }
            
            console.log(`[DurationCalculator] Scanning drafts directory: ${draftsDirectory}`);
            const entries = await fs.readdir(draftsDirectory, { withFileTypes: true });
            let fixedCount = 0;
            let totalCount = 0;
            
            for (const entry of entries) {
                if (entry.isDirectory()) {
                    const draftPath = path.join(draftsDirectory, entry.name, 'draft_content.json');
                    totalCount++;
                    
                    const wasFixed = await this.fixDraftFile(draftPath);
                    if (wasFixed) {
                        fixedCount++;
                    }
                }
            }
            
            console.log(`[DurationCalculator] Batch processing complete: ${fixedCount}/${totalCount} drafts fixed in ${draftsDirectory}`);
            return fixedCount;
        } catch (error) {
            console.error(`[DurationCalculator] Error processing drafts directory ${draftsDirectory}:`, error);
            return 0;
        }
    }
    
    /**
     * 获取草稿的详细时长信息（用于调试）
     * @param {Object} draftContent - 草稿内容JSON
     * @returns {Object} 详细的时长信息
     */
    static getDurationDetails(draftContent) {
        const details = {
            tracks: [],
            maxEndTime: 0,
            bufferTime: 2000000,
            finalDuration: 0,
            originalDuration: draftContent.duration || 0
        };
        
        if (!draftContent.tracks || !Array.isArray(draftContent.tracks)) {
            details.finalDuration = details.bufferTime;
            return details;
        }
        
        // 分析每个轨道
        for (let i = 0; i < draftContent.tracks.length; i++) {
            const track = draftContent.tracks[i];
            const trackInfo = {
                index: i,
                type: track.type || 'unknown',
                segmentCount: 0,
                maxEndTime: 0,
                segments: []
            };
            
            if (track.segments && Array.isArray(track.segments)) {
                trackInfo.segmentCount = track.segments.length;
                
                for (let j = 0; j < track.segments.length; j++) {
                    const segment = track.segments[j];
                    
                    if (segment.target_timerange) {
                        const start = segment.target_timerange.start || 0;
                        const duration = segment.target_timerange.duration || 0;
                        const endTime = start + duration;
                        
                        trackInfo.segments.push({
                            index: j,
                            start,
                            duration,
                            endTime
                        });
                        
                        trackInfo.maxEndTime = Math.max(trackInfo.maxEndTime, endTime);
                    }
                }
            }
            
            details.tracks.push(trackInfo);
            details.maxEndTime = Math.max(details.maxEndTime, trackInfo.maxEndTime);
        }
        
        details.finalDuration = details.maxEndTime + details.bufferTime;
        return details;
    }
}

module.exports = DraftDurationCalculator;
