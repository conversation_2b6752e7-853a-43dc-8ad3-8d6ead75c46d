# CMake.js

**CMake.js** is a build tool that allow native addon developer to compile their
C++ code into executable form. It works like **[node-gyp](node-gyp.md)** but
instead of Google's **gyp** format it is base on **CMake** build system.

## **CMake** reference

  - [Installation](https://www.npmjs.com/package/cmake-js#installation)
  - [How to use](https://www.npmjs.com/package/cmake-js#usage)
  - [Using N-API and node-addon-api](https://github.com/cmake-js/cmake-js#n-api-and-node-addon-api)
  - [Tutorials](https://www.npmjs.com/package/cmake-js#tutorials)
  - [Use case in the works - ArrayFire.js](https://www.npmjs.com/package/cmake-js#use-case-in-the-works---arrayfirejs)

Sometimes finding the right settings is not easy so to accomplish at most
complicated task please refer to:

- [CMake documentation](https://cmake.org/)
- [CMake.js wiki](https://github.com/cmake-js/cmake-js/wiki)