import { Options } from ".";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "./util/errors";
export default class Context {
    options: Options;
    static from(contextOrOptions: Context | Options): Context;
    constructor(options: Options);
    resolvePath(path: string): string;
    nonFatalError(error: Error, errorBuffer?: ErrorBuffer): void;
    warning(error: Error, errorBuffer?: E<PERSON>r<PERSON>uffer): void;
    readonly canWarn: boolean;
}
