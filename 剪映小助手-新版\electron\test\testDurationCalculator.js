const path = require('path');
const DraftDurationCalculator = require('../utils/draftDurationCalculator');
const DraftManager = require('../main/draftManager');

/**
 * Duration计算器测试脚本
 * 用于验证Duration计算功能的正确性
 */
class DurationCalculatorTest {
    /**
     * 运行所有测试
     */
    static async runAllTests() {
        console.log('='.repeat(60));
        console.log('开始Duration计算器测试');
        console.log('='.repeat(60));
        
        try {
            await this.testBasicCalculation();
            await this.testEmptyDraft();
            await this.testMixedTracks();
            await this.testRealDraftFile();
            await this.testBatchProcessing();
            
            console.log('\n' + '='.repeat(60));
            console.log('所有测试完成！');
            console.log('='.repeat(60));
        } catch (error) {
            console.error('测试失败:', error);
        }
    }
    
    /**
     * 测试基本计算功能
     */
    static async testBasicCalculation() {
        console.log('\n[测试1] 基本计算功能');
        console.log('-'.repeat(40));
        
        const testDraft = {
            tracks: [
                {
                    type: 'video',
                    segments: [
                        {
                            target_timerange: { start: 0, duration: 5000000 }
                        },
                        {
                            target_timerange: { start: 5000000, duration: 3000000 }
                        }
                    ]
                }
            ]
        };
        
        const calculatedDuration = DraftDurationCalculator.calculateDuration(testDraft);
        const expectedDuration = 8000000 + 2000000; // 最长轨道 + 2秒缓冲
        
        console.log(`计算结果: ${calculatedDuration} 微秒`);
        console.log(`预期结果: ${expectedDuration} 微秒`);
        console.log(`测试${calculatedDuration === expectedDuration ? '通过' : '失败'}`);
        
        if (calculatedDuration !== expectedDuration) {
            throw new Error(`基本计算测试失败: 期望 ${expectedDuration}, 实际 ${calculatedDuration}`);
        }
    }
    
    /**
     * 测试空草稿
     */
    static async testEmptyDraft() {
        console.log('\n[测试2] 空草稿处理');
        console.log('-'.repeat(40));
        
        const emptyDraft = { tracks: [] };
        const calculatedDuration = DraftDurationCalculator.calculateDuration(emptyDraft);
        const expectedDuration = 2000000; // 只有缓冲时间
        
        console.log(`计算结果: ${calculatedDuration} 微秒`);
        console.log(`预期结果: ${expectedDuration} 微秒`);
        console.log(`测试${calculatedDuration === expectedDuration ? '通过' : '失败'}`);
        
        if (calculatedDuration !== expectedDuration) {
            throw new Error(`空草稿测试失败: 期望 ${expectedDuration}, 实际 ${calculatedDuration}`);
        }
    }
    
    /**
     * 测试混合轨道
     */
    static async testMixedTracks() {
        console.log('\n[测试3] 混合轨道处理');
        console.log('-'.repeat(40));
        
        const mixedDraft = {
            tracks: [
                {
                    type: 'video',
                    segments: [
                        { target_timerange: { start: 0, duration: 10000000 } }
                    ]
                },
                {
                    type: 'audio',
                    segments: [
                        { target_timerange: { start: 0, duration: 8000000 } },
                        { target_timerange: { start: 8000000, duration: 5000000 } }
                    ]
                },
                {
                    type: 'text',
                    segments: [
                        { target_timerange: { start: 2000000, duration: 6000000 } }
                    ]
                }
            ]
        };
        
        const calculatedDuration = DraftDurationCalculator.calculateDuration(mixedDraft);
        const expectedDuration = 13000000 + 2000000; // audio轨道最长(13秒) + 2秒缓冲
        
        console.log(`计算结果: ${calculatedDuration} 微秒`);
        console.log(`预期结果: ${expectedDuration} 微秒`);
        console.log(`测试${calculatedDuration === expectedDuration ? '通过' : '失败'}`);
        
        if (calculatedDuration !== expectedDuration) {
            throw new Error(`混合轨道测试失败: 期望 ${expectedDuration}, 实际 ${calculatedDuration}`);
        }
    }
    
    /**
     * 测试真实草稿文件
     */
    static async testRealDraftFile() {
        console.log('\n[测试4] 真实草稿文件处理');
        console.log('-'.repeat(40));
        
        try {
            // 测试有问题的草稿文件
            const problemDraftPath = path.join(__dirname, '../../coze-plugins/草稿/有问题的draft_content.json');
            
            console.log(`测试文件: ${problemDraftPath}`);
            
            // 检查文件是否存在
            const fs = require('fs-extra');
            if (!await fs.pathExists(problemDraftPath)) {
                console.log('测试文件不存在，跳过真实文件测试');
                return;
            }
            
            // 读取并计算
            const draftContent = await fs.readJson(problemDraftPath);
            const originalDuration = draftContent.duration || 0;
            const calculatedDuration = DraftDurationCalculator.calculateDuration(draftContent);
            
            console.log(`原始duration: ${originalDuration} 微秒 (${originalDuration / 1000000} 秒)`);
            console.log(`计算duration: ${calculatedDuration} 微秒 (${calculatedDuration / 1000000} 秒)`);
            console.log(`需要修正: ${originalDuration !== calculatedDuration ? '是' : '否'}`);
            
            // 获取详细信息
            const details = DraftDurationCalculator.getDurationDetails(draftContent);
            console.log(`轨道数量: ${details.tracks.length}`);
            console.log(`最长轨道结束时间: ${details.maxEndTime} 微秒`);
            console.log(`缓冲时间: ${details.bufferTime} 微秒`);
            
            console.log('真实文件测试完成');
        } catch (error) {
            console.error('真实文件测试失败:', error.message);
        }
    }
    
    /**
     * 测试批量处理
     */
    static async testBatchProcessing() {
        console.log('\n[测试5] 批量处理功能');
        console.log('-'.repeat(40));
        
        try {
            const draftsDirectory = path.join(__dirname, '../../coze-plugins/草稿');
            
            console.log(`测试目录: ${draftsDirectory}`);
            
            // 检查目录是否存在
            const fs = require('fs-extra');
            if (!await fs.pathExists(draftsDirectory)) {
                console.log('草稿目录不存在，跳过批量处理测试');
                return;
            }
            
            // 列出所有草稿
            const drafts = await DraftManager.listDrafts(draftsDirectory);
            console.log(`发现草稿数量: ${drafts.length}`);
            
            if (drafts.length > 0) {
                // 检查前3个草稿的状态
                const checkCount = Math.min(3, drafts.length);
                console.log(`检查前 ${checkCount} 个草稿的duration状态:`);
                
                for (let i = 0; i < checkCount; i++) {
                    const draft = drafts[i];
                    try {
                        const draftContent = await fs.readJson(draft.path);
                        const calculatedDuration = DraftDurationCalculator.calculateDuration(draftContent);
                        const needsFix = calculatedDuration !== (draftContent.duration || 0);
                        
                        console.log(`  ${draft.name}: ${needsFix ? '需要修正' : '正确'} (${draftContent.duration || 0} -> ${calculatedDuration})`);
                    } catch (error) {
                        console.log(`  ${draft.name}: 读取失败 (${error.message})`);
                    }
                }
            }
            
            console.log('批量处理测试完成');
        } catch (error) {
            console.error('批量处理测试失败:', error.message);
        }
    }
    
    /**
     * 创建测试草稿文件
     */
    static createTestDraft(name, tracks) {
        return {
            id: `test-${name}`,
            duration: 10000000, // 故意设置错误的值
            canvas_config: { width: 1080, height: 1920 },
            tracks: tracks,
            materials: {
                videos: [],
                audios: [],
                images: [],
                transitions: []
            }
        };
    }
}

// 如果直接运行此脚本，执行所有测试
if (require.main === module) {
    DurationCalculatorTest.runAllTests().catch(console.error);
}

module.exports = DurationCalculatorTest;
