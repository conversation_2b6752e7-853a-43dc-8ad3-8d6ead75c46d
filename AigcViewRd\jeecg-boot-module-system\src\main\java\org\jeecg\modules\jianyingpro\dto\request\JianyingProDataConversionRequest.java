package org.jeecg.modules.jianyingpro.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 智能数据转换请求
 * 支持同时输入多种数据类型，系统自动进行所有可能的转换
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JianyingProDataConversionRequest extends BaseJianyingProRequest {

    @ApiModelProperty(value = "字符串数据（可选）",
                     notes = "输入字符串，系统会自动转换为列表格式。例如：\"苹果,香蕉,橙子\"",
                     example = "苹果,香蕉,橙子")
    @JsonProperty("string_to_list")
    private String inputString;

    @ApiModelProperty(value = "字符串列表数据（可选）",
                     notes = "输入字符串数组，系统会自动转换为对象格式。例如：[\"苹果\", \"香蕉\", \"橙子\"]",
                     example = "[\"苹果\", \"香蕉\", \"橙子\"]")
    @JsonProperty("string_list_to_objects")
    private java.util.List<String> inputStringList;

    @ApiModelProperty(value = "对象列表数据（可选）",
                     notes = "输入对象数组，系统会自动提取指定字段转换为字符串列表。例如：[{\"name\": \"苹果\"}, {\"name\": \"香蕉\"}]",
                     example = "[{\"name\": \"苹果\"}, {\"name\": \"香蕉\"}]")
    @JsonProperty("objects_to_string_list")
    private java.util.List<Object> inputObjectList;


    
    @Override
    public String getSummary() {
        int inputCount = 0;
        if (inputString != null && !inputString.trim().isEmpty()) inputCount++;
        if (inputStringList != null && !inputStringList.isEmpty()) inputCount++;
        if (inputObjectList != null && !inputObjectList.isEmpty()) inputCount++;

        return "JianyingProDataConversionRequest{" +
               "inputString=" + (inputString != null ? "\"" + inputString.substring(0, Math.min(20, inputString.length())) + "...\"" : "null") +
               ", inputStringListSize=" + (inputStringList != null ? inputStringList.size() : 0) +
               ", inputObjectListSize=" + (inputObjectList != null ? inputObjectList.size() : 0) +
               ", totalInputs=" + inputCount +
               "}";
    }

    @Override
    public void validate() {
        super.validate();

        // 验证至少提供一种输入数据
        boolean hasInput = false;
        if (inputString != null && !inputString.trim().isEmpty()) {
            hasInput = true;
        }
        if (inputStringList != null && !inputStringList.isEmpty()) {
            hasInput = true;
        }
        if (inputObjectList != null && !inputObjectList.isEmpty()) {
            hasInput = true;
        }

        if (!hasInput) {
            throw new IllegalArgumentException(
                "至少需要提供一种输入数据：string_to_list、string_list_to_objects 或 objects_to_string_list"
            );
        }

        // 不再需要设置默认值（已移除delimiter和extractField参数）
    }
}
