package ${bussiPackage}.${entityPackage}.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: ${tableVo.ftlDescription}
 * @Author: jeecg-boot
 * @Date:   ${.now?string["yyyy-MM-dd"]}
 * @Version: V1.0
 */
@Data
@TableName("${tableName}")
@ApiModel(value="${tableName}对象", description="${tableVo.ftlDescription}")
public class ${entityName} implements Serializable {
    private static final long serialVersionUID = 1L;
    
    <#list originalColumns as po>
	/**${po.filedComment}*/
	<#if po.fieldName == primaryKeyField>
	@TableId(type = IdType.ASSIGN_ID)
	</#if>
  <#if po.fieldType =='java.util.Date'>
	<#if po.fieldDbType =='date'>
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
	<#elseif po.fieldDbType =='datetime'>
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	</#if>
  </#if>
	@ApiModelProperty(value = "${po.filedComment}")
	private <#if po.fieldType=='java.sql.Blob'>byte[]<#else>${po.fieldType}</#if> ${po.fieldName};
	</#list>
}
