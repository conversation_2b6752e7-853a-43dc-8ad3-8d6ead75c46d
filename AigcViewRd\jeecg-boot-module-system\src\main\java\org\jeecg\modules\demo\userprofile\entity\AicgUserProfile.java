package org.jeecg.modules.demo.userprofile.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 用户扩展信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Data
@TableName("aicg_user_profile")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aicg_user_profile对象", description="用户扩展信息表")
public class AicgUserProfile implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;
    
	/**用户名*/
	@Excel(name = "用户名", width = 15)
    @ApiModelProperty(value = "用户名")
    private String username;

	/**昵称*/
	@Excel(name = "昵称", width = 15)
    @ApiModelProperty(value = "昵称")
    private String nickname;

	/**手机号*/
	@Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String phone;

	/**邮箱*/
	@Excel(name = "邮箱", width = 20)
    @ApiModelProperty(value = "邮箱")
    private String email;

	/**头像URL*/
	@Excel(name = "头像URL", width = 30)
    @ApiModelProperty(value = "头像URL")
    private String avatar;

	/**账户余额*/
	@Excel(name = "账户余额", width = 15)
    @ApiModelProperty(value = "账户余额")
    private BigDecimal accountBalance;

	/**冻结余额*/
	@Excel(name = "冻结余额", width = 15)
    @ApiModelProperty(value = "冻结余额（预扣费时临时冻结的金额）")
    private BigDecimal frozenBalance;
    
	/**API密钥*/
	@Excel(name = "API密钥", width = 30)
    @ApiModelProperty(value = "API密钥")
    private String apiKey;

	/**是否修改过密码*/
	@Excel(name = "是否修改过密码", width = 15)
    @ApiModelProperty(value = "是否修改过密码：0-未修改，1-已修改")
    private Integer passwordChanged;

	/**累计消费*/
	@Excel(name = "累计消费", width = 15)
    @ApiModelProperty(value = "累计消费")
    private BigDecimal totalConsumption;
    
	/**累计充值*/
	@Excel(name = "累计充值", width = 15)
    @ApiModelProperty(value = "累计充值")
    private BigDecimal totalRecharge;
    
	/**会员到期时间*/
	@Excel(name = "会员到期时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "会员到期时间")
    private Date memberExpireTime;

	/**我的邀请码（用于邀请别人）*/
	@Excel(name = "我的邀请码", width = 15)
    @ApiModelProperty(value = "我的邀请码（用于邀请别人）")
    private String myInviteCode;

	/**我使用的邀请码（注册时填写的）*/
	@Excel(name = "使用的邀请码", width = 15)
    @ApiModelProperty(value = "我使用的邀请码（注册时填写的）")
    private String usedInviteCode;

	/**邀请我的人的用户ID*/
	@Excel(name = "邀请人ID", width = 15)
    @ApiModelProperty(value = "邀请我的人的用户ID")
    private String inviterUserId;

	/**我邀请的人数统计*/
	@Excel(name = "邀请人数", width = 10)
    @ApiModelProperty(value = "我邀请的人数统计")
    private Integer inviteCount;

	/**佣金等级*/
	@Excel(name = "佣金等级", width = 10)
    @ApiModelProperty(value = "佣金等级：1-基础，2-高阶，3-顶级")
    private Integer commissionLevel;

	/**有效邀请人数*/
	@Excel(name = "有效邀请人数", width = 10)
    @ApiModelProperty(value = "有效邀请人数（注册并订阅会员的人数）")
    private Integer validInviteCount;

	/**累计佣金*/
	@Excel(name = "累计佣金", width = 15)
    @ApiModelProperty(value = "累计佣金")
    private BigDecimal totalCommission;

	/**可提现佣金*/
	@Excel(name = "可提现佣金", width = 15)
    @ApiModelProperty(value = "可提现佣金")
    private BigDecimal availableCommission;

	/**注册来源：phone-手机号,email-邮箱,wechat-微信*/
	@Excel(name = "注册来源", width = 15)
    @ApiModelProperty(value = "注册来源：phone-手机号,email-邮箱,wechat-微信")
    private String registerSource;
    
	/**状态：1-正常，2-冻结*/
    @ApiModelProperty(value = "状态：1-正常，2-冻结")
    private Integer status;
    
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
}
