const { ipc<PERSON><PERSON><PERSON> } = require('electron');

/**
 * 草稿Duration调试器（渲染进程）
 * 提供前端调用的Duration计算和修正功能
 */
class DraftDebugger {
    /**
     * 修正单个草稿的duration
     * @param {string} draftPath - 草稿文件路径
     * @returns {Promise<boolean>} 是否进行了修正
     */
    static async fixSingleDraft(draftPath) {
        try {
            console.log(`[DraftDebugger] Fixing single draft: ${draftPath}`);
            
            const result = await ipcRenderer.invoke('fix-draft-duration', draftPath);
            
            if (result.success) {
                console.log(`[DraftDebugger] ${result.message}: ${draftPath}`);
                return result.wasFixed;
            } else {
                console.error(`[DraftDebugger] ${result.message}: ${result.error}`);
                return false;
            }
        } catch (error) {
            console.error('[DraftDebugger] Fix single draft error:', error);
            return false;
        }
    }
    
    /**
     * 批量修正所有草稿
     * @param {string} draftsDirectory - 草稿目录路径
     * @returns {Promise<number>} 修正的文件数量
     */
    static async fixAllDrafts(draftsDirectory) {
        try {
            console.log(`[DraftDebugger] Fixing all drafts in: ${draftsDirectory}`);
            
            const result = await ipcRenderer.invoke('fix-all-drafts', draftsDirectory);
            
            if (result.success) {
                console.log(`[DraftDebugger] ${result.message}`);
                return result.fixedCount;
            } else {
                console.error(`[DraftDebugger] ${result.message}: ${result.error}`);
                return 0;
            }
        } catch (error) {
            console.error('[DraftDebugger] Fix all drafts error:', error);
            return 0;
        }
    }
    
    /**
     * 计算草稿的duration（不保存）
     * @param {string} draftPath - 草稿文件路径
     * @returns {Promise<Object>} 计算结果
     */
    static async calculateDraftDuration(draftPath) {
        try {
            console.log(`[DraftDebugger] Calculating duration for: ${draftPath}`);
            
            const result = await ipcRenderer.invoke('calculate-draft-duration', draftPath);
            
            if (result.success) {
                console.log(`[DraftDebugger] Duration calculation complete:`, result);
                return result;
            } else {
                console.error(`[DraftDebugger] ${result.message}: ${result.error}`);
                return null;
            }
        } catch (error) {
            console.error('[DraftDebugger] Calculate duration error:', error);
            return null;
        }
    }
    
    /**
     * 验证草稿文件
     * @param {string} draftPath - 草稿文件路径
     * @returns {Promise<Object>} 验证结果
     */
    static async validateDraft(draftPath) {
        try {
            console.log(`[DraftDebugger] Validating draft: ${draftPath}`);
            
            const result = await ipcRenderer.invoke('validate-draft', draftPath);
            
            if (result.success) {
                console.log(`[DraftDebugger] Draft validation complete:`, result.validation);
                return result.validation;
            } else {
                console.error(`[DraftDebugger] ${result.message}: ${result.error}`);
                return null;
            }
        } catch (error) {
            console.error('[DraftDebugger] Validate draft error:', error);
            return null;
        }
    }
    
    /**
     * 获取草稿列表
     * @param {string} draftsDirectory - 草稿目录路径
     * @returns {Promise<Array>} 草稿列表
     */
    static async listDrafts(draftsDirectory) {
        try {
            console.log(`[DraftDebugger] Listing drafts in: ${draftsDirectory}`);
            
            const result = await ipcRenderer.invoke('list-drafts', draftsDirectory);
            
            if (result.success) {
                console.log(`[DraftDebugger] Found ${result.count} drafts`);
                return result.drafts;
            } else {
                console.error(`[DraftDebugger] ${result.message}: ${result.error}`);
                return [];
            }
        } catch (error) {
            console.error('[DraftDebugger] List drafts error:', error);
            return [];
        }
    }
    
    /**
     * 保存草稿（自动修正duration）
     * @param {string} draftPath - 草稿文件路径
     * @param {Object} draftContent - 草稿内容
     * @returns {Promise<Object>} 保存结果
     */
    static async saveDraftWithDurationFix(draftPath, draftContent) {
        try {
            console.log(`[DraftDebugger] Saving draft with duration fix: ${draftPath}`);
            
            const result = await ipcRenderer.invoke('save-draft-with-duration-fix', draftPath, draftContent);
            
            if (result.success) {
                console.log(`[DraftDebugger] Draft saved successfully with duration: ${result.fixedDuration}`);
                return result;
            } else {
                console.error(`[DraftDebugger] ${result.message}: ${result.error}`);
                return null;
            }
        } catch (error) {
            console.error('[DraftDebugger] Save draft error:', error);
            return null;
        }
    }
    
    /**
     * 获取默认草稿目录
     * @returns {Promise<string>} 默认草稿目录路径
     */
    static async getDefaultDraftsDirectory() {
        try {
            const result = await ipcRenderer.invoke('get-default-drafts-directory');
            
            if (result.success) {
                console.log(`[DraftDebugger] Default drafts directory: ${result.path}`);
                return result.path;
            } else {
                console.error(`[DraftDebugger] ${result.message}: ${result.error}`);
                return null;
            }
        } catch (error) {
            console.error('[DraftDebugger] Get default directory error:', error);
            return null;
        }
    }
    
    /**
     * 批量检查所有草稿的duration状态
     * @param {string} draftsDirectory - 草稿目录路径
     * @returns {Promise<Object>} 检查结果统计
     */
    static async checkAllDraftsStatus(draftsDirectory) {
        try {
            console.log(`[DraftDebugger] Checking all drafts status in: ${draftsDirectory}`);
            
            const drafts = await this.listDrafts(draftsDirectory);
            const results = {
                total: drafts.length,
                correct: 0,
                needsFix: 0,
                errors: 0,
                details: []
            };
            
            for (const draft of drafts) {
                try {
                    const calculation = await this.calculateDraftDuration(draft.path);
                    
                    if (calculation) {
                        const detail = {
                            name: draft.name,
                            path: draft.path,
                            originalDuration: calculation.originalDuration,
                            calculatedDuration: calculation.calculatedDuration,
                            needsFix: calculation.needsFix,
                            status: calculation.needsFix ? 'needs_fix' : 'correct'
                        };
                        
                        results.details.push(detail);
                        
                        if (calculation.needsFix) {
                            results.needsFix++;
                        } else {
                            results.correct++;
                        }
                    } else {
                        results.errors++;
                        results.details.push({
                            name: draft.name,
                            path: draft.path,
                            status: 'error'
                        });
                    }
                } catch (error) {
                    console.error(`[DraftDebugger] Error checking draft ${draft.name}:`, error);
                    results.errors++;
                    results.details.push({
                        name: draft.name,
                        path: draft.path,
                        status: 'error',
                        error: error.message
                    });
                }
            }
            
            console.log(`[DraftDebugger] Status check complete:`, results);
            return results;
        } catch (error) {
            console.error('[DraftDebugger] Check all drafts status error:', error);
            return null;
        }
    }
    
    /**
     * 创建调试面板HTML
     * @returns {string} HTML内容
     */
    static createDebugPanel() {
        return `
            <div id="draft-debugger-panel" style="
                position: fixed;
                top: 10px;
                right: 10px;
                width: 300px;
                background: white;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 15px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 12px;
            ">
                <h3 style="margin: 0 0 10px 0; color: #333;">Duration调试工具</h3>
                
                <button onclick="DraftDebugger.fixAllDraftsInDefault()" style="
                    width: 100%;
                    padding: 8px;
                    margin: 5px 0;
                    background: #007cba;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                ">修正所有草稿</button>
                
                <button onclick="DraftDebugger.checkAllDraftsInDefault()" style="
                    width: 100%;
                    padding: 8px;
                    margin: 5px 0;
                    background: #28a745;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                ">检查所有草稿状态</button>
                
                <div id="debug-output" style="
                    margin-top: 10px;
                    padding: 8px;
                    background: #f8f9fa;
                    border-radius: 3px;
                    max-height: 200px;
                    overflow-y: auto;
                    font-size: 11px;
                ">
                    点击按钮开始调试...
                </div>
                
                <button onclick="document.getElementById('draft-debugger-panel').style.display='none'" style="
                    position: absolute;
                    top: 5px;
                    right: 5px;
                    background: #dc3545;
                    color: white;
                    border: none;
                    border-radius: 50%;
                    width: 20px;
                    height: 20px;
                    cursor: pointer;
                    font-size: 12px;
                ">×</button>
            </div>
        `;
    }
    
    /**
     * 在默认目录修正所有草稿（便捷方法）
     */
    static async fixAllDraftsInDefault() {
        const directory = await this.getDefaultDraftsDirectory();
        if (directory) {
            const count = await this.fixAllDrafts(directory);
            this.updateDebugOutput(`修正完成！共修正了 ${count} 个草稿文件。`);
        }
    }
    
    /**
     * 在默认目录检查所有草稿状态（便捷方法）
     */
    static async checkAllDraftsInDefault() {
        const directory = await this.getDefaultDraftsDirectory();
        if (directory) {
            const status = await this.checkAllDraftsStatus(directory);
            if (status) {
                this.updateDebugOutput(`
                    检查完成！<br>
                    总计：${status.total} 个草稿<br>
                    正确：${status.correct} 个<br>
                    需修正：${status.needsFix} 个<br>
                    错误：${status.errors} 个
                `);
            }
        }
    }
    
    /**
     * 更新调试输出
     */
    static updateDebugOutput(message) {
        const output = document.getElementById('debug-output');
        if (output) {
            output.innerHTML = message;
        }
    }
}

// 导出到全局，方便调试
if (typeof window !== 'undefined') {
    window.DraftDebugger = DraftDebugger;
    
    // 自动创建调试面板（可选）
    document.addEventListener('DOMContentLoaded', () => {
        if (!document.getElementById('draft-debugger-panel')) {
            document.body.insertAdjacentHTML('beforeend', DraftDebugger.createDebugPanel());
        }
    });
}

module.exports = DraftDebugger;
