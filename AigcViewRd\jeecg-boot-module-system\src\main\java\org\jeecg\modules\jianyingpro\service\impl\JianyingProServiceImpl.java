package org.jeecg.modules.jianyingpro.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.jianying.dto.EffectInfo;
import org.jeecg.modules.jianyingpro.dto.request.*;
import org.jeecg.modules.jianyingpro.enums.JianyingProErrorCode;
import org.jeecg.modules.jianyingpro.service.JianyingProService;
import org.jeecg.modules.jianyingpro.service.internal.JianyingProAssistantService;
import org.jeecg.modules.jianyingpro.service.internal.JianyingProCozeApiService;
import org.jeecg.modules.jianyingpro.service.internal.JianyingProDataboxService;
import org.jeecg.modules.jianyingpro.util.JianyingProResponseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 超级剪映小助手服务实现类
 * 提供8个一体化操作接口的具体实现
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
public class JianyingProServiceImpl implements JianyingProService {
    
    @Autowired
    private JianyingProAssistantService assistantService;

    @Autowired
    private JianyingProDataboxService databoxService;

    @Autowired
    private org.jeecg.modules.jianying.service.JianyingIdResolverService jianyingIdResolverService;

    @Autowired
    private JianyingProCozeApiService cozeApiService;

    @Autowired
    private org.jeecg.modules.jianyingpro.service.internal.JianyingProIdResolverService jianyingProIdResolverService;

    @Autowired
    private org.jeecg.modules.jianying.service.JianyingEffectSearchService effectSearchService;

    @Autowired
    private org.jeecg.modules.jianying.service.JianyingMaskSearchService jianyingMaskSearchService;
    
    @Override
    public JSONObject addAudios(JianyingProAddAudiosRequest request) {
        try {
            log.info("开始Pro版一体化音频添加: {}", request.getSummary());

            // 参数验证
            if (request.getDraftUrl() == null || request.getDraftUrl().trim().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "草稿地址不能为空", "请提供有效的draft_url参数");
            }

            if (request.getMp3Urls() == null || request.getMp3Urls().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "音频列表不能为空", "请提供有效的mp3_urls参数");
            }

            if (request.getTimelines() == null || request.getTimelines().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "时间线列表不能为空", "请提供有效的timelines参数");
            }

            // 第一步：Pro版内部生成音频信息（复制自稳定版audio_infos逻辑）
            String audioInfosJson = generateAudioInfosInternal(request);
            if (audioInfosJson == null) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "音频信息生成失败", "内部处理错误");
            }

            // 第二步：Pro版内部添加音频（复制自稳定版add_audios逻辑）
            JSONObject result = addAudiosInternal(request.getDraftUrl(), audioInfosJson, request);

            // 统一响应格式
            return JianyingProResponseUtil.formatResponse(result, "Pro版一体化音频添加");

        } catch (Exception e) {
            log.error("Pro版一体化音频添加失败", e);
            return JianyingProResponseUtil.systemError("Pro版一体化音频添加", e);
        }
    }
    
    @Override
    public JSONObject addVideos(JianyingProAddVideosRequest request) {
        try {
            log.info("开始Pro版一体化视频添加: {}", request.getSummary());

            // 参数验证
            if (request.getDraftUrl() == null || request.getDraftUrl().trim().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "草稿地址不能为空", "请提供有效的draft_url参数");
            }

            if (request.getVideoUrls() == null || request.getVideoUrls().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "视频URL列表不能为空", "请提供有效的video_urls参数");
            }

            if (request.getTimelines() == null || request.getTimelines().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "时间线列表不能为空", "请提供有效的timelines参数");
            }

            // 第一步：Pro版内部生成视频信息（复制自稳定版video_infos逻辑）
            String videoInfosJson = generateVideoInfosInternal(request);
            if (videoInfosJson == null) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "视频信息生成失败", "内部处理错误");
            }

            // 第二步：Pro版内部添加视频（复制自稳定版add_videos逻辑）
            JSONObject result = addVideosInternal(request.getDraftUrl(), videoInfosJson, request);

            // 统一响应格式
            return JianyingProResponseUtil.formatResponse(result, "Pro版一体化视频添加");

        } catch (Exception e) {
            log.error("Pro版一体化视频添加失败", e);
            return JianyingProResponseUtil.systemError("Pro版一体化视频添加", e);
        }
    }
    
    @Override
    public JSONObject generateTimelines(JianyingProTimelinesRequest request) {
        try {
            log.info("开始智能时间线生成: {}", request.getSummary());

            // 参数互斥验证：不能同时提供音频模式和自定义模式的参数
            boolean hasAudioParams = hasAudioTimelineParams(request);
            boolean hasCustomParams = hasCustomTimelineParams(request);

            if (hasAudioParams && hasCustomParams) {
                // 参数冲突错误直接返回，不经过formatResponse处理
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_CONFLICT_004,
                    "参数冲突", "不能同时提供音频模式参数(audio_urls)和自定义模式参数(duration+num)，请选择其中一种模式");
            }

            // 参数不完整验证
            if (!hasAudioParams && !hasCustomParams) {
                // 参数不完整错误直接返回，不经过formatResponse处理
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "参数不完整", "请提供 audio_urls 或 duration+num 参数");
            }

            // 智能模式识别：根据参数自动选择处理方式
            JSONObject result;
            if (hasAudioParams) {
                // 音频时间线模式
                result = generateAudioTimelines(request);
            } else {
                // 自定义时间线模式
                result = generateCustomTimelines(request);
            }

            return JianyingProResponseUtil.formatResponse(result, "智能时间线生成");

        } catch (Exception e) {
            log.error("智能时间线生成失败", e);
            return JianyingProResponseUtil.systemError("智能时间线生成", e);
        }
    }
    
    @Override
    public JSONObject dataConversion(JianyingProDataConversionRequest request) {
        try {
            log.info("开始智能数据转换: {}", request.getSummary());

            // 智能转换结果容器
            JSONObject allResults = new JSONObject();
            int conversionCount = 0;

            // 1. 字符串转列表转换
            if (request.getInputString() != null && !request.getInputString().trim().isEmpty()) {
                try {
                    JSONObject strToListResult = convertStringToList(request.getInputString(), request.getDelimiter());
                    allResults.put("string_to_list", strToListResult);
                    conversionCount++;
                    log.info("字符串转列表转换完成");
                } catch (Exception e) {
                    log.warn("字符串转列表转换失败: {}", e.getMessage());
                    allResults.put("string_to_list_error", e.getMessage());
                }
            }

            // 2. 字符串列表转对象转换
            if (request.getInputStringList() != null && !request.getInputStringList().isEmpty()) {
                try {
                    JSONObject listToObjsResult = convertStringListToObjects(request.getInputStringList());
                    allResults.put("string_list_to_objects", listToObjsResult);
                    conversionCount++;
                    log.info("字符串列表转对象转换完成，输入: {}, 结果: {}", request.getInputStringList(), listToObjsResult.toJSONString());
                } catch (Exception e) {
                    log.warn("字符串列表转对象转换失败: {}", e.getMessage());
                    allResults.put("string_list_to_objects_error", e.getMessage());
                }
            }

            // 3. 对象列表转字符串列表转换
            if (request.getInputObjectList() != null && !request.getInputObjectList().isEmpty()) {
                try {
                    JSONObject objsToListResult = convertObjectsToStringList(request.getInputObjectList(), request.getExtractField());
                    allResults.put("objects_to_string_list", objsToListResult);
                    conversionCount++;
                    log.info("对象列表转字符串列表转换完成");
                } catch (Exception e) {
                    log.warn("对象列表转字符串列表转换失败: {}", e.getMessage());
                    allResults.put("objects_to_string_list_error", e.getMessage());
                }
            }

            if (conversionCount == 0) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "没有有效的输入数据", "请提供 input_string、input_string_list 或 input_object_list 中的至少一种");
            }

            // 构建最终结果
            JSONObject finalResult = new JSONObject();
            finalResult.put("success", true);
            finalResult.put("message", "智能数据转换完成，共执行 " + conversionCount + " 种转换");
            finalResult.put("data", allResults);
            finalResult.put("conversion_count", conversionCount);

            return finalResult;

        } catch (Exception e) {
            log.error("智能数据转换失败", e);
            return JianyingProResponseUtil.systemError("智能数据转换", e);
        }
    }
    
    @Override
    public JSONObject addImages(JianyingProAddImagesRequest request) {
        try {
            log.info("开始Pro版一体化图片添加: {}", request.getSummary());

            // 参数验证
            if (request.getDraftUrl() == null || request.getDraftUrl().trim().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "草稿地址不能为空", "请提供有效的draft_url参数");
            }

            if (request.getImgs() == null || request.getImgs().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "图片列表不能为空", "请提供有效的imgs参数");
            }

            if (request.getTimelines() == null || request.getTimelines().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "时间线列表不能为空", "请提供有效的timelines参数");
            }

            // 第一步：Pro版内部生成图片信息（复制自稳定版imgs_infos逻辑）
            String imageInfosJson = generateImageInfosInternal(request);
            if (imageInfosJson == null) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "图片信息生成失败", "内部处理错误");
            }

            // 第二步：Pro版内部添加图片（复制自稳定版add_images逻辑）
            JSONObject result = addImagesInternal(request.getDraftUrl(), imageInfosJson, request);

            // 统一响应格式
            return JianyingProResponseUtil.formatResponse(result, "Pro版一体化图片添加");

        } catch (Exception e) {
            log.error("Pro版一体化图片添加失败", e);
            return JianyingProResponseUtil.systemError("Pro版一体化图片添加", e);
        }
    }

    @Override
    public JSONObject addCaptions(JianyingProAddCaptionsRequest request) {
        try {
            log.info("开始Pro版一体化字幕添加: {}", request.getSummary());

            // 参数验证
            if (request.getDraftUrl() == null || request.getDraftUrl().trim().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "草稿地址不能为空", "请提供有效的draft_url参数");
            }

            if (request.getTexts() == null || request.getTexts().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "文本列表不能为空", "请提供有效的texts参数");
            }

            if (request.getTimelines() == null || request.getTimelines().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "时间线列表不能为空", "请提供有效的timelines参数");
            }

            // 第一步：Pro版内部生成字幕信息（复制自稳定版caption_infos逻辑）
            String captionInfosJson = generateCaptionInfosInternal(request);
            if (captionInfosJson == null) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "字幕信息生成失败", "内部处理错误");
            }

            // 第二步：Pro版内部添加字幕（复制自稳定版add_captions逻辑）
            JSONObject result = addCaptionsInternal(request.getDraftUrl(), captionInfosJson, request);

            // 统一响应格式
            return JianyingProResponseUtil.formatResponse(result, "Pro版一体化字幕添加");

        } catch (Exception e) {
            log.error("Pro版一体化字幕添加失败", e);
            return JianyingProResponseUtil.systemError("Pro版一体化字幕添加", e);
        }
    }

    @Override
    public JSONObject addEffects(JianyingProAddEffectsRequest request) {
        try {
            log.info("开始Pro版一体化特效添加: {}", request.getSummary());

            // 参数验证
            if (request.getDraftUrl() == null || request.getDraftUrl().trim().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "草稿地址不能为空", "请提供有效的draft_url参数");
            }

            if (request.getEffects() == null || request.getEffects().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "特效列表不能为空", "请提供有效的effects参数");
            }

            if (request.getTimelines() == null || request.getTimelines().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "时间线列表不能为空", "请提供有效的timelines参数");
            }

            // 第一步：Pro版内部生成特效信息（复制自稳定版effect_infos逻辑）
            String effectInfosJson = generateEffectInfosInternal(request);
            if (effectInfosJson == null) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "特效信息生成失败", "内部处理错误");
            }

            // 第二步：Pro版内部添加特效（复制自稳定版add_effects逻辑）
            JSONObject result = addEffectsInternal(request.getDraftUrl(), effectInfosJson, request);

            // 统一响应格式
            return JianyingProResponseUtil.formatResponse(result, "Pro版一体化特效添加");

        } catch (Exception e) {
            log.error("Pro版一体化特效添加失败", e);
            return JianyingProResponseUtil.systemError("Pro版一体化特效添加", e);
        }
    }

    @Override
    public JSONObject addKeyframes(JianyingProAddKeyframesRequest request) {
        try {
            log.info("开始Pro版一体化关键帧添加: {}", request.getSummary());

            // 参数验证
            if (request.getDraftUrl() == null || request.getDraftUrl().trim().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "草稿地址不能为空", "请提供有效的draft_url参数");
            }

            if (request.getCtype() == null || request.getCtype().trim().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "关键帧类型不能为空", "请提供有效的ctype参数");
            }

            if (request.getOffsets() == null || request.getOffsets().trim().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "偏移量不能为空", "请提供有效的offsets参数");
            }

            if (request.getSegmentInfos() == null || request.getSegmentInfos().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "段信息不能为空", "请提供有效的segment_infos参数");
            }

            if (request.getValues() == null || request.getValues().trim().isEmpty()) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "关键帧值不能为空", "请提供有效的values参数");
            }

            // 第一步：Pro版内部生成关键帧信息（复制自稳定版keyframes_infos逻辑）
            String keyframesJson = generateKeyframeInfosInternal(request);
            if (keyframesJson == null) {
                return JianyingProResponseUtil.error(JianyingProErrorCode.PARAM_INCOMPLETE_003,
                    "关键帧信息生成失败", "内部处理错误");
            }

            // 第二步：Pro版内部添加关键帧（复制自稳定版add_keyframes逻辑）
            JSONObject result = addKeyframesInternal(request.getDraftUrl(), keyframesJson, request);

            // 统一响应格式
            return JianyingProResponseUtil.formatResponse(result, "Pro版一体化关键帧添加");

        } catch (Exception e) {
            log.error("Pro版一体化关键帧添加失败", e);
            return JianyingProResponseUtil.systemError("Pro版一体化关键帧添加", e);
        }
    }
    
    // ==================== 私有辅助方法 ====================

    /**
     * 检查是否包含音频时间线参数
     */
    private boolean hasAudioTimelineParams(JianyingProTimelinesRequest request) {
        return request.getAudioUrls() != null && !request.getAudioUrls().isEmpty();
    }

    /**
     * 检查是否包含自定义时间线参数
     */
    private boolean hasCustomTimelineParams(JianyingProTimelinesRequest request) {
        return request.getDuration() != null && request.getNum() != null;
    }

    /**
     * 生成音频时间线
     */
    private JSONObject generateAudioTimelines(JianyingProTimelinesRequest request) {
        try {
            log.info("生成音频时间线，音频数量: {}", request.getAudioUrls().size());

            // 构造原始请求对象
            org.jeecg.modules.jianying.dto.AudioTimelinesRequest originalRequest =
                new org.jeecg.modules.jianying.dto.AudioTimelinesRequest();
            originalRequest.setZjLinks(request.getAudioUrls());
            originalRequest.setAccessKey(request.getAccessKey());

            // 调用数据箱服务
            JSONObject result = databoxService.audioTimelines(originalRequest);

            log.info("音频时间线生成完成");
            return result;

        } catch (Exception e) {
            log.error("生成音频时间线失败", e);
            return JianyingProResponseUtil.systemError("音频时间线生成", e);
        }
    }

    /**
     * 生成自定义时间线
     */
    private JSONObject generateCustomTimelines(JianyingProTimelinesRequest request) {
        try {
            log.info("生成自定义时间线，时长: {}μs, 分段: {}", request.getDuration(), request.getNum());

            // 构造原始请求对象
            org.jeecg.modules.jianying.dto.TimelinesRequest originalRequest =
                new org.jeecg.modules.jianying.dto.TimelinesRequest();
            originalRequest.setZjDuration(request.getDuration());
            originalRequest.setZjNum(request.getNum());
            originalRequest.setZjStart(request.getStart() != null ? request.getStart().intValue() : 0);
            originalRequest.setZjType(request.getType() != null ? request.getType() : 0);
            originalRequest.setAccessKey(request.getAccessKey());

            // 调用数据箱服务
            JSONObject result = databoxService.timelines(originalRequest);

            log.info("自定义时间线生成完成");
            return result;

        } catch (Exception e) {
            log.error("生成自定义时间线失败", e);
            return JianyingProResponseUtil.systemError("自定义时间线生成", e);
        }
    }



    // ========== 旧方法已删除，现在使用*Internal方法实现真正的代码隔离 ==========



    /**
     * 从请求参数生成特效信息JSON
     * 智能识别effects、timelines等参数，自动生成符合add_effects接口要求的特效信息
     */
    private String generateEffectInfosFromParams(JianyingProAddEffectsRequest request) {
        try {
            // 检查是否有effects参数
            if (request.getEffects() == null || request.getEffects().isEmpty()) {
                log.warn("无法生成特效信息：effects参数为空");
                return null;
            }

            log.info("开始智能生成特效信息，特效数量: {}", request.getEffects().size());

            com.alibaba.fastjson.JSONArray effectsArray = new com.alibaba.fastjson.JSONArray();

            // 使用默认参数（稳定版标准）
            Double intensity = 1.0; // 默认强度
            Double alpha = 1.0; // 默认透明度

            // 处理时间线：如果提供了timelines，使用提供的；否则自动生成
            java.util.List<com.alibaba.fastjson.JSONObject> timelines = request.getTimelines();
            boolean hasCustomTimelines = timelines != null && !timelines.isEmpty();

            // 为每个特效名称生成对应的特效信息对象
            for (int i = 0; i < request.getEffects().size(); i++) {
                String effectTitle = request.getEffects().get(i);

                com.alibaba.fastjson.JSONObject effectInfo = new com.alibaba.fastjson.JSONObject();
                effectInfo.put("effect_title", effectTitle);

                // 设置时间线
                if (hasCustomTimelines && i < timelines.size()) {
                    // 使用提供的时间线
                    com.alibaba.fastjson.JSONObject timeline = timelines.get(i);
                    effectInfo.put("start", timeline.getLongValue("start"));
                    effectInfo.put("end", timeline.getLongValue("end"));
                } else {
                    // 自动生成时间线：每个特效默认5秒，依次排列
                    long startTime = i * 5000000L; // 微秒
                    long endTime = (i + 1) * 5000000L;
                    effectInfo.put("start", startTime);
                    effectInfo.put("end", endTime);
                }

                // 设置特效属性
                if (intensity != null) {
                    effectInfo.put("intensity", intensity);
                }

                if (alpha != null) {
                    effectInfo.put("alpha", alpha);
                }

                // 使用标准特效配置
                effectInfo.put("effect_type", "standard");
                effectInfo.put("blend_mode", "normal");

                effectsArray.add(effectInfo);
            }

            String result = effectsArray.toJSONString();
            log.info("特效信息生成成功，生成了{}个特效对象", effectsArray.size());
            log.debug("生成的特效信息: {}", result);

            return result;

        } catch (Exception e) {
            log.error("生成特效信息失败", e);
            return null;
        }
    }

    /**
     * 从请求参数生成关键帧信息JSON
     * 智能识别ctype、offsets、segment_infos、values等参数，自动生成符合add_keyframes接口要求的关键帧信息
     */
    private String generateKeyframeInfosFromParams(JianyingProAddKeyframesRequest request) {
        try {
            // 检查必要参数
            if (request.getCtype() == null || request.getCtype().trim().isEmpty()) {
                log.warn("无法生成关键帧信息：ctype参数为空");
                return null;
            }
            if (request.getOffsets() == null || request.getOffsets().trim().isEmpty()) {
                log.warn("无法生成关键帧信息：offsets参数为空");
                return null;
            }
            if (request.getSegmentInfos() == null || request.getSegmentInfos().isEmpty()) {
                log.warn("无法生成关键帧信息：segmentInfos参数为空");
                return null;
            }
            if (request.getValues() == null || request.getValues().isEmpty()) {
                log.warn("无法生成关键帧信息：values参数为空");
                return null;
            }

            log.info("开始智能生成关键帧信息，类型: {}, 段数量: {}",
                request.getCtype(), request.getSegmentInfos().size());

            com.alibaba.fastjson.JSONArray keyframesArray = new com.alibaba.fastjson.JSONArray();

            // 解析偏移量
            String[] offsetStrs = request.getOffsets().split("\\|");
            java.util.List<Double> offsetPercentages = new java.util.ArrayList<>();
            for (String offsetStr : offsetStrs) {
                try {
                    offsetPercentages.add(Double.parseDouble(offsetStr.trim()));
                } catch (NumberFormatException e) {
                    log.warn("无效的偏移量值: {}", offsetStr);
                    continue;
                }
            }

            if (offsetPercentages.isEmpty()) {
                log.warn("无法解析偏移量: {}", request.getOffsets());
                return null;
            }

            // 解析值
            String[] valueStrs = request.getValues().split("\\|");
            java.util.List<Double> values = new java.util.ArrayList<>();
            for (String valueStr : valueStrs) {
                try {
                    values.add(Double.parseDouble(valueStr.trim()));
                } catch (NumberFormatException e) {
                    log.warn("无效的值: {}", valueStr);
                    continue;
                }
            }

            if (values.isEmpty()) {
                log.warn("无法解析值: {}", request.getValues());
                return null;
            }

            // 获取关键帧属性
            String property = request.getCtype();

            // 为每个段生成关键帧
            for (com.alibaba.fastjson.JSONObject segmentInfo : request.getSegmentInfos()) {
                String segmentId = segmentInfo.getString("id");
                if (segmentId == null || segmentId.trim().isEmpty()) {
                    log.warn("段信息缺少id字段，跳过");
                    continue;
                }

                // 获取段的时间信息
                Long segmentStart = segmentInfo.getLong("start");
                Long segmentEnd = segmentInfo.getLong("end");
                if (segmentStart == null || segmentEnd == null) {
                    log.warn("段信息缺少时间信息，跳过段: {}", segmentId);
                    continue;
                }

                long segmentDuration = segmentEnd - segmentStart;

                // 为每个偏移量和值生成关键帧
                for (int i = 0; i < Math.min(offsetPercentages.size(), values.size()); i++) {
                    double offsetPercentage = offsetPercentages.get(i);
                    double value = values.get(i);

                    // 计算实际偏移量（微秒）
                    long offset = segmentStart + (long)(segmentDuration * offsetPercentage / 100.0);

                    com.alibaba.fastjson.JSONObject keyframe = new com.alibaba.fastjson.JSONObject();
                    keyframe.put("offset", offset);
                    keyframe.put("property", property);
                    keyframe.put("segment_id", segmentId);
                    keyframe.put("value", value);

                    // 使用默认曲线类型
                    keyframe.put("curve_type", "Linear");

                    keyframesArray.add(keyframe);
                }
            }

            String result = keyframesArray.toJSONString();
            log.info("关键帧信息生成成功，生成了{}个关键帧", keyframesArray.size());
            log.debug("生成的关键帧信息: {}", result);

            return result;

        } catch (Exception e) {
            log.error("生成关键帧信息失败", e);
            return null;
        }
    }



    /**
     * 字符串转列表（新版智能转换）
     */
    private JSONObject convertStringToList(String inputString, String delimiter) {
        try {
            log.info("执行字符串转列表转换: {}", inputString.substring(0, Math.min(50, inputString.length())));

            // 使用指定分隔符分割字符串
            String[] items = inputString.split(delimiter != null ? delimiter : ",");
            java.util.List<String> resultList = new java.util.ArrayList<>();

            for (String item : items) {
                String trimmedItem = item.trim();
                if (!trimmedItem.isEmpty()) {
                    resultList.add(trimmedItem);
                }
            }

            // 构建结果
            JSONObject result = new JSONObject();
            result.put("list", resultList);
            result.put("count", resultList.size());
            result.put("delimiter_used", delimiter != null ? delimiter : ",");

            log.info("字符串转列表转换完成，生成 {} 个项目", resultList.size());
            return result;

        } catch (Exception e) {
            log.error("字符串转列表转换失败", e);
            throw new RuntimeException("字符串转列表转换失败: " + e.getMessage());
        }
    }

    /**
     * 字符串列表转对象（新版智能转换）- 与基础版保持一致
     */
    private JSONObject convertStringListToObjects(java.util.List<String> inputList) {
        try {
            log.info("执行字符串列表转对象转换，输入项目数: {}, 输入内容: {}", inputList.size(), inputList);

            java.util.List<JSONObject> resultList = new java.util.ArrayList<>();

            for (int i = 0; i < inputList.size(); i++) {
                String item = inputList.get(i);
                log.info("处理第 {} 项: '{}'", i, item);

                if (item != null && !item.trim().isEmpty()) {
                    JSONObject obj = new JSONObject();
                    String trimmedItem = item.trim();
                    obj.put("output", trimmedItem); // 与基础版一致，只有output字段，没有index
                    resultList.add(obj);
                    log.info("创建对象: output='{}', 对象内容: {}", trimmedItem, obj.toJSONString());
                } else {
                    log.warn("跳过空项: '{}'", item);
                }
            }

            // 构建结果
            JSONObject result = new JSONObject();
            result.put("objects", resultList);
            result.put("count", resultList.size());

            log.info("字符串列表转对象转换完成，生成 {} 个对象", resultList.size());
            log.info("结果列表内容: {}", resultList);
            log.info("最终结果对象: {}", result.toJSONString());

            // 验证每个对象的内容
            for (int i = 0; i < resultList.size(); i++) {
                JSONObject obj = resultList.get(i);
                log.info("对象 {}: {}", i, obj.toJSONString());
                log.info("对象 {} 的output字段: {}", i, obj.get("output"));
            }

            return result;

        } catch (Exception e) {
            log.error("字符串列表转对象转换失败", e);
            throw new RuntimeException("字符串列表转对象转换失败: " + e.getMessage());
        }
    }

    /**
     * 对象列表转字符串列表（智能转换）- 支持智能字段提取
     */
    private JSONObject convertObjectsToStringList(java.util.List<Object> inputList, String extractField) {
        try {
            log.info("执行智能对象列表转字符串列表转换，输入对象数: {}，指定字段: {}", inputList.size(), extractField);

            java.util.List<String> resultList = new java.util.ArrayList<>();
            String actualFieldExtracted = null;
            boolean isUserSpecified = extractField != null && !extractField.trim().isEmpty();

            for (Object obj : inputList) {
                java.util.List<String> extractedValues = new java.util.ArrayList<>();

                if (obj instanceof java.util.Map) {
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, Object> map = (java.util.Map<String, Object>) obj;
                    extractedValues = extractFromMap(map, extractField, isUserSpecified);
                } else if (obj instanceof JSONObject) {
                    JSONObject jsonObj = (JSONObject) obj;
                    extractedValues = extractFromJsonObject(jsonObj, extractField, isUserSpecified);
                } else {
                    // 如果不是Map或JSONObject，直接转换为字符串
                    extractedValues.add(obj.toString());
                }

                // 添加提取的值到结果列表
                for (String value : extractedValues) {
                    if (value != null && !value.trim().isEmpty()) {
                        resultList.add(value.trim());
                    }
                }
            }

            // 确定实际提取的字段描述
            if (isUserSpecified) {
                actualFieldExtracted = extractField;
            } else {
                actualFieldExtracted = "智能提取（单字段对象提取唯一字段，多字段对象提取所有字段）";
            }

            // 构建结果
            JSONObject result = new JSONObject();
            result.put("list", resultList);
            result.put("count", resultList.size());
            result.put("field_extracted", actualFieldExtracted);
            result.put("extraction_mode", isUserSpecified ? "指定字段" : "智能提取");

            log.info("智能对象列表转字符串列表转换完成，生成 {} 个字符串，提取模式: {}",
                    resultList.size(), isUserSpecified ? "指定字段" : "智能提取");
            return result;

        } catch (Exception e) {
            log.error("智能对象列表转字符串列表转换失败", e);
            throw new RuntimeException("智能对象列表转字符串列表转换失败: " + e.getMessage());
        }
    }

    /**
     * 从Map对象中智能提取字段值
     */
    private java.util.List<String> extractFromMap(java.util.Map<String, Object> map, String extractField, boolean isUserSpecified) {
        java.util.List<String> values = new java.util.ArrayList<>();

        if (isUserSpecified) {
            // 用户指定字段，只提取该字段
            Object value = map.get(extractField);
            if (value != null) {
                values.add(value.toString());
            }
        } else {
            // 智能提取
            if (map.size() == 1) {
                // 单字段对象：提取唯一字段的值
                Object value = map.values().iterator().next();
                if (value != null) {
                    values.add(value.toString());
                }
            } else {
                // 多字段对象：提取所有字段的值
                for (Object value : map.values()) {
                    if (value != null) {
                        values.add(value.toString());
                    }
                }
            }
        }

        return values;
    }

    /**
     * 从JSONObject中智能提取字段值
     */
    private java.util.List<String> extractFromJsonObject(JSONObject jsonObj, String extractField, boolean isUserSpecified) {
        java.util.List<String> values = new java.util.ArrayList<>();

        if (isUserSpecified) {
            // 用户指定字段，只提取该字段
            Object value = jsonObj.get(extractField);
            if (value != null) {
                values.add(value.toString());
            }
        } else {
            // 智能提取
            if (jsonObj.size() == 1) {
                // 单字段对象：提取唯一字段的值
                Object value = jsonObj.values().iterator().next();
                if (value != null) {
                    values.add(value.toString());
                }
            } else {
                // 多字段对象：提取所有字段的值
                for (Object value : jsonObj.values()) {
                    if (value != null) {
                        values.add(value.toString());
                    }
                }
            }
        }

        return values;
    }

    // ========== Pro版独立处理逻辑（复制自稳定版，实现真正的代码隔离） ==========

    /**
     * Pro版内部生成视频信息（复制自稳定版video_infos的完整处理逻辑）
     * 实现真正的代码隔离，不依赖稳定版Service
     */
    private String generateVideoInfosInternal(JianyingProAddVideosRequest request) {
        try {
            log.info("Pro版开始生成视频信息，视频数量: {}", request.getVideoUrls().size());

            // 参数处理（复制自稳定版逻辑）
            java.util.List<String> videoUrls = request.getVideoUrls();
            java.util.List<JSONObject> timelines = request.getTimelines();
            String mask = request.getMask();
            Integer height = request.getHeight() != null ? request.getHeight() : 1080;
            Integer width = request.getWidth() != null ? request.getWidth() : 1920;
            String transition = request.getTransition();
            Integer transitionDuration = request.getTransitionDuration();
            Double volume = request.getVolume() != null ? request.getVolume() : 1.0;

            // 添加遗漏的视频属性参数（复制自稳定版add_videos逻辑）
            Double alpha = request.getAlpha();
            Double scaleX = request.getScaleX();
            Double scaleY = request.getScaleY();
            Double transformX = request.getTransformX();
            Double transformY = request.getTransformY();

            // 参数验证（复制自稳定版逻辑）
            if (videoUrls == null || videoUrls.isEmpty()) {
                throw new RuntimeException("视频列表不能为空");
            }

            // 参数验证：timelines是必填的（与稳定版保持一致）
            if (timelines == null || timelines.isEmpty()) {
                throw new RuntimeException("时间线不能为空");
            }

            // 生成视频信息JSON字符串（复制自稳定版的完整逻辑）
            StringBuilder jsonBuilder = new StringBuilder("[");
            for (int i = 0; i < timelines.size(); i++) {
                JSONObject timeline = timelines.get(i);

                if (i > 0) jsonBuilder.append(",");

                // 视频分配逻辑：按索引分配，超出范围则为空字符串
                String videoUrl = (i < videoUrls.size()) ? videoUrls.get(i) : "";

                // 计算duration
                long duration = timeline.getLong("end") - timeline.getLong("start");

                // 构建视频信息对象（完全复制稳定版的字段顺序和逻辑）
                jsonBuilder.append("{\"video_url\":\"").append(videoUrl).append("\"")
                           .append(",\"duration\":").append(duration)
                           .append(",\"width\":").append(width)
                           .append(",\"height\":").append(height)
                           .append(",\"start\":").append(timeline.getLong("start"))
                           .append(",\"end\":").append(timeline.getLong("end"))
                           .append(",\"volume\":").append(volume);

                // 可选参数（复制自稳定版逻辑）
                if (mask != null) {
                    jsonBuilder.append(",\"mask\":\"").append(mask).append("\"");
                }
                if (transition != null) {
                    jsonBuilder.append(",\"transition\":\"").append(transition).append("\"");
                }
                if (transitionDuration != null) {
                    jsonBuilder.append(",\"transition_duration\":").append(transitionDuration);
                }

                // 添加遗漏的视频属性参数到JSON中（修复关键问题）
                if (alpha != null) {
                    jsonBuilder.append(",\"alpha\":").append(alpha);
                }
                if (scaleX != null) {
                    jsonBuilder.append(",\"scale_x\":").append(scaleX);
                }
                if (scaleY != null) {
                    jsonBuilder.append(",\"scale_y\":").append(scaleY);
                }
                if (transformX != null) {
                    jsonBuilder.append(",\"transform_x\":").append(transformX);
                }
                if (transformY != null) {
                    jsonBuilder.append(",\"transform_y\":").append(transformY);
                }

                jsonBuilder.append("}");
            }
            jsonBuilder.append("]");

            String result = jsonBuilder.toString();
            log.info("Pro版视频信息生成成功，生成了{}个视频对象", timelines.size());
            log.debug("生成的视频信息: {}", result);

            return result;

        } catch (Exception e) {
            log.error("Pro版生成视频信息失败", e);
            return null;
        }
    }

    /**
     * Pro版内部添加视频（复制自稳定版add_videos的完整处理逻辑）
     * 实现真正的代码隔离，不依赖稳定版Service
     */
    private JSONObject addVideosInternal(String draftUrl, String videoInfosJson, JianyingProAddVideosRequest request) {
        try {
            log.info("Pro版开始添加视频到草稿，视频数量: {}", request.getVideoUrls().size());

            // 第1步：下载并解析草稿文件（复制自稳定版逻辑）
            JSONObject draft = downloadAndParseDraft(draftUrl);
            if (draft == null) {
                return createErrorResult("草稿文件下载失败", "PRO_DRAFT_DOWNLOAD_ERROR");
            }

            // 第2步：解析视频信息数组（复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray videoInfos;
            try {
                videoInfos = com.alibaba.fastjson.JSONArray.parseArray(videoInfosJson);
                if (videoInfos == null || videoInfos.isEmpty()) {
                    return createErrorResult("视频信息解析失败或为空", "PRO_VIDEO_INFOS_PARSE_ERROR");
                }
            } catch (Exception e) {
                log.error("Pro版解析视频信息失败: {}", e.getMessage());
                return createErrorResult("视频信息JSON格式错误: " + e.getMessage(), "PRO_VIDEO_INFOS_FORMAT_ERROR");
            }

            // 第3步：处理视频添加逻辑（复制自稳定版逻辑）
            JSONObject result = processVideoAddition(draft, videoInfos, draftUrl, request);
            if (result.containsKey("error")) {
                return result;
            }

            // 第4步：保存草稿文件（复制自稳定版逻辑）
            boolean saveSuccess = saveDraftFile(draftUrl, draft);
            if (!saveSuccess) {
                return createErrorResult("草稿文件保存失败", "PRO_DRAFT_SAVE_ERROR");
            }

            log.info("Pro版视频添加完成，草稿已保存: {}", draftUrl);
            return result;

        } catch (Exception e) {
            log.error("Pro版添加视频失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", "Pro版添加视频失败: " + e.getMessage());
            errorResult.put("error_code", "PRO_ADD_VIDEOS_ERROR");
            return errorResult;
        }
    }

    /**
     * 处理视频添加逻辑（复制自稳定版逻辑）
     */
    private JSONObject processVideoAddition(JSONObject draft, com.alibaba.fastjson.JSONArray videoInfos, String draftUrl, JianyingProAddVideosRequest request) {
        try {
            log.info("Pro版处理视频添加逻辑，视频数量: {}", videoInfos.size());

            // 第1步：确保materials对象存在且有正确的结构（复制自稳定版逻辑）
            JSONObject materials = draft.getJSONObject("materials");
            if (materials == null) {
                materials = new JSONObject(new java.util.LinkedHashMap<>());
                draft.put("materials", materials);
            }

            // 确保materials.videos数组存在
            com.alibaba.fastjson.JSONArray videos = materials.getJSONArray("videos");
            if (videos == null) {
                videos = new com.alibaba.fastjson.JSONArray();
                materials.put("videos", videos);
            }

            // 第2步：确保tracks数组存在（复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray tracks = draft.getJSONArray("tracks");
            if (tracks == null) {
                tracks = new com.alibaba.fastjson.JSONArray();
                draft.put("tracks", tracks);
            }

            // 第3步：获取画布尺寸作为坐标转换基准（复制自稳定版逻辑）
            JSONObject canvasConfig = draft.getJSONObject("canvas_config");
            int canvasWidth = canvasConfig != null ? canvasConfig.getIntValue("width") : 1920;
            int canvasHeight = canvasConfig != null ? canvasConfig.getIntValue("height") : 1080;

            // 确保画布尺寸有效
            if (canvasWidth <= 0 || canvasHeight <= 0) {
                log.warn("Pro版画布尺寸无效: {}x{}, 使用默认值1920x1080", canvasWidth, canvasHeight);
                canvasWidth = 1920;
                canvasHeight = 1080;
            }
            log.info("Pro版使用画布尺寸: {}x{}", canvasWidth, canvasHeight);

            // 第4步：获取或创建统一文件夹ID（复制自稳定版逻辑）
            String unifiedFolderId = extractOrCreateUnifiedFolderId(draft);
            log.info("Pro版使用统一文件夹ID: {}", unifiedFolderId);

            // 第5步：创建新的视频轨道（复制自稳定版逻辑）
            String newTrackId = java.util.UUID.randomUUID().toString().toUpperCase();
            JSONObject newTrack = createVideoTrack(newTrackId);
            tracks.add(newTrack);

            // 第4步：处理转场效果（如果有）- 完全复制自稳定版addVideoTransitionMaterials逻辑
            java.util.List<String> transitionIds = addVideoTransitionMaterialsInternal(draft, videoInfos, request.getTransition());
            log.info("Pro版添加转场材料列表: transitionIds={}, transition={}", transitionIds, request.getTransition());

            // 第5步：处理每个视频，添加材料和片段（复制自稳定版逻辑）
            java.util.List<String> videoIds = new java.util.ArrayList<>();
            java.util.List<String> segmentIds = new java.util.ArrayList<>();
            java.util.List<JSONObject> segmentTimes = new java.util.ArrayList<>();
            java.util.List<Integer> videoWidths = new java.util.ArrayList<>(); // 收集视频宽度信息
            java.util.List<Integer> videoHeights = new java.util.ArrayList<>(); // 收集视频高度信息

            for (int i = 0; i < videoInfos.size(); i++) {
                JSONObject videoInfo = videoInfos.getJSONObject(i);
                try {
                    // 添加视频材料到materials.videos
                    String videoId = addVideoMaterialInternal(draft, videoInfo, request, i, unifiedFolderId);
                    videoIds.add(videoId);

                    // 获取对应的转场ID（最后一个视频没有转场，与稳定版一致）
                    String transitionId = (i < transitionIds.size()) ? transitionIds.get(i) : null;

                    // 添加视频片段到轨道（传递转场ID、画布尺寸和draft）
                    String segmentId = addVideoSegmentInternal(newTrack, videoInfo, videoId, request, i, transitionId, canvasWidth, canvasHeight, draft);
                    segmentIds.add(segmentId);

                    // 保存时间信息用于生成segment_infos
                    JSONObject timeInfo = new JSONObject();
                    long videoStart = videoInfo.getLong("start") != null ? videoInfo.getLong("start") : i * 5000000L;
                    long videoEnd = videoInfo.getLong("end") != null ? videoInfo.getLong("end") : (i + 1) * 5000000L;
                    timeInfo.put("start", videoStart);
                    timeInfo.put("end", videoEnd);
                    segmentTimes.add(timeInfo);

                    // 保存视频尺寸信息用于生成segment_infos
                    final int videoWidth = videoInfo.getIntValue("width") > 0 ?
                                          videoInfo.getIntValue("width") : 1080;
                    final int videoHeight = videoInfo.getIntValue("height") > 0 ?
                                           videoInfo.getIntValue("height") : 1920;
                    videoWidths.add(videoWidth);
                    videoHeights.add(videoHeight);

                    log.info("Pro版视频处理成功[{}]: videoId={}, segmentId={}", i, videoId, segmentId);

                } catch (Exception e) {
                    log.error("Pro版处理视频失败[{}]: {}", i, videoInfo.getString("video_url"), e);
                    // 创建占位符ID保持数组长度一致
                    String placeholderVideoId = "placeholder_video_" + i;
                    String placeholderSegmentId = "placeholder_segment_" + i;
                    videoIds.add(placeholderVideoId);
                    segmentIds.add(placeholderSegmentId);
                    JSONObject placeholderTime = new JSONObject();
                    placeholderTime.put("start", i * 5000000L);
                    placeholderTime.put("end", (i + 1) * 5000000L);
                    segmentTimes.add(placeholderTime);

                    // 为占位符创建默认尺寸信息
                    final int defaultWidth = videoInfo.getIntValue("width") > 0 ?
                                            videoInfo.getIntValue("width") : 1080;
                    final int defaultHeight = videoInfo.getIntValue("height") > 0 ?
                                             videoInfo.getIntValue("height") : 1920;
                    videoWidths.add(defaultWidth);
                    videoHeights.add(defaultHeight);
                }
            }

            // 第5步：返回处理结果（完全复制自稳定版generateAddVideosResponseWithWarnings格式）
            JSONObject result = new JSONObject(new java.util.LinkedHashMap<>());
            result.put("success", true); // 添加success字段以保持一致性
            result.put("video_ids", videoIds);
            result.put("draft_url", draftUrl);
            result.put("segment_ids", segmentIds);
            result.put("track_id", newTrackId);

            // 生成segment_infos（使用真实的时间信息和尺寸信息）
            com.alibaba.fastjson.JSONArray segmentInfos = new com.alibaba.fastjson.JSONArray();
            if (segmentIds != null && segmentTimes != null) {
                for (int i = 0; i < segmentIds.size() && i < segmentTimes.size(); i++) {
                    JSONObject segmentInfo = new JSONObject();
                    segmentInfo.put("segment_id", segmentIds.get(i));

                    JSONObject timeInfo = segmentTimes.get(i);
                    long start = timeInfo.getLong("start");
                    long end = timeInfo.getLong("end");
                    segmentInfo.put("start", start);
                    segmentInfo.put("end", end);
                    segmentInfo.put("duration", end - start);

                    // 添加视频尺寸信息
                    if (videoWidths != null && i < videoWidths.size()) {
                        segmentInfo.put("width", videoWidths.get(i));
                    }
                    if (videoHeights != null && i < videoHeights.size()) {
                        segmentInfo.put("height", videoHeights.get(i));
                    }

                    segmentInfos.add(segmentInfo);
                }
            }
            result.put("segment_infos", segmentInfos);

            // 添加警告信息（如果有）- 暂时为空，后续可扩展
            java.util.List<String> warnings = new java.util.ArrayList<>();
            if (!warnings.isEmpty()) {
                result.put("warnings", warnings);
            }

            log.info("Pro版视频添加逻辑处理完成，轨道ID: {}, 视频数量: {}", newTrackId, videoIds.size());
            return result;

        } catch (Exception e) {
            log.error("Pro版处理视频添加失败: {}", e.getMessage());
            return createErrorResult("视频添加处理失败: " + e.getMessage(), "PRO_VIDEO_PROCESS_ERROR");
        }
    }

    // ========== Pro版视频处理辅助方法（复制自稳定版完整逻辑） ==========

    /**
     * 创建视频轨道（复制自稳定版逻辑）
     */
    private JSONObject createVideoTrack(String trackId) {
        JSONObject track = new JSONObject(new java.util.LinkedHashMap<>());
        track.put("attribute", 0);
        track.put("flag", 0);
        track.put("id", trackId);
        track.put("is_default_name", true);
        track.put("name", "");
        track.put("segments", new com.alibaba.fastjson.JSONArray());
        track.put("type", "video");
        return track;
    }

    /**
     * 添加视频材料到materials.videos（复制自稳定版逻辑）
     */
    private String addVideoMaterialInternal(JSONObject draft, JSONObject videoInfo, JianyingProAddVideosRequest request, int index, String unifiedFolderId) {
        try {
            String videoUrl = videoInfo.getString("video_url");
            log.info("Pro版开始添加视频材料[{}]: {}", index, videoUrl);

            // 生成视频材料ID
            String videoMaterialId = java.util.UUID.randomUUID().toString();

            // 创建视频材料对象（复制自稳定版逻辑）
            JSONObject videoMaterial = createVideoMaterialObject(videoMaterialId, videoUrl, videoInfo, request, index, unifiedFolderId);

            // 添加到materials.videos数组
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray videos = materials.getJSONArray("videos");
            videos.add(videoMaterial);

            log.info("Pro版视频材料添加成功[{}]: ID={}, URL={}", index, videoMaterialId, videoUrl);
            return videoMaterialId;

        } catch (Exception e) {
            log.error("Pro版添加视频材料失败[{}]", index, e);
            throw new RuntimeException("添加视频材料失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加视频转场材料到materials.transitions，返回转场ID列表（完全复制自稳定版addVideoTransitionMaterials逻辑）
     */
    private java.util.List<String> addVideoTransitionMaterialsInternal(JSONObject draft, com.alibaba.fastjson.JSONArray videoInfos, String transitionName) {
        try {
            int videoCount = videoInfos.size();
            log.info("Pro版开始添加视频转场材料 - 视频数量: {}, 转场数量: {}", videoCount, videoCount - 1);

            if (videoCount <= 1) {
                log.info("Pro版视频数量不足，无需添加转场");
                return new java.util.ArrayList<>();
            }

            java.util.List<String> transitionIds = new java.util.ArrayList<>();

            // 添加到materials.transitions
            JSONObject materials = draft.getJSONObject("materials");
            if (materials == null) {
                materials = new JSONObject();
                draft.put("materials", materials);
            }

            com.alibaba.fastjson.JSONArray transitions = materials.getJSONArray("transitions");
            if (transitions == null) {
                transitions = new com.alibaba.fastjson.JSONArray();
                materials.put("transitions", transitions);
            }

            // 为每个转场位置创建转场材料（videoCount - 1个转场）
            for (int i = 0; i < videoCount - 1; i++) {
                // 修复：只有在用户明确指定转场参数时才添加转场效果，匹配稳定版行为
                if (transitionName != null && !transitionName.trim().isEmpty()) {
                    String transitionId = java.util.UUID.randomUUID().toString().toUpperCase();

                    // 默认转场时长（与稳定版一致）
                    Long transitionDuration = 500000L; // 默认0.5秒

                    // 创建转场对象，集成真实ID获取（复用稳定版的方法）
                    JSONObject transition = createTransitionObjectInternal(transitionId, transitionName, transitionDuration);

                    transitions.add(transition);
                    transitionIds.add(transitionId);
                    log.info("Pro版视频转场材料添加成功[{}]: ID={}, 名称={}, 持续时间={}ms",
                            i, transitionId, transitionName, transitionDuration / 1000);
                } else {
                    // 用户没有指定转场，不添加转场效果（匹配稳定版行为）
                    transitionIds.add(null);
                    log.info("Pro版视频[{}]未指定转场，跳过转场添加", i);
                }
            }

            log.info("Pro版视频转场材料添加完成 - 总数: {}", videoCount - 1);
            return transitionIds;

        } catch (Exception e) {
            log.error("Pro版添加视频转场材料失败", e);
            throw new RuntimeException("添加视频转场材料失败: " + e.getMessage(), e);
        }
    }



    /**
     * 添加转场材料到materials.transitions（完全复制自稳定版逻辑，使用真实ID查找）
     */
    private String addTransitionMaterialInternal(JSONObject draft, String transitionName) {
        try {
            // 生成转场材料ID
            String transitionId = java.util.UUID.randomUUID().toString().toUpperCase();

            // 获取materials对象
            JSONObject materials = draft.getJSONObject("materials");
            if (materials == null) {
                materials = new JSONObject();
                draft.put("materials", materials);
            }

            // 获取或创建transitions数组
            com.alibaba.fastjson.JSONArray transitions = materials.getJSONArray("transitions");
            if (transitions == null) {
                transitions = new com.alibaba.fastjson.JSONArray();
                materials.put("transitions", transitions);
            }

            // 创建转场材料对象（完全复制自稳定版createVideoTransitionObject逻辑）
            JSONObject transitionMaterial = createTransitionObjectInternal(transitionId, transitionName, 500000L);

            // 添加到transitions数组
            transitions.add(transitionMaterial);

            log.info("Pro版转场材料添加成功: transitionId={}, name={}", transitionId, transitionName);
            return transitionId;

        } catch (Exception e) {
            log.error("Pro版添加转场材料失败: {}", e.getMessage(), e);
            throw new RuntimeException("添加转场材料失败: " + e.getMessage());
        }
    }

    /**
     * 创建转场对象，集成真实ID获取（完全复制自稳定版createVideoTransitionObject逻辑）
     */
    private JSONObject createTransitionObjectInternal(String transitionId, String transitionName, Long transitionDuration) {
        try {
            log.info("Pro版创建转场对象: id={}, name={}, duration={}", transitionId, transitionName, transitionDuration);

            // 使用ID解析服务获取真实转场ID
            org.jeecg.modules.jianyingpro.service.internal.JianyingProIdResolverService.TransitionInfo transitionInfo =
                jianyingProIdResolverService.findTransitionByName(transitionName);

            JSONObject transition = new JSONObject();
            transition.put("duration", transitionDuration);
            transition.put("id", transitionId);
            transition.put("name", transitionName);
            transition.put("panel", "video");
            transition.put("path", "");
            transition.put("platform", "all");
            transition.put("request_id", "");
            transition.put("type", "transition");

            if (transitionInfo != null) {
                transition.put("category_id", "");
                transition.put("category_name", transitionInfo.getCategory() != null ? transitionInfo.getCategory() : "基础");
                transition.put("material_type", "transition");
                transition.put("resource_id", transitionInfo.getResourceId());
                transition.put("effect_id", transitionInfo.getEffectId());
                transition.put("is_overlap", transitionInfo.isOverlap());
                log.info("Pro版转场真实ID获取成功: resource_id={}, effect_id={}",
                        transitionInfo.getResourceId(), transitionInfo.getEffectId());
            } else {
                // 不使用降级处理，直接抛出错误
                log.error("Pro版转场真实ID获取失败: {}", transitionName);
                throw new RuntimeException("转场真实ID获取失败: " + transitionName + "，请检查转场API服务");
            }

            return transition;

        } catch (Exception e) {
            log.error("Pro版创建转场对象失败: id={}, name={}", transitionId, transitionName, e);
            throw new RuntimeException("创建转场对象失败: " + transitionName, e);
        }
    }

    /**
     * 添加视频片段到轨道（复制自稳定版逻辑，支持转场效果、画布尺寸和mask材料创建）
     */
    private String addVideoSegmentInternal(JSONObject track, JSONObject videoInfo, String videoId, JianyingProAddVideosRequest request, int index, String transitionId, int canvasWidth, int canvasHeight, JSONObject draft) {
        try {
            log.debug("Pro版开始添加视频片段[{}]: videoId={}", index, videoId);

            // 生成片段ID
            String segmentId = java.util.UUID.randomUUID().toString();

            // 使用videoInfo中的实际时间范围
            long startTime = videoInfo.getLong("start") != null ? videoInfo.getLong("start") : index * 5000000L;
            long endTime = videoInfo.getLong("end") != null ? videoInfo.getLong("end") : (index + 1) * 5000000L;
            long duration = endTime - startTime;

            // 创建视频片段对象（复制自稳定版逻辑，使用传入的画布尺寸用于坐标转换和转场ID）
            JSONObject segment = createVideoSegmentObject(segmentId, videoId, startTime, endTime, duration, videoInfo, request, index, canvasWidth, canvasHeight, transitionId, draft);

            // 添加到轨道的segments数组
            com.alibaba.fastjson.JSONArray segments = track.getJSONArray("segments");
            if (segments == null) {
                segments = new com.alibaba.fastjson.JSONArray();
                track.put("segments", segments);
            }
            segments.add(segment);

            log.info("Pro版视频片段添加成功[{}]: ID={}, 时间范围={}~{}", index, segmentId, startTime, endTime);
            return segmentId;

        } catch (Exception e) {
            log.error("Pro版添加视频片段失败[{}]", index, e);
            throw new RuntimeException("添加视频片段失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建视频材料对象（完全复制自稳定版createVideoMaterialWithOriginalURL逻辑）
     */
    private JSONObject createVideoMaterialObject(String videoMaterialId, String videoUrl, JSONObject videoInfo, JianyingProAddVideosRequest request, int index, String unifiedFolderId) {
        // 获取视频时长（从videoInfo中获取，如果没有则使用默认值）
        final long videoDuration = videoInfo.getLongValue("duration") > 0 ?
                                   videoInfo.getLongValue("duration") : 5000000L;

        // 获取视频尺寸参数（从videoInfo中获取，支持自定义）
        final int videoWidth = videoInfo.getIntValue("width") > 0 ?
                              videoInfo.getIntValue("width") : 1080;
        final int videoHeight = videoInfo.getIntValue("height") > 0 ?
                               videoInfo.getIntValue("height") : 1920;

        // 生成随机的material_name（UUID格式，与稳定版一致）
        String materialName = java.util.UUID.randomUUID().toString();

        // 判断URL有效性
        boolean urlValid = videoUrl != null && !videoUrl.trim().isEmpty();
        String originalUrl = urlValid ? videoUrl : "";

        JSONObject videoMaterial = new JSONObject(new java.util.LinkedHashMap<>());

        // 基础信息（完全复制自稳定版）
        videoMaterial.put("id", videoMaterialId);
        videoMaterial.put("material_id", videoMaterialId);
        videoMaterial.put("material_name", materialName);
        videoMaterial.put("material_url", originalUrl); // 直接使用原始URL

        // 修复：path字段使用Electron期望的Windows路径格式（与稳定版完全一致）
        String electronPath = urlValid ?
            generateUnifiedFolderPath(videoMaterialId, originalUrl, unifiedFolderId) :
            originalUrl;
        videoMaterial.put("path", electronPath);

        videoMaterial.put("type", "video");
        videoMaterial.put("source_platform", urlValid ? "external" : "local"); // 标识外部URL

        // 视频属性（支持自定义参数）
        videoMaterial.put("duration", videoDuration);
        videoMaterial.put("width", videoWidth);
        videoMaterial.put("height", videoHeight);
        videoMaterial.put("has_audio", true);

        // 标准字段（完全复制自稳定版）
        videoMaterial.put("aigc_type", "none");
        videoMaterial.put("audio_fade", null);
        videoMaterial.put("cartoon_path", "");
        videoMaterial.put("category_id", "");
        videoMaterial.put("category_name", "");
        videoMaterial.put("check_flag", 63487);

        // 裁剪信息（完全复制自稳定版）
        videoMaterial.put("crop", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("lower_left_x", 0);
            put("lower_left_y", 1);
            put("lower_right_x", 1);
            put("lower_right_y", 1);
            put("upper_left_x", 0);
            put("upper_left_y", 0);
            put("upper_right_x", 1);
            put("upper_right_y", 0);
        }});

        // 其他必要字段（完全复制自稳定版）
        videoMaterial.put("create_time", System.currentTimeMillis() * 1000); // 微秒时间戳
        videoMaterial.put("extra_info", "");
        videoMaterial.put("file_Path", "");
        videoMaterial.put("fps", 30.0);
        videoMaterial.put("freeze", null);
        videoMaterial.put("freeze_cover_path", "");
        videoMaterial.put("import_time", System.currentTimeMillis() * 1000);
        videoMaterial.put("import_time_ms", System.currentTimeMillis());
        videoMaterial.put("intensifies_audio_path", "");
        videoMaterial.put("intensifies_path", "");
        videoMaterial.put("is_unified_beauty_mode", false);
        videoMaterial.put("local_material_id", "");
        videoMaterial.put("material_type", "video");
        videoMaterial.put("metetype", "");
        videoMaterial.put("roughcut_time_range", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", videoDuration);
            put("start", 0L);
        }});
        videoMaterial.put("source_platform", urlValid ? "external" : "local");
        videoMaterial.put("stable", new JSONObject());
        videoMaterial.put("team_id", "");
        videoMaterial.put("text_info", new JSONObject());
        videoMaterial.put("video_algorithm", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("algorithms", new com.alibaba.fastjson.JSONArray());
            put("deflicker", new JSONObject());
            put("motion_blur_config", new JSONObject());
            put("noise_reduction", new JSONObject());
            put("path", "");
            put("time_range", new JSONObject());
        }});

        // 客户端字段（保留原始URL信息）
        videoMaterial.put("download_url", originalUrl);
        videoMaterial.put("file_name", "");
        videoMaterial.put("original_url", originalUrl); // 新增：保留原始URL用于调试

        return videoMaterial;
    }

    /**
     * 生成统一文件夹的Windows路径格式（与稳定版完全一致）
     * 格式：##_draftpath_placeholder_{UUID}_##\\{unifiedFolderId}\\{materialId}_{fileName}
     */
    private String generateUnifiedFolderPath(String materialId, String originalUrl, String unifiedFolderId) {
        try {
            // 从URL中提取文件名
            String baseFileName = extractFileNameFromUrl(originalUrl);

            // 生成带素材ID前缀的文件名，确保唯一性
            String uniqueFileName = materialId + "_" + baseFileName;

            // 生成Electron期望的统一文件夹路径格式（使用固定的placeholder）
            String draftPlaceholder = "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##";
            return draftPlaceholder + "\\" + unifiedFolderId + "\\" + uniqueFileName;

        } catch (Exception e) {
            log.warn("生成统一文件夹路径失败，使用默认格式: {}", e.getMessage());
            // 如果提取失败，使用默认文件名
            String draftPlaceholder = "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##";
            return draftPlaceholder + "\\" + unifiedFolderId + "\\" + materialId + "_video.mp4";
        }
    }

    /**
     * 从URL中提取文件名（与稳定版完全一致）
     */
    private String extractFileNameFromUrl(String url) {
        try {
            if (url == null || url.trim().isEmpty()) {
                return "video.mp4";
            }

            // 移除查询参数
            String cleanUrl = url.split("\\?")[0];

            // 提取文件名
            String fileName = cleanUrl.substring(cleanUrl.lastIndexOf('/') + 1);

            // 如果没有扩展名，添加默认扩展名
            if (!fileName.contains(".")) {
                fileName += ".mp4";
            }

            return fileName;
        } catch (Exception e) {
            log.warn("从URL提取文件名失败: {}", e.getMessage());
            return "video.mp4";
        }
    }

    /**
     * 蒙版类型映射表（完全复制自稳定版MASK_TYPE_MAP）
     */
    private static class MaskInfo {
        final String resourceId;
        final String resourceType;

        MaskInfo(String resourceId, String resourceType) {
            this.resourceId = resourceId;
            this.resourceType = resourceType;
        }
    }

    private static final java.util.Map<String, MaskInfo> MASK_TYPE_MAP = new java.util.HashMap<>();
    static {
        // 使用竞争对手成功的resource_id（从草稿文件对比中获得的正确ID）
        MASK_TYPE_MAP.put("圆形", new MaskInfo("6791700663249146381", "circle"));
        // 其他类型暂时使用推测的ID，如果需要可以通过测试验证
        MASK_TYPE_MAP.put("矩形", new MaskInfo("6791700663249146382", "rectangle"));
        MASK_TYPE_MAP.put("线性", new MaskInfo("6791700663249146383", "linear"));
        MASK_TYPE_MAP.put("镜面", new MaskInfo("6791700663249146384", "mirror"));
        MASK_TYPE_MAP.put("爱心", new MaskInfo("6791700663249146385", "heart"));
        MASK_TYPE_MAP.put("星形", new MaskInfo("6791700663249146386", "star"));
    }

    /**
     * 创建蒙版材料对象（完全复制自稳定版createMaskMaterial逻辑）
     */
    private String createMaskMaterialInternal(JSONObject draft, String maskName, int canvasWidth, int canvasHeight) {
        try {
            // 生成蒙版ID
            String maskId = java.util.UUID.randomUUID().toString();

            // 使用新的蒙版搜索服务获取蒙版类型信息
            org.jeecg.modules.jianying.service.JianyingMaskSearchService.MaskInfo maskInfo = jianyingMaskSearchService.findMaskByName(maskName);
            if (maskInfo == null) {
                log.warn("蒙版搜索服务未找到蒙版类型: {}，使用固定配置降级", maskName);
                // 使用固定配置作为降级
                MaskInfo fallbackMask = MASK_TYPE_MAP.get(maskName);
                if (fallbackMask == null) {
                    log.warn("Pro版不支持的蒙版类型: {}", maskName);
                    return null;
                }
                // 转换为新的MaskInfo格式
                maskInfo = new org.jeecg.modules.jianying.service.JianyingMaskSearchService.MaskInfo(
                    fallbackMask.resourceId,
                    "",
                    maskName,
                    fallbackMask.resourceType,
                    maskName
                );
            }

            // 创建蒙版配置（使用默认值，与稳定版一致）
            JSONObject config = new JSONObject(new java.util.LinkedHashMap<>());
            config.put("aspectRatio", 1);
            config.put("centerX", 0.0); // 默认居中
            config.put("centerY", 0.0); // 默认居中
            config.put("feather", 0.0); // 默认无羽化
            config.put("height", 1.0); // 默认高度
            config.put("invert", false); // 默认不反转
            config.put("rotation", 0); // 默认无旋转
            config.put("roundCorner", 0.0); // 默认无圆角
            config.put("width", 1.0); // 默认宽度

            // 创建蒙版对象（完全复制自稳定版）
            JSONObject mask = new JSONObject(new java.util.LinkedHashMap<>());
            mask.put("config", config);
            mask.put("id", maskId);
            mask.put("name", maskName);
            mask.put("path", "");
            mask.put("platform", "all");
            mask.put("position_info", "");
            mask.put("resource_id", maskInfo.getResourceId());
            mask.put("resource_type", maskInfo.getResourceType());
            mask.put("type", "mask");

            // 添加到materials.masks数组
            JSONObject materials = draft.getJSONObject("materials");
            if (materials == null) {
                materials = new JSONObject();
                draft.put("materials", materials);
            }

            com.alibaba.fastjson.JSONArray masksArray = materials.getJSONArray("masks");
            if (masksArray == null) {
                masksArray = new com.alibaba.fastjson.JSONArray();
                materials.put("masks", masksArray);
            }
            masksArray.add(mask);

            log.info("Pro版蒙版材料创建成功: maskId={}, name={}, resource_id={}",
                    maskId, maskName, maskInfo.getResourceId());
            return maskId;

        } catch (Exception e) {
            log.error("Pro版创建蒙版材料失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 提取或创建统一文件夹ID（与稳定版完全一致）
     */
    private String extractOrCreateUnifiedFolderId(JSONObject draft) {
        try {
            // 尝试从现有materials中提取统一文件夹ID
            JSONObject materials = draft.getJSONObject("materials");
            if (materials != null) {
                // 检查videos数组中是否有现有的统一文件夹ID
                com.alibaba.fastjson.JSONArray videos = materials.getJSONArray("videos");
                if (videos != null && videos.size() > 0) {
                    for (int i = 0; i < videos.size(); i++) {
                        JSONObject video = videos.getJSONObject(i);
                        String path = video.getString("path");
                        if (path != null && path.contains("##_draftpath_placeholder_")) {
                            // 从路径中提取统一文件夹ID
                            String[] parts = path.split("\\\\");
                            if (parts.length >= 2) {
                                return parts[1]; // 统一文件夹ID
                            }
                        }
                    }
                }
            }

            // 如果没有找到现有的统一文件夹ID，创建新的
            String newUnifiedFolderId = java.util.UUID.randomUUID().toString().toUpperCase();
            log.info("创建新的统一文件夹ID: {}", newUnifiedFolderId);
            return newUnifiedFolderId;

        } catch (Exception e) {
            log.warn("提取统一文件夹ID失败，创建新的: {}", e.getMessage());
            return java.util.UUID.randomUUID().toString().toUpperCase();
        }
    }

    /**
     * 创建视频片段对象（完全复制自稳定版createVideoSegmentObject逻辑，支持转场效果和mask材料创建）
     */
    private JSONObject createVideoSegmentObject(String segmentId, String videoId, long startTime, long endTime, long duration, JSONObject videoInfo, JianyingProAddVideosRequest request, int index, int canvasWidth, int canvasHeight, String transitionId, JSONObject draft) {
        JSONObject segment = new JSONObject(new java.util.LinkedHashMap<>());

        // 获取视频信息中的参数（保留所有输入参数）
        double volume = videoInfo.getDoubleValue("volume") > 0 ? videoInfo.getDoubleValue("volume") : 1.0;
        String mask = videoInfo.getString("mask"); // 遮罩参数

        // 获取视频属性参数（优先使用videoInfo中的参数，这些是用户真正输入的参数）
        Double alpha = videoInfo.getDouble("alpha") != null ? videoInfo.getDouble("alpha") :
                      (request.getAlpha() != null ? request.getAlpha() : 1.0);
        Double scaleX = videoInfo.getDouble("scale_x") != null ? videoInfo.getDouble("scale_x") :
                       (request.getScaleX() != null ? request.getScaleX() : 1.0);
        Double scaleY = videoInfo.getDouble("scale_y") != null ? videoInfo.getDouble("scale_y") :
                       (request.getScaleY() != null ? request.getScaleY() : 1.0);
        Double transformX = videoInfo.getDouble("transform_x") != null ? videoInfo.getDouble("transform_x") :
                           (request.getTransformX() != null ? request.getTransformX() : 0.0);
        Double transformY = videoInfo.getDouble("transform_y") != null ? videoInfo.getDouble("transform_y") :
                           (request.getTransformY() != null ? request.getTransformY() : 0.0);

        // 基础字段（完全复制自稳定版）
        segment.put("id", segmentId);
        segment.put("material_id", videoId);
        segment.put("render_index", 4000000 + index); // 使用索引确保唯一性
        segment.put("reverse", false);
        segment.put("speed", 1.0);
        segment.put("visible", true);
        segment.put("volume", volume); // 使用输入的音量参数

        // 时间范围（完全复制自稳定版）
        segment.put("source_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", 0L);
        }});
        segment.put("target_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", startTime);
        }});

        // 变换属性（使用videoInfo中的参数，这些是用户真正输入的参数）- 完全复制自稳定版
        segment.put("clip", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("alpha", alpha);
            put("flip", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("horizontal", false);
                put("vertical", false);
            }});
            put("rotation", 0);
            put("scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", scaleX);
                put("y", scaleY);
            }});
            put("transform", new JSONObject(new java.util.LinkedHashMap<>()) {{
                // 转换用户输入的像素坐标为剪映归一化坐标（与稳定版完全一致的绝对坐标系统）
                final double normalizedX = transformX / (double)canvasWidth;  // 正确转换：像素值/画布尺寸
                final double normalizedY = transformY / (double)canvasHeight; // 正确转换：像素值/画布尺寸

                log.info("Pro版视频坐标转换[{}]: 用户输入({}, {})像素 -> 剪映归一化({}, {}), 画布尺寸: {}x{}",
                        index, transformX, transformY, normalizedX, normalizedY, canvasWidth, canvasHeight);

                put("x", normalizedX);
                put("y", normalizedY);
            }});
        }});

        // 转场效果和遮罩通过extra_material_refs处理（完全复制自稳定版逻辑）
        com.alibaba.fastjson.JSONArray extraMaterialRefs = new com.alibaba.fastjson.JSONArray();

        // 添加转场效果
        if (transitionId != null && !transitionId.trim().isEmpty()) {
            extraMaterialRefs.add(transitionId);
            log.info("Pro版视频片段[{}]添加转场效果: transitionId={}", index, transitionId);
        }

        // 添加遮罩效果 - 完全重写，创建mask材料并通过extra_material_refs引用
        if (mask != null && !mask.trim().isEmpty()) {
            String maskId = createMaskMaterialInternal(draft, mask.trim(), canvasWidth, canvasHeight);
            if (maskId != null) {
                extraMaterialRefs.add(maskId);
                log.info("Pro版视频片段[{}]应用遮罩: {} -> maskId={}", index, mask, maskId);
            }
        }

        // 其他标准字段（完全复制自稳定版）
        segment.put("caption_info", null);
        segment.put("cartoon", false);
        segment.put("common_keyframes", new com.alibaba.fastjson.JSONArray());
        segment.put("enable_adjust", true);
        segment.put("enable_color_correct_adjust", false);
        segment.put("enable_color_curves", true);
        segment.put("enable_color_match_adjust", false);
        segment.put("enable_color_wheels", true);
        segment.put("enable_lut", true);
        segment.put("enable_smart_color_adjust", false);

        // 添加转场材料引用（与稳定版一致）
        segment.put("extra_material_refs", extraMaterialRefs);
        segment.put("group_id", "");

        // 等比缩放控制：根据scale_x和scale_y是否一致自动决定（完全复制自稳定版）
        final boolean isUniformScale = Math.abs(scaleX - scaleY) < 0.001; // 允许微小误差

        segment.put("uniform_scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("on", isUniformScale);
            put("value", isUniformScale ? scaleX : 1.0); // 等比缩放时使用scale值，否则使用1.0
        }});

        log.info("Pro版等比缩放设置[{}]: scaleX={}, scaleY={}, uniform_scale.on={}, uniform_scale.value={}",
                index, scaleX, scaleY, isUniformScale, isUniformScale ? scaleX : 1.0);

        // HDR设置（完全复制自稳定版）
        segment.put("hdr_settings", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("intensity", 1);
            put("mode", 1);
            put("nits", 1000);
        }});

        segment.put("intensifies_audio", false);
        segment.put("is_placeholder", false);
        segment.put("is_tone_modify", false);
        segment.put("keyframe_refs", new com.alibaba.fastjson.JSONArray());
        segment.put("last_nonzero_volume", 1);

        // 响应式布局（完全复制自稳定版）
        segment.put("responsive_layout", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("enable", false);
            put("horizontal_pos_layout", 0);
            put("size_layout", 0);
            put("target_follow", "");
            put("vertical_pos_layout", 0);
        }});

        segment.put("template_id", "");
        segment.put("template_scene", "default");
        segment.put("track_attribute", 0);
        segment.put("track_render_index", 3); // 正确的轨道渲染索引（复制自稳定版）

        return segment;
    }

    /**
     * Pro版内部生成图片信息（复制自稳定版imgs_infos的完整处理逻辑）
     * 实现真正的代码隔离，不依赖稳定版Service
     */
    private String generateImageInfosInternal(JianyingProAddImagesRequest request) {
        try {
            log.info("Pro版开始生成图片信息，图片数量: {}", request.getImgs().size());

            // 参数处理（复制自稳定版逻辑）
            java.util.List<String> imgs = request.getImgs();
            java.util.List<JSONObject> timelines = request.getTimelines();
            Integer height = request.getHeight() != null ? request.getHeight() : 1080;
            Integer width = request.getWidth() != null ? request.getWidth() : 1920;
            String transition = request.getTransition();
            Integer transitionDuration = request.getTransitionDuration();
            String inAnimation = request.getInAnimation();
            Integer inAnimationDuration = request.getInAnimationDuration();
            String outAnimation = request.getOutAnimation();
            Integer outAnimationDuration = request.getOutAnimationDuration();
            String groupAnimation = request.getGroupAnimation();
            Integer groupAnimationDuration = request.getGroupAnimationDuration();

            // 参数验证（复制自稳定版逻辑）
            if (imgs == null || imgs.isEmpty()) {
                throw new RuntimeException("图片列表不能为空");
            }
            if (timelines == null || timelines.isEmpty()) {
                throw new RuntimeException("时间线不能为空");
            }

            // 生成图片信息JSON字符串（复制自稳定版的完整逻辑）
            StringBuilder jsonBuilder = new StringBuilder("[");
            for (int i = 0; i < timelines.size(); i++) {
                JSONObject timeline = timelines.get(i);

                if (i > 0) jsonBuilder.append(",");

                // 图片分配逻辑：按索引分配，超出范围则为空字符串
                String imgUrl = (i < imgs.size()) ? imgs.get(i) : "";

                // 不需要duration字段，与稳定版保持一致

                // 构建图片信息对象（完全复制稳定版的字段顺序和逻辑，不包含duration字段）
                jsonBuilder.append("{\"image_url\":\"").append(imgUrl).append("\"")
                           .append(",\"width\":").append(width)
                           .append(",\"height\":").append(height)
                           .append(",\"start\":").append(timeline.getLong("start"))
                           .append(",\"end\":").append(timeline.getLong("end"));

                // 可选参数（复制自稳定版逻辑）
                if (transition != null) {
                    jsonBuilder.append(",\"transition\":\"").append(transition).append("\"");
                }
                if (transitionDuration != null) {
                    jsonBuilder.append(",\"transition_duration\":").append(transitionDuration);
                }
                if (inAnimation != null) {
                    jsonBuilder.append(",\"in_animation\":\"").append(inAnimation).append("\"");
                }
                if (inAnimationDuration != null) {
                    jsonBuilder.append(",\"in_animation_duration\":").append(inAnimationDuration);
                }
                if (outAnimation != null) {
                    jsonBuilder.append(",\"out_animation\":\"").append(outAnimation).append("\"");
                }
                if (outAnimationDuration != null) {
                    jsonBuilder.append(",\"out_animation_duration\":").append(outAnimationDuration);
                }
                if (groupAnimation != null) {
                    jsonBuilder.append(",\"group_animation\":\"").append(groupAnimation).append("\"");
                }
                if (groupAnimationDuration != null) {
                    jsonBuilder.append(",\"group_animation_duration\":").append(groupAnimationDuration);
                }

                jsonBuilder.append("}");
            }
            jsonBuilder.append("]");

            String result = jsonBuilder.toString();
            log.info("Pro版图片信息生成成功，生成了{}个图片对象", timelines.size());
            log.debug("生成的图片信息: {}", result);

            return result;

        } catch (Exception e) {
            log.error("Pro版生成图片信息失败", e);
            return null;
        }
    }

    /**
     * Pro版内部添加图片（复制自稳定版add_images的完整处理逻辑）
     * 实现真正的代码隔离，不依赖稳定版Service
     */
    private JSONObject addImagesInternal(String draftUrl, String imageInfosJson, JianyingProAddImagesRequest request) {
        try {
            log.info("Pro版开始添加图片到草稿，图片数量: {}", request.getImgs().size());

            // 第1步：下载并解析草稿文件（复制自稳定版逻辑）
            JSONObject draft = downloadAndParseDraft(draftUrl);
            if (draft == null) {
                return createErrorResult("草稿文件下载失败", "PRO_DRAFT_DOWNLOAD_ERROR");
            }

            // 第2步：解析图片信息数组（复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray imageInfos;
            try {
                imageInfos = com.alibaba.fastjson.JSONArray.parseArray(imageInfosJson);
                if (imageInfos == null || imageInfos.isEmpty()) {
                    return createErrorResult("图片信息解析失败或为空", "PRO_IMAGE_INFOS_PARSE_ERROR");
                }
            } catch (Exception e) {
                return createErrorResult("图片信息JSON格式错误: " + e.getMessage(), "PRO_IMAGE_INFOS_FORMAT_ERROR");
            }

            // 第3步：处理图片添加逻辑（复制自稳定版逻辑）
            JSONObject processResult = processImageAddition(draft, imageInfos, draftUrl, request);
            if (!processResult.getBoolean("success")) {
                return processResult;
            }

            // 第4步：保存更新后的草稿文件（复制自稳定版逻辑）
            boolean saveSuccess = saveDraftFile(draftUrl, draft);
            if (!saveSuccess) {
                return createErrorResult("草稿文件保存失败", "PRO_DRAFT_SAVE_ERROR");
            }

            // 第5步：生成返回结果（复制自稳定版逻辑）
            JSONObject result = generateAddImagesResponse(processResult, draftUrl, imageInfos.size());

            log.info("Pro版图片添加完成，成功添加{}个图片", imageInfos.size());
            return result;

        } catch (Exception e) {
            log.error("Pro版添加图片失败", e);
            return createErrorResult("Pro版添加图片失败: " + e.getMessage(), "PRO_ADD_IMAGES_ERROR");
        }
    }

    /**
     * Pro版内部生成音频信息（复制自稳定版audio_infos的完整处理逻辑）
     * 实现真正的代码隔离，不依赖稳定版Service
     */
    private String generateAudioInfosInternal(JianyingProAddAudiosRequest request) {
        try {
            log.info("Pro版开始生成音频信息，音频数量: {}", request.getMp3Urls().size());

            // 参数处理（复制自稳定版逻辑）
            java.util.List<String> mp3Urls = request.getMp3Urls();
            java.util.List<JSONObject> timelines = request.getTimelines();
            String audioEffect = request.getAudioEffect();
            Double volume = request.getVolume() != null ? request.getVolume() : 1.0; // 音量可以默认1

            // 参数验证（复制自稳定版逻辑）
            if (mp3Urls == null || mp3Urls.isEmpty()) {
                throw new RuntimeException("音频列表不能为空");
            }
            if (timelines == null || timelines.isEmpty()) {
                throw new RuntimeException("时间线不能为空");
            }

            // 生成音频信息JSON字符串（复制自稳定版的完整逻辑）
            StringBuilder jsonBuilder = new StringBuilder("[");
            for (int i = 0; i < timelines.size(); i++) {
                JSONObject timeline = timelines.get(i);

                if (i > 0) jsonBuilder.append(",");

                // 音频分配逻辑：按索引分配，超出范围则为空字符串
                String audioUrl = (i < mp3Urls.size()) ? mp3Urls.get(i) : "";

                // 计算duration
                long duration = timeline.getLong("end") - timeline.getLong("start");

                // 构建音频信息对象（完全复制稳定版的字段顺序和逻辑）
                jsonBuilder.append("{\"audio_url\":\"").append(audioUrl).append("\"")
                           .append(",\"duration\":").append(duration)
                           .append(",\"start\":").append(timeline.getLong("start"))
                           .append(",\"end\":").append(timeline.getLong("end"))
                           .append(",\"volume\":").append(volume); // 音量可以默认1

                // 可选参数（不设置默认值）
                if (audioEffect != null && !audioEffect.trim().isEmpty()) {
                    jsonBuilder.append(",\"audio_effect\":\"").append(audioEffect).append("\"");
                }

                jsonBuilder.append("}");
            }
            jsonBuilder.append("]");

            String result = jsonBuilder.toString();
            log.info("Pro版音频信息生成成功，生成了{}个音频对象", timelines.size());
            log.debug("生成的音频信息: {}", result);

            return result;

        } catch (Exception e) {
            log.error("Pro版生成音频信息失败", e);
            return null;
        }
    }

    /**
     * Pro版内部添加音频（复制自稳定版add_audios的完整处理逻辑）
     * 实现真正的代码隔离，不依赖稳定版Service
     */
    private JSONObject addAudiosInternal(String draftUrl, String audioInfosJson, JianyingProAddAudiosRequest request) {
        try {
            log.info("Pro版开始添加音频到草稿，音频数量: {}", request.getMp3Urls().size());

            // 第1步：下载并解析草稿文件（复制自稳定版逻辑）
            JSONObject draft = downloadAndParseDraft(draftUrl);
            if (draft == null) {
                return createErrorResult("草稿文件下载失败", "PRO_DRAFT_DOWNLOAD_ERROR");
            }

            // 第2步：解析音频信息数组（复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray audioInfos;
            try {
                audioInfos = com.alibaba.fastjson.JSONArray.parseArray(audioInfosJson);
                if (audioInfos == null || audioInfos.isEmpty()) {
                    return createErrorResult("音频信息解析失败或为空", "PRO_AUDIO_INFOS_PARSE_ERROR");
                }
            } catch (Exception e) {
                return createErrorResult("音频信息JSON格式错误: " + e.getMessage(), "PRO_AUDIO_INFOS_FORMAT_ERROR");
            }

            // 第3步：处理音频添加逻辑（复制自稳定版逻辑）
            JSONObject processResult = processAudioAddition(draft, audioInfos, draftUrl);
            if (!processResult.getBoolean("success")) {
                return processResult;
            }

            // 第4步：保存更新后的草稿文件（复制自稳定版逻辑）
            boolean saveSuccess = saveDraftFile(draftUrl, draft);
            if (!saveSuccess) {
                return createErrorResult("草稿文件保存失败", "PRO_DRAFT_SAVE_ERROR");
            }

            // 第5步：生成返回结果（复制自稳定版逻辑）
            JSONObject result = generateAddAudiosResponse(processResult, draftUrl, audioInfos.size());

            log.info("Pro版音频添加完成，成功添加{}个音频", audioInfos.size());
            return result;

        } catch (Exception e) {
            log.error("Pro版添加音频失败", e);
            return createErrorResult("Pro版添加音频失败: " + e.getMessage(), "PRO_ADD_AUDIOS_ERROR");
        }
    }

    // ========== Pro版图片添加辅助方法（复制自稳定版完整逻辑） ==========

    /**
     * 下载并解析草稿文件（复制自稳定版逻辑）
     */
    private JSONObject downloadAndParseDraft(String draftUrl) {
        try {
            log.info("Pro版开始下载草稿文件: {}", draftUrl);

            // 直接调用Pro版CozeApiService的downloadAndParseDraft方法
            // 复制自稳定版的完整逻辑，包括TOS SDK下载、HTTP下载等
            JSONObject draftJson = cozeApiService.downloadAndParseDraft(draftUrl);

            if (draftJson == null) {
                log.error("Pro版下载草稿文件返回null: {}", draftUrl);
                return null;
            }

            log.info("Pro版草稿文件下载成功，ID: {}", draftJson.getString("id"));
            return draftJson;

        } catch (Exception e) {
            log.error("Pro版下载草稿文件失败: {}, 错误: {}", draftUrl, e.getMessage());
            return null;
        }
    }

    /**
     * 创建错误结果（复制自稳定版逻辑）
     */
    private JSONObject createErrorResult(String message, String errorCode) {
        JSONObject errorResult = new JSONObject();
        errorResult.put("success", false);
        errorResult.put("error", message);
        errorResult.put("error_code", errorCode);
        return errorResult;
    }

    /**
     * 处理图片添加逻辑（复制自稳定版逻辑）
     */
    private JSONObject processImageAddition(JSONObject draft, com.alibaba.fastjson.JSONArray imageInfos, String draftUrl, JianyingProAddImagesRequest request) {
        try {
            log.info("Pro版处理图片添加逻辑，图片数量: {}", imageInfos.size());

            // 第1步：确保materials对象存在且有正确的结构（复制自稳定版逻辑）
            JSONObject materials = draft.getJSONObject("materials");
            if (materials == null) {
                materials = new JSONObject(new java.util.LinkedHashMap<>());
                draft.put("materials", materials);
            }

            // 确保materials.videos数组存在（图片也放在videos数组中，与稳定版完全一致）
            com.alibaba.fastjson.JSONArray videos = materials.getJSONArray("videos");
            if (videos == null) {
                videos = new com.alibaba.fastjson.JSONArray();
                materials.put("videos", videos);
            }

            // 第2步：确保tracks数组存在（复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray tracks = draft.getJSONArray("tracks");
            if (tracks == null) {
                tracks = new com.alibaba.fastjson.JSONArray();
                draft.put("tracks", tracks);
            }

            // 第3步：创建新的video轨道（完全复制自稳定版逻辑）
            String newTrackId = java.util.UUID.randomUUID().toString();
            JSONObject newTrack = createNewVideoTrackInternal(newTrackId);
            tracks.add(newTrack);

            // 获取画布尺寸作为坐标转换基准（完全复制自稳定版逻辑）
            JSONObject canvasConfig = draft.getJSONObject("canvas_config");
            int canvasWidth = canvasConfig != null ? canvasConfig.getIntValue("width") : 1920;
            int canvasHeight = canvasConfig != null ? canvasConfig.getIntValue("height") : 1080;

            // 确保画布尺寸有效
            if (canvasWidth <= 0 || canvasHeight <= 0) {
                log.warn("Pro版画布尺寸无效: {}x{}, 使用默认值1920x1080", canvasWidth, canvasHeight);
                canvasWidth = 1920;
                canvasHeight = 1080;
            }

            log.info("Pro版坐标转换基准 - 画布尺寸: {}x{}", canvasWidth, canvasHeight);

            // 先添加转场材料，获取转场ID列表（修复：使用request级别的转场参数，与add_videos_pro一致）
            java.util.List<String> transitionIds = addTransitionMaterialsInternal(draft, imageInfos, request.getTransition(), request.getTransitionDuration());

            // 性能优化：批量预加载所有需要的动画ID（完全复制自稳定版逻辑）
            java.util.Map<String, org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo> animationCache = preloadAnimationIdsInternal(imageInfos);
            log.info("Pro版动画ID预加载完成，缓存数量: {}", animationCache.size());

            // 第4步：处理每个图片，添加材料和片段（使用完整版本逻辑）
            com.alibaba.fastjson.JSONArray imageIds = new com.alibaba.fastjson.JSONArray();
            com.alibaba.fastjson.JSONArray segmentIds = new com.alibaba.fastjson.JSONArray();
            com.alibaba.fastjson.JSONArray segmentTimes = new com.alibaba.fastjson.JSONArray(); // 收集实际时间信息
            java.util.List<Integer> imageWidths = new java.util.ArrayList<>(); // 收集图片宽度信息
            java.util.List<Integer> imageHeights = new java.util.ArrayList<>(); // 收集图片高度信息

            for (int i = 0; i < imageInfos.size(); i++) {
                JSONObject imageInfo = imageInfos.getJSONObject(i);
                try {
                    // 添加图片材料到materials.videos（与稳定版完全一致）
                    String imageId = addImageMaterialInternal(draft, imageInfo, i);
                    imageIds.add(imageId);

                    // 创建动画材料（使用预加载的缓存）
                    String animationId = addAnimationMaterialInternal(draft, imageInfo, i, animationCache);

                    // 获取对应的转场ID（最后一个图片没有转场）
                    String transitionId = (i < transitionIds.size()) ? transitionIds.get(i) : null;

                    // 创建片段（使用完整版本，传递画布尺寸用于坐标转换）
                    // 传递request参数以获取用户自定义的缩放和位置参数
                    String segmentId = addImageSegmentInternalWithParams(newTrack, imageInfo, imageId, animationId, request, i, transitionId, canvasWidth, canvasHeight);
                    segmentIds.add(segmentId);

                    // 保存时间信息用于生成segment_infos（使用实际时间）
                    JSONObject timeInfo = new JSONObject();
                    long imageStart = imageInfo.getLong("start") != null ? imageInfo.getLong("start") : i * 5000000L;
                    long imageEnd = imageInfo.getLong("end") != null ? imageInfo.getLong("end") : (i + 1) * 5000000L;
                    timeInfo.put("start", imageStart);
                    timeInfo.put("end", imageEnd);
                    segmentTimes.add(timeInfo);

                    // 保存图片尺寸信息用于生成segment_infos
                    final int imageWidth = imageInfo.getIntValue("width") > 0 ?
                                          imageInfo.getIntValue("width") : 1920;
                    final int imageHeight = imageInfo.getIntValue("height") > 0 ?
                                           imageInfo.getIntValue("height") : 1080;
                    imageWidths.add(imageWidth);
                    imageHeights.add(imageHeight);

                    log.info("Pro版图片处理成功[{}]: materialId={}, segmentId={}, animationId={}, transitionId={}, 时间范围: {}-{}",
                            i, imageId, segmentId, animationId, transitionId, imageStart, imageEnd);

                } catch (Exception e) {
                    log.error("Pro版处理图片失败[{}]: {}", i, imageInfo.getString("image_url"), e);
                    // 创建占位符ID保持数组长度一致
                    String placeholderMaterialId = "placeholder_material_" + i;
                    String placeholderSegmentId = "placeholder_segment_" + i;
                    imageIds.add(placeholderMaterialId);
                    segmentIds.add(placeholderSegmentId);

                    // 为占位符创建默认时间信息
                    JSONObject placeholderTime = new JSONObject();
                    placeholderTime.put("start", i * 5000000L);
                    placeholderTime.put("end", (i + 1) * 5000000L);
                    segmentTimes.add(placeholderTime);

                    // 为占位符创建默认尺寸信息
                    final int defaultWidth = imageInfo.getIntValue("width") > 0 ?
                                            imageInfo.getIntValue("width") : 1920;
                    final int defaultHeight = imageInfo.getIntValue("height") > 0 ?
                                             imageInfo.getIntValue("height") : 1080;
                    imageWidths.add(defaultWidth);
                    imageHeights.add(defaultHeight);
                }
            }

            // 第5步：返回处理结果（完全复制自稳定版generateAddImagesResponseWithData格式）
            // 生成segment_infos（使用真实的时间信息和尺寸信息）
            com.alibaba.fastjson.JSONArray segmentInfos = new com.alibaba.fastjson.JSONArray();
            if (segmentIds != null && segmentTimes != null) {
                for (int i = 0; i < segmentIds.size() && i < segmentTimes.size(); i++) {
                    JSONObject segmentInfo = new JSONObject();
                    segmentInfo.put("segment_id", segmentIds.get(i));

                    JSONObject timeInfo = (JSONObject) segmentTimes.get(i);
                    long start = timeInfo.getLong("start");
                    long end = timeInfo.getLong("end");
                    segmentInfo.put("start", start);
                    segmentInfo.put("end", end);
                    segmentInfo.put("duration", end - start);

                    // 添加图片尺寸信息
                    if (imageWidths != null && i < imageWidths.size()) {
                        segmentInfo.put("width", imageWidths.get(i));
                    }
                    if (imageHeights != null && i < imageHeights.size()) {
                        segmentInfo.put("height", imageHeights.get(i));
                    }

                    segmentInfos.add(segmentInfo);
                }
            }

            // 构建返回结果（完全复制自稳定版格式）
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("track_id", newTrackId);
            result.put("draft_url", draftUrl);
            result.put("image_ids", imageIds);
            result.put("segment_ids", segmentIds);
            result.put("segment_infos", segmentInfos);

            log.info("Pro版图片添加逻辑处理完成，轨道ID: {}, 图片数量: {}", newTrackId, imageIds.size());
            return result;

        } catch (Exception e) {
            log.error("Pro版处理图片添加失败: {}", e.getMessage());
            return createErrorResult("图片添加处理失败: " + e.getMessage(), "PRO_IMAGE_PROCESS_ERROR");
        }
    }

    /**
     * 保存草稿文件（复制自稳定版逻辑）
     */
    private boolean saveDraftFile(String draftUrl, JSONObject draft) {
        try {
            log.info("Pro版开始保存草稿文件: {}", draftUrl);

            // 调试：检查要保存的内容
            JSONObject materials = draft.getJSONObject("materials");
            if (materials != null) {
                com.alibaba.fastjson.JSONArray videos = materials.getJSONArray("videos");
                com.alibaba.fastjson.JSONArray audios = materials.getJSONArray("audios");
                com.alibaba.fastjson.JSONArray texts = materials.getJSONArray("texts");
                log.info("Pro版准备保存的materials - videos数量: {}, audios数量: {}, texts数量: {}",
                    videos != null ? videos.size() : 0,
                    audios != null ? audios.size() : 0,
                    texts != null ? texts.size() : 0);
            }

            // 直接调用Pro版CozeApiService的overwriteDraftFile方法
            // 复制自稳定版的完整逻辑，包括TOS上传、JSON序列化等
            cozeApiService.overwriteDraftFile(draftUrl, draft);

            log.info("Pro版草稿文件保存成功: {}", draftUrl);
            return true;

        } catch (Exception e) {
            log.error("Pro版保存草稿文件失败: {}, 错误: {}", draftUrl, e.getMessage());
            return false;
        }
    }

    /**
     * 生成添加图片响应（完全复制自稳定版逻辑，确保返回值一致）
     */
    private JSONObject generateAddImagesResponse(JSONObject processResult, String draftUrl, int imageCount) {
        JSONObject result = new JSONObject();
        result.put("track_id", processResult.getString("track_id"));
        result.put("draft_url", draftUrl);
        result.put("image_ids", processResult.get("image_ids"));
        result.put("segment_ids", processResult.get("segment_ids"));
        result.put("segment_infos", processResult.get("segment_infos"));  // ✅ 添加segment_infos字段
        result.put("message", "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=" + draftUrl);  // ✅ 与稳定版一致的message

        return result;
    }

    // ========== Pro版音频添加辅助方法（复制自稳定版完整逻辑） ==========

    /**
     * 处理音频添加逻辑（复制自稳定版逻辑）
     */
    private JSONObject processAudioAddition(JSONObject draft, com.alibaba.fastjson.JSONArray audioInfos, String draftUrl) {
        try {
            log.info("Pro版处理音频添加逻辑，音频数量: {}", audioInfos.size());

            // 第1步：确保materials对象存在且有正确的结构（复制自稳定版逻辑）
            JSONObject materials = draft.getJSONObject("materials");
            if (materials == null) {
                materials = new JSONObject(new java.util.LinkedHashMap<>());
                draft.put("materials", materials);
            }

            // 确保materials.audios数组存在
            com.alibaba.fastjson.JSONArray audios = materials.getJSONArray("audios");
            if (audios == null) {
                audios = new com.alibaba.fastjson.JSONArray();
                materials.put("audios", audios);
            }

            // 确保materials.audio_effects数组存在（完全复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray audioEffectsArray = materials.getJSONArray("audio_effects");
            if (audioEffectsArray == null) {
                audioEffectsArray = new com.alibaba.fastjson.JSONArray();
                materials.put("audio_effects", audioEffectsArray);
            }

            // 第2步：确保tracks数组存在（复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray tracks = draft.getJSONArray("tracks");
            if (tracks == null) {
                tracks = new com.alibaba.fastjson.JSONArray();
                draft.put("tracks", tracks);
            }

            // 第3步：创建新的音频轨道（复制自稳定版逻辑）
            String newTrackId = java.util.UUID.randomUUID().toString();
            JSONObject newTrack = createAudioTrack(newTrackId);
            tracks.add(newTrack);

            // 第4步：处理每个音频，添加材料和片段（使用完整版本逻辑）
            com.alibaba.fastjson.JSONArray audioIds = new com.alibaba.fastjson.JSONArray();
            com.alibaba.fastjson.JSONArray segmentIds = new com.alibaba.fastjson.JSONArray();
            java.util.List<String> audioEffectIds = new java.util.ArrayList<>();

            for (int i = 0; i < audioInfos.size(); i++) {
                JSONObject audioInfo = audioInfos.getJSONObject(i);
                try {
                    // 1. 先添加音频效果（如果有）
                    String audioEffectId = null;
                    if (audioInfo.containsKey("audio_effect") && audioInfo.getString("audio_effect") != null) {
                        audioEffectId = addAudioEffectStandardInternal(draft, audioInfo.getString("audio_effect"));
                    }
                    audioEffectIds.add(audioEffectId);

                    // 2. 添加音频材料到materials.audios
                    String audioId = addAudioMaterialInternal(draft, audioInfo, i);
                    audioIds.add(audioId);

                    // 3. 添加音频段（关联音效ID）
                    String segmentId = addAudioSegmentInternalWithEffect(newTrack, audioInfo, audioId, audioEffectId, i);
                    segmentIds.add(segmentId);

                    log.info("Pro版音频处理成功[{}]: materialId={}, segmentId={}, audioEffectId={}", i, audioId, segmentId, audioEffectId);

                } catch (Exception e) {
                    log.error("Pro版处理音频失败[{}]: {}", i, audioInfo.getString("audio_url"), e);
                    // 创建占位符ID保持数组长度一致
                    String placeholderMaterialId = "placeholder_audio_material_" + i;
                    String placeholderSegmentId = "placeholder_audio_segment_" + i;
                    audioIds.add(placeholderMaterialId);
                    segmentIds.add(placeholderSegmentId);
                    audioEffectIds.add(null);
                }
            }

            // 第5步：返回处理结果（完全复制自稳定版generateAddAudiosResponseWithWarnings格式）
            JSONObject result = new JSONObject(new java.util.LinkedHashMap<>());
            result.put("success", true); // 添加success字段
            result.put("audio_ids", audioIds);
            result.put("draft_url", draftUrl);
            result.put("segment_ids", segmentIds);
            result.put("track_id", newTrackId);

            // 添加警告信息（如果有）- 暂时为空，后续可扩展
            java.util.List<String> warnings = new java.util.ArrayList<>();
            if (!warnings.isEmpty()) {
                result.put("warnings", warnings);
            }

            log.info("Pro版音频添加逻辑处理完成，轨道ID: {}, 音频数量: {}", newTrackId, audioIds.size());
            return result;

        } catch (Exception e) {
            log.error("Pro版处理音频添加失败: {}", e.getMessage());
            return createErrorResult("音频添加处理失败: " + e.getMessage(), "PRO_AUDIO_PROCESS_ERROR");
        }
    }

    /**
     * 生成添加音频响应（复制自稳定版逻辑）
     */
    private JSONObject generateAddAudiosResponse(JSONObject processResult, String draftUrl, int audioCount) {
        JSONObject result = new JSONObject();
        result.put("success", true);
        result.put("message", "Pro版音频添加成功");
        result.put("data", new JSONObject());
        result.put("draft_url", draftUrl);
        result.put("audio_count", audioCount);
        result.put("track_id", processResult.getString("track_id"));
        result.put("audio_ids", processResult.get("audio_ids"));
        result.put("segment_ids", processResult.get("segment_ids"));

        return result;
    }

    // ========== Pro版图片处理辅助方法（复制自稳定版完整逻辑） ==========

    /**
     * 创建新的video轨道（完全复制自稳定版createNewVideoTrack逻辑）
     */
    private JSONObject createNewVideoTrackInternal(String trackId) {
        JSONObject track = new JSONObject();
        track.put("attribute", 0);
        track.put("flag", 0);
        track.put("id", trackId);
        track.put("segments", new com.alibaba.fastjson.JSONArray());
        track.put("type", "video");
        return track;
    }

    /**
     * 添加图片材料到materials.videos（与稳定版完全一致）
     */
    private String addImageMaterialInternal(JSONObject draft, JSONObject imageInfo, int index) {
        try {
            String imageUrl = imageInfo.getString("image_url");
            log.info("Pro版开始添加图片材料[{}]: {}", index, imageUrl);

            // 生成图片材料ID
            String imageMaterialId = java.util.UUID.randomUUID().toString();

            // 获取统一文件夹ID
            String unifiedFolderId = cozeApiService.extractOrCreateUnifiedFolderId(draft);

            // 处理图片URL（完全复制自稳定版逻辑）
            String originalUrl = "";
            boolean urlValid = false;

            if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                // URL格式验证
                if (isValidImageURLInternal(imageUrl)) {
                    originalUrl = imageUrl;
                    urlValid = true;
                    log.info("Pro版图片URL格式验证通过[{}]: {}", index, imageUrl);

                    // 可选的连通性检查（不阻塞处理）
                    if (!checkImageURLAccessibleInternal(imageUrl)) {
                        log.warn("Pro版图片URL连通性检查失败[{}]: {}", index, imageUrl);
                    }
                } else {
                    // URL格式错误，抛出异常
                    throw new RuntimeException("图片URL格式不正确: " + imageUrl);
                }
            } else {
                log.info("Pro版图片对象[{}]无image_url，创建占位符材料", index);
            }

            // 图片尺寸将在createImageMaterialWithOriginalURLInternal方法中处理

            // 添加到materials.videos（图片也放在videos数组中，与稳定版完全一致）
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray videos = materials.getJSONArray("videos");

            // 创建图片材料对象（完全复制自稳定版逻辑）
            JSONObject photoMaterial = createImageMaterialWithOriginalURLInternal(imageMaterialId, originalUrl, imageInfo, urlValid, unifiedFolderId);
            videos.add(photoMaterial);

            log.info("Pro版图片材料添加成功[{}]: ID={}, URL={}, 有效={}", index, imageMaterialId, originalUrl, urlValid);
            return imageMaterialId;

        } catch (Exception e) {
            log.error("Pro版添加图片材料失败[{}]", index, e);
            throw new RuntimeException("添加图片材料失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加图片片段到轨道（简化版本，用于内部处理）
     */
    private String addImageSegmentInternal(JSONObject track, JSONObject imageInfo, String imageId, int index) {
        try {
            log.debug("Pro版开始添加图片片段[{}]: imageId={}", index, imageId);

            // 生成片段ID
            String segmentId = java.util.UUID.randomUUID().toString();

            // 使用imageInfo中的实际时间范围
            long startTime = imageInfo.getLong("start") != null ? imageInfo.getLong("start") : index * 5000000L;
            long endTime = imageInfo.getLong("end") != null ? imageInfo.getLong("end") : (index + 1) * 5000000L;
            long duration = endTime - startTime;

            // 创建图片片段对象（使用简化版本）
            JSONObject segment = createImageSegmentObject(segmentId, imageId, startTime, endTime, duration, imageInfo, index);

            // 添加到轨道的segments数组
            com.alibaba.fastjson.JSONArray segments = track.getJSONArray("segments");
            if (segments == null) {
                segments = new com.alibaba.fastjson.JSONArray();
                track.put("segments", segments);
            }
            segments.add(segment);

            log.info("Pro版图片片段添加成功[{}]: ID={}, 时间范围={}~{}", index, segmentId, startTime, endTime);
            return segmentId;

        } catch (Exception e) {
            log.error("Pro版添加图片片段失败[{}]", index, e);
            throw new RuntimeException("添加图片片段失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加图片片段到轨道（完全复制自稳定版addImageSegment逻辑，支持参数处理）
     */
    private String addImageSegmentInternalWithParams(JSONObject track, JSONObject imageInfo, String imageId, String animationId,
                                                   JianyingProAddImagesRequest request, int index, String transitionId,
                                                   int canvasWidth, int canvasHeight) {
        try {
            log.debug("Pro版开始添加图片片段[{}]: imageId={}, animationId={}", index, imageId, animationId);

            // 生成片段ID
            String segmentId = java.util.UUID.randomUUID().toString();

            // 使用imageInfo中的实际时间范围
            long startTime = imageInfo.getLong("start") != null ? imageInfo.getLong("start") : index * 5000000L;
            long endTime = imageInfo.getLong("end") != null ? imageInfo.getLong("end") : (index + 1) * 5000000L;
            long duration = endTime - startTime;

            // 转换用户输入的像素坐标为剪映归一化坐标（完全复制自稳定版）
            double inputX = (request != null && request.getTransformX() != null) ? request.getTransformX() : 0.0;
            double inputY = (request != null && request.getTransformY() != null) ? request.getTransformY() : 0.0;
            final double transformX = inputX / (double)canvasWidth;  // 正确转换：像素值/画布尺寸
            final double transformY = inputY / (double)canvasHeight; // 正确转换：像素值/画布尺寸

            log.info("Pro版图片坐标转换[{}]: 用户输入({}, {})像素 -> 剪映归一化({}, {}), 画布尺寸: {}x{}",
                     index, inputX, inputY, transformX, transformY, canvasWidth, canvasHeight);

            // 创建图片片段对象（完全复制自稳定版逻辑）
            JSONObject segment = createImageSegmentObjectWithParams(segmentId, imageId, startTime, endTime, duration,
                                                                   imageInfo, request, index, animationId, transitionId,
                                                                   transformX, transformY);

            // 添加到轨道的segments数组
            com.alibaba.fastjson.JSONArray segments = track.getJSONArray("segments");
            if (segments == null) {
                segments = new com.alibaba.fastjson.JSONArray();
                track.put("segments", segments);
            }
            segments.add(segment);

            log.info("Pro版图片片段添加成功[{}]: ID={}, 时间范围={}~{}", index, segmentId, startTime, endTime);
            return segmentId;

        } catch (Exception e) {
            log.error("Pro版添加图片片段失败[{}]", index, e);
            throw new RuntimeException("添加图片片段失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建图片材料对象（完全复制自稳定版createImageMaterialWithOriginalURL逻辑）
     */
    private JSONObject createImageMaterialWithOriginalURLInternal(String imageMaterialId, String originalUrl,
                                                                 JSONObject imageInfo, boolean urlValid, String unifiedFolderId) {
        // 获取图片尺寸（从imageInfo中获取，如果没有则使用默认值）
        final int imageWidth = imageInfo.getIntValue("width") > 0 ?
                              imageInfo.getIntValue("width") : 1920;
        final int imageHeight = imageInfo.getIntValue("height") > 0 ?
                               imageInfo.getIntValue("height") : 1080;

        // 生成随机的material_name（UUID格式，与稳定版一致）
        String materialName = java.util.UUID.randomUUID().toString();

        JSONObject imageMaterial = new JSONObject(new java.util.LinkedHashMap<>());

        // 基础信息
        imageMaterial.put("id", imageMaterialId);
        imageMaterial.put("material_id", imageMaterialId);
        imageMaterial.put("material_name", materialName);
        imageMaterial.put("material_url", originalUrl); // 直接使用原始URL

        // path字段使用Electron期望的Windows路径格式（统一文件夹模式）
        String electronPath = urlValid ?
            generateImageUnifiedFolderPathInternal(imageMaterialId, originalUrl, unifiedFolderId) :
            "";
        imageMaterial.put("path", electronPath);

        imageMaterial.put("type", "photo");
        imageMaterial.put("source_platform", urlValid ? "external" : "local"); // 标识外部URL

        // 图片属性
        imageMaterial.put("duration", 5000000L); // 5秒固定时长
        imageMaterial.put("width", imageWidth);
        imageMaterial.put("height", imageHeight);
        imageMaterial.put("has_audio", false);

        // 图片特有字段（完全复制自稳定版）
        imageMaterial.put("aigc_type", "none");
        imageMaterial.put("cartoon_path", "");
        imageMaterial.put("category_id", "");
        imageMaterial.put("category_name", "");
        imageMaterial.put("check_flag", 63487);
        imageMaterial.put("create_time", System.currentTimeMillis() * 1000L);
        imageMaterial.put("crop_ratio", "free");
        imageMaterial.put("crop_scale", 1.0);
        imageMaterial.put("extra_type_option", 0);
        imageMaterial.put("file_Path", "");
        imageMaterial.put("import_time", System.currentTimeMillis() * 1000L);
        imageMaterial.put("import_time_ms", System.currentTimeMillis());
        imageMaterial.put("intensifies_audio_path", "");
        imageMaterial.put("intensifies_path", "");
        imageMaterial.put("is_ai_generate_content", false);
        imageMaterial.put("is_unified_beauty_mode", false);
        imageMaterial.put("local_id", "");
        imageMaterial.put("local_material_id", "");
        imageMaterial.put("material_type", "photo");
        imageMaterial.put("media_path", "");
        imageMaterial.put("metetype", "");
        imageMaterial.put("object_locked", false);
        imageMaterial.put("picture_from", "none");
        imageMaterial.put("picture_set_category_id", "");
        imageMaterial.put("picture_set_category_name", "");
        imageMaterial.put("request_id", "");
        imageMaterial.put("reverse_intensifies_path", "");
        imageMaterial.put("reverse_path", "");
        imageMaterial.put("source", 0);
        imageMaterial.put("stable", new JSONObject() {{
            put("matrix_path", "");
            put("stable_level", 0);
        }});
        imageMaterial.put("team_id", "");
        imageMaterial.put("video_algorithm", new JSONObject() {{
            put("algorithms", new com.alibaba.fastjson.JSONArray());
            put("deflicker", new JSONObject());
            put("motion_blur_config", new JSONObject());
            put("noise_reduction", new JSONObject());
            put("path", "");
            put("time_range", new JSONObject());
        }});

        // 下载相关字段
        imageMaterial.put("download_url", originalUrl);  // 使用原始URL
        imageMaterial.put("original_url", originalUrl);  // 保留原始URL引用

        return imageMaterial;
    }

    /**
     * 创建图片片段对象（支持参数处理，完全复制自稳定版addImageSegment逻辑）
     */
    private JSONObject createImageSegmentObjectWithParams(String segmentId, String imageId, long startTime, long endTime,
                                                         long duration, JSONObject imageInfo, JianyingProAddImagesRequest request,
                                                         int index, String animationId, String transitionId,
                                                         double transformX, double transformY) {
        JSONObject segment = new JSONObject(new java.util.LinkedHashMap<>());

        // 基础字段（完全复制自稳定版）
        segment.put("cartoon", false);
        segment.put("clip", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("alpha", (request != null && request.getAlpha() != null) ? request.getAlpha() : 1.0);
            put("flip", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("horizontal", false);
                put("vertical", false);
            }});
            put("rotation", 0.0);
            put("scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", (request != null && request.getScaleX() != null) ? request.getScaleX() : 1.0);
                put("y", (request != null && request.getScaleY() != null) ? request.getScaleY() : 1.0);
            }});
            put("transform", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", transformX);
                put("y", transformY);
            }});
        }});
        segment.put("common_keyframes", new com.alibaba.fastjson.JSONArray());
        segment.put("enable_adjust", true);
        segment.put("enable_color_curves", true);
        segment.put("enable_color_match_adjust", false);
        segment.put("enable_color_wheels", true);
        segment.put("enable_lut", true);
        segment.put("enable_smart_color_adjust", false);
        segment.put("extra_material_refs", new com.alibaba.fastjson.JSONArray() {{
            add(animationId); // 关联动画材料
            if (transitionId != null) {
                add(transitionId); // 关联转场材料
            }
        }});
        segment.put("group_id", "");
        segment.put("hdr_settings", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("intensity", 1.0);
            put("mode", 1);
            put("nits", 1000);
        }});
        segment.put("id", segmentId);
        segment.put("intensifies_audio", false);
        segment.put("is_placeholder", false);
        segment.put("is_tone_modify", false);
        segment.put("keyframe_refs", new com.alibaba.fastjson.JSONArray());
        segment.put("last_nonzero_volume", 1.0);
        segment.put("material_id", imageId);
        segment.put("render_index", 4000000 + index);
        segment.put("reverse", false);
        segment.put("source_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", 0L);
        }});
        segment.put("speed", 1.0);
        segment.put("target_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", startTime);
        }});
        segment.put("template_id", "");
        segment.put("template_scene", "default");
        segment.put("track_attribute", 0);
        segment.put("track_render_index", 0);

        // 等比缩放控制：根据scale_x和scale_y是否一致自动决定（完全复制自稳定版）
        final double scaleX = (request != null && request.getScaleX() != null) ? request.getScaleX() : 1.0;
        final double scaleY = (request != null && request.getScaleY() != null) ? request.getScaleY() : 1.0;
        final boolean isUniformScale = Math.abs(scaleX - scaleY) < 0.001; // 允许微小误差

        segment.put("uniform_scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("on", isUniformScale);
            put("value", isUniformScale ? scaleX : 1.0); // 等比缩放时使用scale值，否则使用1.0
        }});

        log.info("Pro版图片等比缩放设置[{}]: scaleX={}, scaleY={}, uniform_scale.on={}, uniform_scale.value={}",
                 index, scaleX, scaleY, isUniformScale, isUniformScale ? scaleX : 1.0);
        segment.put("visible", true);
        segment.put("volume", 1.0);

        return segment;
    }

    /**
     * 验证图片URL格式（宽松模式，支持各种图片链接）
     * 完全复制自稳定版逻辑
     */
    private boolean isValidImageURLInternal(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }

        // 基础格式检查：只要是http/https协议即可
        if (!url.matches("^https?://.*")) {
            return false;
        }

        // 排除明显的非图片URL（如视频、音频、文档等）
        String lowerUrl = url.toLowerCase();

        // 排除视频格式
        if (lowerUrl.matches(".*\\.(mp4|avi|mov|wmv|flv|webm|mkv|m4v)($|\\?.*)")) {
            return false;
        }

        // 排除音频格式
        if (lowerUrl.matches(".*\\.(mp3|wav|flac|aac|ogg|wma)($|\\?.*)")) {
            return false;
        }

        // 排除文档格式
        if (lowerUrl.matches(".*\\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt)($|\\?.*)")) {
            return false;
        }

        // 其他HTTP/HTTPS链接都认为可能是图片（包括coze.cn、imgur、cloudinary等图片服务）
        return true;
    }

    /**
     * 检查图片URL连通性（可选，5秒超时）
     * 完全复制自稳定版逻辑
     */
    private boolean checkImageURLAccessibleInternal(String url) {
        try {
            java.net.HttpURLConnection conn = (java.net.HttpURLConnection) new java.net.URL(url).openConnection();
            conn.setRequestMethod("HEAD");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            conn.setInstanceFollowRedirects(true);

            int responseCode = conn.getResponseCode();
            boolean accessible = responseCode >= 200 && responseCode < 400;

            log.debug("Pro版图片URL连通性检查: {} -> {}", url, accessible ? "可访问" : "不可访问(" + responseCode + ")");
            return accessible;

        } catch (Exception e) {
            log.debug("Pro版图片URL连通性检查失败: {} -> {}", url, e.getMessage());
            return false; // 检查失败不阻塞处理
        }
    }

    /**
     * 生成图片统一文件夹的Windows路径格式（匹配Electron统一文件夹逻辑）
     * 格式：##_draftpath_placeholder_{UUID}_##\\{unifiedFolderId}\\{materialId}_{fileName}
     * 完全复制自稳定版逻辑
     */
    private String generateImageUnifiedFolderPathInternal(String materialId, String originalUrl, String unifiedFolderId) {
        try {
            // 从URL中提取文件名
            String baseFileName = extractImageFileNameFromUrlInternal(originalUrl);

            // 生成带素材ID前缀的文件名，确保唯一性
            String uniqueFileName = materialId + "_" + baseFileName;

            // 生成Electron期望的统一文件夹路径格式（使用固定的placeholder）
            String draftPlaceholder = "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##";
            return draftPlaceholder + "\\" + unifiedFolderId + "\\" + uniqueFileName;

        } catch (Exception e) {
            log.warn("Pro版生成图片统一文件夹路径失败，使用默认格式: {}", e.getMessage());
            // 如果提取失败，使用默认文件名
            String draftPlaceholder = "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##";
            return draftPlaceholder + "\\" + unifiedFolderId + "\\" + materialId + "_image.jpg";
        }
    }

    /**
     * 从图片URL中提取文件名
     * 完全复制自稳定版逻辑
     */
    private String extractImageFileNameFromUrlInternal(String url) {
        try {
            // 移除查询参数
            String cleanUrl = url.split("\\?")[0];

            // 提取最后一部分作为文件名
            String[] parts = cleanUrl.split("/");
            String fileName = parts[parts.length - 1];

            // 如果没有扩展名，添加默认扩展名
            if (!fileName.contains(".")) {
                fileName += ".jpg";
            }

            // 确保文件名不为空
            if (fileName.trim().isEmpty() || fileName.equals(".jpg")) {
                fileName = "image.jpg";
            }

            return fileName;

        } catch (Exception e) {
            log.warn("Pro版提取图片文件名失败: {}", e.getMessage());
            return "image.jpg";
        }
    }

    /**
     * 添加动画材料到materials.material_animations（完全复制自稳定版逻辑）
     */
    private String addAnimationMaterialInternal(JSONObject draft, JSONObject imageInfo, int index,
                                               java.util.Map<String, org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo> animationCache) {
        try {
            log.debug("Pro版开始添加动画材料[{}]", index);

            // 生成动画材料ID
            String animationId = java.util.UUID.randomUUID().toString();

            // 获取动画信息
            String inAnimation = imageInfo.getString("in_animation");
            String outAnimation = imageInfo.getString("out_animation");
            String groupAnimation = imageInfo.getString("group_animation");

            // 获取动画持续时间参数（使用final变量）
            final long inAnimationDuration = imageInfo.getLong("in_animation_duration") != null ?
                imageInfo.getLong("in_animation_duration") : 1000000L;
            final long outAnimationDuration = imageInfo.getLong("out_animation_duration") != null ?
                imageInfo.getLong("out_animation_duration") : 1000000L;
            final long groupAnimationDuration = imageInfo.getLong("group_animation_duration") != null ?
                imageInfo.getLong("group_animation_duration") : 1000000L;

            // 添加到materials.material_animations
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray materialAnimations = materials.getJSONArray("material_animations");
            if (materialAnimations == null) {
                materialAnimations = new com.alibaba.fastjson.JSONArray();
                materials.put("material_animations", materialAnimations);
            }

            JSONObject animationMaterial = new JSONObject();
            animationMaterial.put("id", animationId);
            animationMaterial.put("multi_language_current", "none");  // 关键字段：多语言设置
            animationMaterial.put("type", "sticker_animation");       // 关键字段：动画类型

            // 创建动画数组
            com.alibaba.fastjson.JSONArray animations = new com.alibaba.fastjson.JSONArray();

            // 计算动画时间：根据图片总时长来分配动画时间
            long totalDuration = imageInfo.getLong("end") != null && imageInfo.getLong("start") != null ?
                imageInfo.getLong("end") - imageInfo.getLong("start") : 5000000L;

            // 入场动画：从0开始
            long inStartTime = 0L;

            // 循环动画：入场动画结束后开始
            long loopStartTime = inAnimationDuration;

            // 出场动画：总时长减去出场动画时长
            long outStartTime = totalDuration - outAnimationDuration;

            // 调整循环动画时长，确保不与出场动画重叠
            long adjustedLoopDuration = Math.max(0L, outStartTime - loopStartTime);

            log.debug("Pro版动画时间计算[{}]: 总时长={}ms, 入场={}ms(start={}), 循环={}ms(start={}), 出场={}ms(start={})",
                    index, totalDuration/1000, inAnimationDuration/1000, inStartTime/1000,
                    adjustedLoopDuration/1000, loopStartTime/1000, outAnimationDuration/1000, outStartTime/1000);

            // 入场动画（按索引循环选择，修复随机选择问题）
            if (inAnimation != null && !inAnimation.trim().isEmpty() && !"无".equals(inAnimation)) {
                String selectedInAnimation = selectAnimationByIndexInternal(inAnimation, index);
                if (selectedInAnimation != null) {
                    JSONObject inAnimationObj = createAnimationObjectWithCacheInternal(selectedInAnimation, "in", inAnimationDuration, "入场", inStartTime, animationCache);
                    if (inAnimationObj != null) {
                        animations.add(inAnimationObj);
                    }
                }
            }

            // 出场动画（按索引循环选择，修复随机选择问题）
            if (outAnimation != null && !outAnimation.trim().isEmpty() && !"无".equals(outAnimation)) {
                String selectedOutAnimation = selectAnimationByIndexInternal(outAnimation, index);
                if (selectedOutAnimation != null) {
                    JSONObject outAnimationObj = createAnimationObjectWithCacheInternal(selectedOutAnimation, "out", outAnimationDuration, "出场", outStartTime, animationCache);
                    if (outAnimationObj != null) {
                        animations.add(outAnimationObj);
                    }
                }
            }

            // 循环动画（按索引循环选择，修复随机选择问题）
            if (groupAnimation != null && !groupAnimation.trim().isEmpty() && !"无".equals(groupAnimation) && adjustedLoopDuration > 0) {
                String selectedGroupAnimation = selectAnimationByIndexInternal(groupAnimation, index);
                if (selectedGroupAnimation != null) {
                    // 使用用户指定的组合动画时长，如果没有指定则使用调整后的循环时长
                    long finalGroupDuration = groupAnimationDuration > 0 ? groupAnimationDuration : adjustedLoopDuration;
                    JSONObject groupAnimationObj = createAnimationObjectWithCacheInternal(selectedGroupAnimation, "group", finalGroupDuration, "循环", loopStartTime, animationCache);
                    if (groupAnimationObj != null) {
                        animations.add(groupAnimationObj);
                    }
                }
            }

            animationMaterial.put("animations", animations);
            materialAnimations.add(animationMaterial);

            // 显示实际选中的动画（按索引循环选择，确保一致性）
            String selectedInAnimation = (inAnimation != null && !inAnimation.trim().isEmpty() && !"无".equals(inAnimation)) ?
                selectAnimationByIndexInternal(inAnimation, index) : "无";
            String selectedOutAnimation = (outAnimation != null && !outAnimation.trim().isEmpty() && !"无".equals(outAnimation)) ?
                selectAnimationByIndexInternal(outAnimation, index) : "无";
            String selectedGroupAnimation = (groupAnimation != null && !groupAnimation.trim().isEmpty() && !"无".equals(groupAnimation)) ?
                selectAnimationByIndexInternal(groupAnimation, index) : "无";

            log.info("Pro版动画材料添加成功[{}]: ID={}, 入场={}({}ms), 出场={}({}ms), 循环={}({}ms)",
                    index, animationId,
                    selectedInAnimation, inAnimationDuration/1000,
                    selectedOutAnimation, outAnimationDuration/1000,
                    selectedGroupAnimation, (groupAnimationDuration > 0 ? groupAnimationDuration : adjustedLoopDuration)/1000);
            return animationId;

        } catch (Exception e) {
            log.error("Pro版添加动画材料失败[{}]", index, e);
            return null;
        }
    }

    /**
     * 按索引循环选择动画（修复随机选择导致的不稳定问题）
     */
    private String selectAnimationByIndexInternal(String animationStr, int index) {
        if (animationStr == null || animationStr.trim().isEmpty()) {
            return null;
        }

        // 同时支持英文|和中文｜分隔符
        String[] animations;
        if (animationStr.contains("|")) {
            animations = animationStr.split("\\|");
        } else if (animationStr.contains("｜")) {
            animations = animationStr.split("｜");
        } else {
            // 没有分隔符，直接返回原字符串
            return animationStr.trim();
        }

        // 过滤空字符串
        java.util.List<String> validAnimations = java.util.Arrays.stream(animations)
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(java.util.stream.Collectors.toList());

        if (validAnimations.isEmpty()) {
            return null;
        }

        // 按索引循环选择动画（修复随机选择问题）
        int animationIndex = index % validAnimations.size();
        String selectedAnimation = validAnimations.get(animationIndex);

        log.debug("Pro版按索引选择动画: 原始={}, 候选={}, 图片索引={}, 动画索引={}, 选中={}",
                 animationStr, validAnimations, index, animationIndex, selectedAnimation);
        return selectedAnimation;
    }

    /**
     * 从动画字符串中提取所有动画名称并添加到集合中（支持多个动画，用|分割）
     */
    private void addAnimationNamesFromString(java.util.Set<String> animationNames, String animationStr, String animationType) {
        if (animationStr == null || animationStr.trim().isEmpty()) {
            return;
        }

        // 同时支持英文|和中文｜分隔符
        String[] animations;
        if (animationStr.contains("|")) {
            animations = animationStr.split("\\|");
        } else if (animationStr.contains("｜")) {
            animations = animationStr.split("｜");
        } else {
            // 没有分隔符，直接添加
            animationNames.add(animationStr.trim() + ":" + animationType);
            return;
        }

        // 添加所有有效的动画名称
        for (String animation : animations) {
            String trimmed = animation.trim();
            if (!trimmed.isEmpty()) {
                animationNames.add(trimmed + ":" + animationType);
            }
        }
    }

    /**
     * 添加转场材料到materials.transitions，返回转场ID列表（修复：使用request级别的转场参数，与add_videos_pro一致）
     */
    private java.util.List<String> addTransitionMaterialsInternal(JSONObject draft, com.alibaba.fastjson.JSONArray imageInfos, String transitionName, Integer transitionDuration) {
        try {
            int imageCount = imageInfos.size();
            log.info("Pro版开始添加转场材料 - 图片数量: {}, 转场数量: {}", imageCount, imageCount - 1);

            if (imageCount <= 1) {
                log.info("Pro版图片数量不足，无需添加转场");
                return new java.util.ArrayList<>();
            }

            java.util.List<String> transitionIds = new java.util.ArrayList<>();

            // 添加到materials.transitions
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray transitions = materials.getJSONArray("transitions");
            if (transitions == null) {
                transitions = new com.alibaba.fastjson.JSONArray();
                materials.put("transitions", transitions);
            }

            // 为N个图片创建N-1个转场（修复：使用request级别的转场参数，与add_videos_pro一致）
            for (int i = 0; i < imageCount - 1; i++) {
                // 修复：只有在用户明确指定转场参数时才添加转场效果，匹配add_videos_pro行为
                if (transitionName != null && !transitionName.trim().isEmpty()) {
                    String transitionId = java.util.UUID.randomUUID().toString().toUpperCase();

                    // 使用用户指定的转场时长，如果没有指定则使用默认值（与add_videos_pro一致）
                    Long finalTransitionDuration = (transitionDuration != null && transitionDuration > 0) ?
                        transitionDuration.longValue() : 500000L; // 默认0.5秒

                    // 创建转场对象，集成真实ID获取（复用add_videos_pro的方法）
                    JSONObject transition = createTransitionObjectInternal(transitionId, transitionName, finalTransitionDuration);

                    transitions.add(transition);
                    transitionIds.add(transitionId);
                    log.info("Pro版图片转场材料添加成功[{}]: ID={}, 名称={}, 持续时间={}ms",
                            i, transitionId, transitionName, finalTransitionDuration / 1000);
                } else {
                    // 用户没有指定转场，不添加转场效果（匹配add_videos_pro行为）
                    transitionIds.add(null);
                    log.info("Pro版图片[{}]未指定转场，跳过转场添加", i);
                }
            }

            log.info("Pro版图片转场材料添加完成 - 总数: {}, 转场名称: {}, 转场时长: {}ms",
                    transitionIds.size(), transitionName,
                    (transitionDuration != null && transitionDuration > 0) ? transitionDuration : 500);
            return transitionIds;

        } catch (Exception e) {
            log.error("Pro版添加转场材料失败", e);
            throw new RuntimeException("添加转场材料失败: " + e.getMessage(), e);
        }
    }



    /**
     * 性能优化：批量预加载所有需要的动画ID（完全复制自稳定版逻辑）
     */
    private java.util.Map<String, org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo> preloadAnimationIdsInternal(com.alibaba.fastjson.JSONArray imageInfos) {
        try {
            log.info("Pro版开始预加载动画ID，图片数量: {}", imageInfos.size());

            java.util.Set<String> animationNames = new java.util.HashSet<>();

            // 收集所有需要的动画名称
            for (int i = 0; i < imageInfos.size(); i++) {
                JSONObject imageInfo = imageInfos.getJSONObject(i);

                String inAnimation = imageInfo.getString("in_animation");
                String outAnimation = imageInfo.getString("out_animation");
                String groupAnimation = imageInfo.getString("group_animation");

                // 处理入场动画（支持多个动画，用|分割）
                if (inAnimation != null && !inAnimation.trim().isEmpty() && !"无".equals(inAnimation)) {
                    addAnimationNamesFromString(animationNames, inAnimation, "in");
                }
                // 处理出场动画（支持多个动画，用|分割）
                if (outAnimation != null && !outAnimation.trim().isEmpty() && !"无".equals(outAnimation)) {
                    addAnimationNamesFromString(animationNames, outAnimation, "out");
                }
                // 处理循环动画（支持多个动画，用|分割）
                if (groupAnimation != null && !groupAnimation.trim().isEmpty() && !"无".equals(groupAnimation)) {
                    addAnimationNamesFromString(animationNames, groupAnimation, "group");
                }
            }

            // 批量获取动画信息
            java.util.Map<String, org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo> cache = new java.util.HashMap<>();
            for (String animationKey : animationNames) {
                String[] parts = animationKey.split(":");
                if (parts.length == 2) {
                    String animationName = parts[0];
                    String animationType = parts[1];

                    try {
                        org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo info =
                            jianyingIdResolverService.findAnimationByName(animationName, animationType);
                        if (info != null) {
                            cache.put(animationKey, info);
                            log.debug("Pro版动画ID预加载成功: {} -> {}", animationKey, info.getResourceId());
                        } else {
                            log.warn("Pro版动画ID预加载失败: {}", animationKey);
                        }
                    } catch (Exception e) {
                        log.error("Pro版动画ID预加载异常: {}", animationKey, e);
                    }
                }
            }

            log.info("Pro版动画ID预加载完成，成功: {}/{}", cache.size(), animationNames.size());
            return cache;

        } catch (Exception e) {
            log.error("Pro版预加载动画ID失败", e);
            return new java.util.HashMap<>();
        }
    }

    /**
     * 创建动画对象（使用预加载缓存，完全复制自稳定版逻辑）
     */
    private JSONObject createAnimationObjectWithCacheInternal(String animationName, String animationType, long duration,
                                                     String categoryName, long startTime, java.util.Map<String, org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo> animationCache) {
        try {
            log.debug("Pro版创建动画对象（缓存版本）: name={}, type={}, duration={}", animationName, animationType, duration);

            JSONObject animation = new JSONObject();
            animation.put("anim_adjust_params", null);  // 使用null而不是空数组，可能是动态参数
            animation.put("duration", duration);
            animation.put("name", animationName);
            animation.put("panel", "video");
            animation.put("path", "");                  // 添加缺失的path字段
            animation.put("platform", "all");
            animation.put("request_id", "");
            animation.put("start", startTime);          // 使用计算的开始时间
            animation.put("type", animationType);
            animation.put("category_name", categoryName);

            // 从缓存中获取动画信息
            String cacheKey = animationName + ":" + animationType;
            org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo animationInfo = animationCache.get(cacheKey);

            if (animationInfo != null) {
                // 使用缓存的真实ID
                animation.put("resource_id", animationInfo.getResourceId());
                animation.put("id", animationInfo.getId());
                animation.put("category_id", animationInfo.getCategoryId() != null ? animationInfo.getCategoryId() : "");
                animation.put("material_type", "video");  // 修改为video，与竞争对手一致
                log.debug("Pro版动画真实ID从缓存获取成功: {} -> resource_id: {}, id: {}",
                        animationName, animationInfo.getResourceId(), animationInfo.getId());
            } else {
                // 缓存中没有找到，抛出错误
                log.error("Pro版动画真实ID在缓存中未找到: {}", animationName);
                throw new RuntimeException("动画真实ID在缓存中未找到: " + animationName + "，请检查预加载逻辑");
            }

            return animation;

        } catch (Exception e) {
            log.error("Pro版创建动画对象失败: animationName={}, type={}", animationName, animationType, e);
            throw new RuntimeException("创建动画对象失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建图片片段对象（简化版本，用于不需要参数处理的场景）
     */
    private JSONObject createImageSegmentObject(String segmentId, String imageId, long startTime, long endTime,
                                              long duration, JSONObject imageInfo, int index) {
        JSONObject segment = new JSONObject(new java.util.LinkedHashMap<>());

        // 基础字段（完全复制自稳定版）
        segment.put("id", segmentId);
        segment.put("material_id", imageId);
        segment.put("render_index", 4000000 + index); // 使用索引确保唯一性
        segment.put("reverse", false);
        segment.put("speed", 1.0);
        segment.put("visible", true);
        segment.put("volume", 1.0);

        // 时间范围（完全复制自稳定版）
        segment.put("source_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", 0L);
        }});
        segment.put("target_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", startTime);
        }});

        // 变换属性（使用clip对象包装，完全复制自稳定版）
        segment.put("clip", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("alpha", 1.0); // 默认不透明
            put("flip", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("horizontal", false);
                put("vertical", false);
            }});
            put("rotation", 0);
            put("scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", 1.0);
                put("y", 1.0);
            }});
            put("transform", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", 0.0);
                put("y", 0.0);
            }});
        }});

        // 等比缩放控制（完全复制自稳定版）
        segment.put("uniform_scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("on", true);
            put("value", 1.0);
        }});

        // 其他标准字段（完全复制自稳定版）
        segment.put("caption_info", null);
        segment.put("cartoon", false);
        segment.put("common_keyframes", new com.alibaba.fastjson.JSONArray());
        segment.put("enable_adjust", true);
        segment.put("enable_color_correct_adjust", false);
        segment.put("enable_color_curves", true);
        segment.put("enable_color_match_adjust", false);
        segment.put("enable_color_wheels", true);
        segment.put("enable_lut", true);
        segment.put("enable_smart_color_adjust", false);
        segment.put("extra_material_refs", new com.alibaba.fastjson.JSONArray());
        segment.put("group_id", "");

        // HDR设置（完全复制自稳定版）
        segment.put("hdr_settings", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("intensity", 1);
            put("mode", 1);
            put("nits", 1000);
        }});

        segment.put("intensifies_audio", false);
        segment.put("is_placeholder", false);
        segment.put("is_tone_modify", false);
        segment.put("keyframe_refs", new com.alibaba.fastjson.JSONArray());
        segment.put("last_nonzero_volume", 1);

        // 响应式布局（完全复制自稳定版）
        segment.put("responsive_layout", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("enable", false);
            put("horizontal_pos_layout", 0);
            put("size_layout", 0);
            put("target_follow", "");
            put("vertical_pos_layout", 0);
        }});

        segment.put("template_id", "");
        segment.put("template_scene", "default");
        segment.put("track_attribute", 0);
        segment.put("track_render_index", 3); // 正确的轨道渲染索引

        return segment;
    }

    // ========== Pro版音频处理辅助方法（复制自稳定版完整逻辑） ==========

    /**
     * 创建音频轨道（复制自稳定版逻辑）
     */
    private JSONObject createAudioTrack(String trackId) {
        JSONObject track = new JSONObject(new java.util.LinkedHashMap<>());
        track.put("attribute", 0);
        track.put("flag", 0);
        track.put("id", trackId);
        track.put("is_default_name", true);
        track.put("name", "");
        track.put("segments", new com.alibaba.fastjson.JSONArray());
        track.put("type", "audio");
        return track;
    }

    /**
     * 添加音频材料到materials.audios（外部URL直接下载模式，与add_images_pro、add_videos_pro一致）
     */
    private String addAudioMaterialInternal(JSONObject draft, JSONObject audioInfo, int index) {
        try {
            String audioUrl = audioInfo.getString("audio_url");
            log.info("Pro版开始添加音频材料[{}]: {}", index, audioUrl);

            // 生成音频材料ID
            String audioMaterialId = java.util.UUID.randomUUID().toString();

            // 获取统一文件夹ID（与add_images_pro、add_videos_pro完全一致）
            String unifiedFolderId = cozeApiService.extractOrCreateUnifiedFolderId(draft);

            // 外部URL直接引用模式：跳过TOS上传优化，直接使用原始URL
            String originalUrl = "";
            boolean urlValid = false;

            if (audioUrl != null && !audioUrl.trim().isEmpty()) {
                // URL格式验证
                if (isValidAudioURL(audioUrl)) {
                    originalUrl = audioUrl;
                    urlValid = true;
                    log.info("音频URL格式验证通过[{}]: {}", index, audioUrl);

                    // 可选的连通性检查（不阻塞处理）
                    if (!checkAudioURLAccessible(audioUrl)) {
                        log.warn("音频URL连通性检查失败[{}]: {}", index, audioUrl);
                    }
                } else {
                    // URL格式错误，抛出异常
                    throw new RuntimeException("音频URL格式不正确: " + audioUrl);
                }
            } else {
                log.info("音频对象[{}]无audio_url，创建占位符材料", index);
            }

            // 添加到materials.audios
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray audios = materials.getJSONArray("audios");

            // 创建音频材料对象（外部URL直接引用模式）
            JSONObject audioMaterial = createAudioMaterialWithOriginalURL(audioMaterialId, originalUrl, audioInfo, urlValid, unifiedFolderId);
            audios.add(audioMaterial);

            log.info("Pro版音频材料添加成功[{}]: ID={}, URL={}, 有效={}", index, audioMaterialId, originalUrl, urlValid);
            return audioMaterialId;

        } catch (Exception e) {
            log.error("Pro版添加音频材料失败[{}]", index, e);
            throw new RuntimeException("添加音频材料失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加音频段到轨道（关联音效，完全复制自稳定版逻辑）
     */
    private String addAudioSegmentInternalWithEffect(JSONObject track, JSONObject audioInfo, String audioMaterialId, String audioEffectId, int index) {
        try {
            log.info("Pro版开始添加音频段[{}]: 材料ID={}, 音效ID={}", index, audioMaterialId, audioEffectId);

            // 生成段ID
            String segmentId = java.util.UUID.randomUUID().toString();

            // 获取时间信息
            long startTime = audioInfo.getLong("start") != null ? audioInfo.getLong("start") : index * 5000000L;
            long endTime = audioInfo.getLong("end") != null ? audioInfo.getLong("end") : (index + 1) * 5000000L;
            long duration = endTime - startTime;

            // 获取音量信息（如果有）
            double volume = 1.0; // 默认音量
            if (audioInfo.containsKey("volume")) {
                volume = audioInfo.getDoubleValue("volume");
            }

            // 创建extra_material_refs数组（关联音效）
            com.alibaba.fastjson.JSONArray extraMaterialRefs = new com.alibaba.fastjson.JSONArray();
            if (audioEffectId != null) {
                extraMaterialRefs.add(audioEffectId);
            }

            // 创建音频段对象（与竞争对手完全一致）
            JSONObject segment = new JSONObject(new java.util.LinkedHashMap<>());
            segment.put("cartoon", false);
            segment.put("clip", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("alpha", 1.0);
                put("flip", new JSONObject());
                put("rotation", 0.0);
                put("scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
                    put("x", 1.0);
                    put("y", 1.0);
                }});
                put("transform", new JSONObject(new java.util.LinkedHashMap<>()) {{
                    put("x", 0.0);
                    put("y", 0.0);
                }});
            }});
            segment.put("common_keyframes", new com.alibaba.fastjson.JSONArray());
            segment.put("enable_adjust", true);
            segment.put("enable_color_curves", true);
            segment.put("enable_color_match_adjust", false);
            segment.put("enable_color_wheels", true);
            segment.put("enable_lut", true);
            segment.put("enable_smart_color_adjust", false);
            segment.put("extra_material_refs", extraMaterialRefs); // 关联音效
            segment.put("group_id", "");
            segment.put("hdr_settings", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("intensity", 1);
                put("mode", 1);
                put("nits", 1000);
            }});
            segment.put("id", segmentId);
            segment.put("intensifies_audio", false);
            segment.put("is_placeholder", false);
            segment.put("is_tone_modify", false);
            segment.put("keyframe_refs", new com.alibaba.fastjson.JSONArray());
            segment.put("last_nonzero_volume", 1.0);
            segment.put("material_id", audioMaterialId);
            segment.put("render_index", 1); // 竞争对手使用1
            segment.put("reverse", false);
            segment.put("source_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("duration", duration);
                put("start", 0L);
            }});
            segment.put("speed", 1.0);
            segment.put("target_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("duration", duration);
                put("start", startTime);
            }});
            segment.put("template_id", "");
            segment.put("template_scene", "default");
            segment.put("track_attribute", 0);
            segment.put("track_render_index", 3); // 竞争对手使用3
            segment.put("uniform_scale", new JSONObject());
            segment.put("visible", true);
            segment.put("volume", volume);
            segment.put("caption_info", null); // 竞争对手有这个字段
            segment.put("responsive_layout", new JSONObject()); // 竞争对手有这个字段

            // 添加到轨道的segments数组
            com.alibaba.fastjson.JSONArray segments = track.getJSONArray("segments");
            if (segments == null) {
                segments = new com.alibaba.fastjson.JSONArray();
                track.put("segments", segments);
            }
            segments.add(segment);

            log.info("Pro版音频段添加成功[{}]: ID={}, 开始时间={}, 结束时间={}, 音量={}, 音效关联={}",
                    index, segmentId, startTime, endTime, volume, audioEffectId != null);
            return segmentId;

        } catch (Exception e) {
            log.error("Pro版添加音频段失败[{}]", index, e);
            throw new RuntimeException("添加音频段失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加音频片段到轨道（复制自稳定版逻辑）
     */
    private String addAudioSegmentInternal(JSONObject track, JSONObject audioInfo, String audioId, int index) {
        try {
            log.debug("Pro版开始添加音频片段[{}]: audioId={}", index, audioId);

            // 生成片段ID
            String segmentId = java.util.UUID.randomUUID().toString();

            // 使用audioInfo中的实际时间范围
            long startTime = audioInfo.getLong("start") != null ? audioInfo.getLong("start") : index * 5000000L;
            long endTime = audioInfo.getLong("end") != null ? audioInfo.getLong("end") : (index + 1) * 5000000L;
            long duration = endTime - startTime;

            // 创建音频片段对象（复制自稳定版逻辑）
            JSONObject segment = createAudioSegmentObject(segmentId, audioId, startTime, endTime, duration, audioInfo, index);

            // 添加到轨道的segments数组
            com.alibaba.fastjson.JSONArray segments = track.getJSONArray("segments");
            if (segments == null) {
                segments = new com.alibaba.fastjson.JSONArray();
                track.put("segments", segments);
            }
            segments.add(segment);

            log.info("Pro版音频片段添加成功[{}]: ID={}, 时间范围={}~{}", index, segmentId, startTime, endTime);
            return segmentId;

        } catch (Exception e) {
            log.error("Pro版添加音频片段失败[{}]", index, e);
            throw new RuntimeException("添加音频片段失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加音频效果到materials.audio_effects（完全复制自稳定版逻辑）
     */
    private String addAudioEffectStandardInternal(JSONObject draft, String audioEffect) {
        try {
            log.info("Pro版添加音频效果: {}", audioEffect);

            // 生成音频效果ID
            String audioEffectId = java.util.UUID.randomUUID().toString();

            // 创建完整的audio_adjust_params（与竞争对手一致）
            com.alibaba.fastjson.JSONArray audioAdjustParams = new com.alibaba.fastjson.JSONArray();
            JSONObject adjustParam = new JSONObject(new java.util.LinkedHashMap<>());
            adjustParam.put("parameterIndex", 0);
            adjustParam.put("portIndex", 0);
            adjustParam.put("sliderName", "强度");
            adjustParam.put("minValue", 0);
            adjustParam.put("defaultValue", 1);
            adjustParam.put("maxValue", 1);
            adjustParam.put("name", "强度");
            adjustParam.put("max_value", 1);
            adjustParam.put("min_value", 0);
            adjustParam.put("value", 1);
            adjustParam.put("default_value", 1);
            audioAdjustParams.add(adjustParam);

            // 创建音频效果对象（与竞争对手完全一致）
            JSONObject audioEffectObj = new JSONObject(new java.util.LinkedHashMap<>());
            audioEffectObj.put("audio_adjust_params", audioAdjustParams);
            audioEffectObj.put("category_id", "sound_effect");
            audioEffectObj.put("category_name", "场景音");
            audioEffectObj.put("id", audioEffectId);
            audioEffectObj.put("is_ugc", false);
            audioEffectObj.put("name", audioEffect);
            audioEffectObj.put("production_path", "");
            audioEffectObj.put("resource_id", "7282691146759803429");
            audioEffectObj.put("speaker_id", "");
            audioEffectObj.put("sub_type", 1);
            audioEffectObj.put("time_range", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("duration", 0);
                put("start", 0);
            }});
            audioEffectObj.put("type", "audio_effect");
            audioEffectObj.put("is_vip", 1);

            // 添加到materials.audio_effects数组
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray audioEffectsArray = materials.getJSONArray("audio_effects");
            audioEffectsArray.add(audioEffectObj);

            log.info("Pro版音频效果添加成功: {} (ID: {})", audioEffect, audioEffectId);
            return audioEffectId;

        } catch (Exception e) {
            log.error("Pro版添加音频效果失败: {}", audioEffect, e);
            throw new RuntimeException("添加音频效果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建音频材料对象（完全复制稳定版的剪映标准格式）
     */
    private JSONObject createAudioMaterialObjectStandard(String audioMaterialId, String audioFileName, String audioDownloadUrl, JSONObject audioInfo, String subFolderId) {
        // 生成剪映标准的path格式：只有当有文件时才设置path
        String jianyingPath = "";
        if (audioFileName != null && !audioFileName.isEmpty() && subFolderId != null && !subFolderId.isEmpty()) {
            String unifiedFolderId = "0E685133-18CE-45ED-8CB8-2904A212EC80";
            jianyingPath = "##_draftpath_placeholder_" + unifiedFolderId + "_##\\" + subFolderId + "\\" + audioFileName;
        }

        // 生成随机的name（UUID格式，与稳定版一致）
        String materialName = java.util.UUID.randomUUID().toString();

        // 创建剪映标准的音频材料对象（完全复制稳定版）
        JSONObject audioMaterial = new JSONObject(new java.util.LinkedHashMap<>());
        audioMaterial.put("app_id", 0);
        audioMaterial.put("category_id", "");
        audioMaterial.put("category_name", "local");
        audioMaterial.put("check_flag", 3);
        audioMaterial.put("copyright_limit_type", "none");
        audioMaterial.put("effect_id", "");
        audioMaterial.put("formula_id", "");
        audioMaterial.put("id", audioMaterialId);
        audioMaterial.put("intensifies_path", "");
        audioMaterial.put("is_ai_clone_tone", false);
        audioMaterial.put("is_text_edit_overdub", false);
        audioMaterial.put("is_ugc", false);
        audioMaterial.put("local_material_id", "");
        audioMaterial.put("music_id", "");
        audioMaterial.put("name", materialName);
        audioMaterial.put("path", jianyingPath);
        audioMaterial.put("query", "");
        audioMaterial.put("request_id", "");
        audioMaterial.put("resource_id", "");
        audioMaterial.put("search_id", "");
        audioMaterial.put("source_from", "");
        audioMaterial.put("source_platform", 0);
        audioMaterial.put("team_id", "");
        audioMaterial.put("text_id", "");
        audioMaterial.put("tone_category_id", "");
        audioMaterial.put("tone_category_name", "");
        audioMaterial.put("tone_effect_id", "");
        audioMaterial.put("tone_effect_name", "");
        audioMaterial.put("tone_platform", "");
        audioMaterial.put("tone_second_category_id", "");
        audioMaterial.put("tone_second_category_name", "");
        audioMaterial.put("tone_speaker", "");
        audioMaterial.put("tone_type", "");
        audioMaterial.put("type", "extract_music");
        audioMaterial.put("video_id", "");
        audioMaterial.put("wave_points", new com.alibaba.fastjson.JSONArray());

        // ========== 添加客户端下载需要的字段（完全复制稳定版逻辑） ==========
        if (audioDownloadUrl != null && !audioDownloadUrl.isEmpty()) {
            audioMaterial.put("download_url", audioDownloadUrl);
            audioMaterial.put("file_name", audioFileName);
        }

        return audioMaterial;
    }

    /**
     * 创建音频材料对象（外部URL直接引用模式，与add_images_pro、add_videos_pro一致）
     */
    private JSONObject createAudioMaterialWithOriginalURL(String audioMaterialId, String originalUrl,
                                                         JSONObject audioInfo, boolean urlValid, String unifiedFolderId) {
        // 生成随机的material_name（UUID格式，与add_images_pro、add_videos_pro一致）
        String materialName = java.util.UUID.randomUUID().toString();

        JSONObject audioMaterial = new JSONObject(new java.util.LinkedHashMap<>());

        // 基础信息
        audioMaterial.put("id", audioMaterialId);
        audioMaterial.put("material_id", audioMaterialId);
        audioMaterial.put("material_name", materialName);
        audioMaterial.put("material_url", originalUrl); // 直接使用原始URL

        // 修复：path字段使用Electron期望的Windows路径格式（统一文件夹模式）
        String electronPath = urlValid ?
            generateAudioUnifiedFolderPath(audioMaterialId, originalUrl, unifiedFolderId) :
            "";
        audioMaterial.put("path", electronPath);

        audioMaterial.put("type", "extract_music");
        audioMaterial.put("source_platform", urlValid ? "external" : "local"); // 标识外部URL

        // 音频属性
        audioMaterial.put("duration", 5000000L); // 5秒固定时长
        audioMaterial.put("has_audio", true);

        // 音频特有字段（完整的剪映格式）
        audioMaterial.put("app_id", 0);
        audioMaterial.put("category_id", "");
        audioMaterial.put("category_name", "local");
        audioMaterial.put("check_flag", 3);
        audioMaterial.put("copyright_limit_type", "none");
        audioMaterial.put("create_time", System.currentTimeMillis() * 1000L);
        audioMaterial.put("effect_id", "");
        audioMaterial.put("file_Path", "");
        audioMaterial.put("formula_id", "");
        audioMaterial.put("import_time", System.currentTimeMillis() * 1000L);
        audioMaterial.put("import_time_ms", System.currentTimeMillis());
        audioMaterial.put("intensifies_path", "");
        audioMaterial.put("is_ai_clone_tone", false);
        audioMaterial.put("is_text_edit_overdub", false);
        audioMaterial.put("is_ugc", false);
        audioMaterial.put("local_material_id", "");
        audioMaterial.put("music_id", "");
        audioMaterial.put("name", materialName);
        audioMaterial.put("query", "");
        audioMaterial.put("request_id", "");
        audioMaterial.put("resource_id", "");
        audioMaterial.put("search_id", "");
        audioMaterial.put("source_from", "");
        audioMaterial.put("team_id", "");
        audioMaterial.put("text_id", "");
        audioMaterial.put("tone_category_id", "");
        audioMaterial.put("tone_category_name", "");
        audioMaterial.put("tone_effect_id", "");
        audioMaterial.put("tone_effect_name", "");
        audioMaterial.put("tone_platform", "");
        audioMaterial.put("tone_second_category_id", "");
        audioMaterial.put("tone_second_category_name", "");
        audioMaterial.put("tone_speaker", "");
        audioMaterial.put("tone_type", "");
        audioMaterial.put("video_id", "");
        audioMaterial.put("wave_points", new com.alibaba.fastjson.JSONArray());

        // 下载相关字段
        audioMaterial.put("download_url", originalUrl);  // 使用原始URL
        audioMaterial.put("original_url", originalUrl);  // 保留原始URL引用

        return audioMaterial;
    }

    /**
     * 生成音频统一文件夹路径（完全复制稳定版格式，修复音频丢失问题）
     */
    private String generateAudioUnifiedFolderPath(String audioMaterialId, String originalUrl, String unifiedFolderId) {
        // 从URL中提取文件名，如果失败则使用默认名
        String baseFileName;
        try {
            java.net.URL url = new java.net.URL(originalUrl);
            String path = url.getPath();
            baseFileName = path.substring(path.lastIndexOf('/') + 1);
            if (baseFileName.isEmpty() || !baseFileName.contains(".")) {
                baseFileName = "audio.mp3";
            }
        } catch (Exception e) {
            baseFileName = "audio.mp3";
        }

        // 生成带素材ID前缀的文件名，确保唯一性（与稳定版一致）
        String uniqueFileName = audioMaterialId + "_" + baseFileName;

        // 生成剪映期望的路径格式（使用固定的placeholder UUID）
        String draftPlaceholder = "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##";
        return draftPlaceholder + "\\" + unifiedFolderId + "\\" + uniqueFileName;
    }

    /**
     * 验证音频URL格式（与add_images_pro、add_videos_pro一致）
     */
    private boolean isValidAudioURL(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }

        try {
            // 基本URL格式验证
            new java.net.URL(url);

            // 音频文件格式验证（支持常见音频格式）
            return url.matches("^https?://.*\\.(mp3|wav|m4a|aac|ogg|flac|wma).*$");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查音频URL连通性（与add_images_pro、add_videos_pro一致）
     */
    private boolean checkAudioURLAccessible(String url) {
        try {
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) new java.net.URL(url).openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(3000);
            connection.setReadTimeout(3000);

            int responseCode = connection.getResponseCode();
            return responseCode >= 200 && responseCode < 400;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 创建音频材料对象（旧版本，保留以防需要）
     */
    private JSONObject createAudioMaterialObject(String audioMaterialId, String originalUrl, JSONObject audioInfo,
                                                boolean urlValid, String unifiedFolderId, long audioDuration) {
        // 生成随机的material_name（UUID格式）
        String materialName = java.util.UUID.randomUUID().toString();

        JSONObject audioMaterial = new JSONObject(new java.util.LinkedHashMap<>());

        // 基础信息
        audioMaterial.put("id", audioMaterialId);
        audioMaterial.put("material_id", audioMaterialId);
        audioMaterial.put("material_name", materialName);
        audioMaterial.put("material_url", originalUrl);

        // path字段（Electron期望的Windows路径格式）
        String electronPath = urlValid ?
            "##_draftpath_placeholder_" + unifiedFolderId + "_##\\" + unifiedFolderId + "\\" + materialName + ".mp3" :
            "";
        audioMaterial.put("path", electronPath);

        // 基本属性
        audioMaterial.put("create_time", System.currentTimeMillis() * 1000);
        audioMaterial.put("duration", audioDuration);
        audioMaterial.put("extra_info", "");
        audioMaterial.put("file_Path", "");
        audioMaterial.put("import_time", System.currentTimeMillis() * 1000);
        audioMaterial.put("import_time_ms", System.currentTimeMillis());
        audioMaterial.put("md5", "");
        audioMaterial.put("roughcut_time_range", new JSONObject() {{
            put("duration", -1);
            put("start", -1);
        }});
        audioMaterial.put("sub_time_range", new JSONObject() {{
            put("duration", -1);
            put("start", -1);
        }});
        audioMaterial.put("type", 1); // 音频类型为1
        audioMaterial.put("download_url", originalUrl);
        audioMaterial.put("original_url", originalUrl);

        // 音频特有属性
        Double volume = audioInfo.getDouble("volume");
        if (volume != null) {
            audioMaterial.put("volume", volume);
        } else {
            audioMaterial.put("volume", 1.0);
        }

        String audioEffect = audioInfo.getString("audio_effect");
        if (audioEffect != null && !audioEffect.trim().isEmpty()) {
            audioMaterial.put("audio_effect", audioEffect);
        }

        return audioMaterial;
    }

    /**
     * 创建音频片段对象（完全复制自稳定版createAudioSegmentObject逻辑）
     */
    private JSONObject createAudioSegmentObject(String segmentId, String audioId, long startTime, long endTime,
                                              long duration, JSONObject audioInfo, int index) {
        JSONObject segment = new JSONObject(new java.util.LinkedHashMap<>());

        // 音频属性
        Double volume = audioInfo.getDouble("volume");
        double finalVolume = volume != null ? volume : 1.0;

        // 基础字段（完全复制自稳定版）
        segment.put("cartoon", false);
        segment.put("clip", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("alpha", 1.0);
            put("flip", new JSONObject());
            put("rotation", 0.0);
            put("scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", 1.0);
                put("y", 1.0);
            }});
            put("transform", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", 0.0);
                put("y", 0.0);
            }});
        }});
        segment.put("common_keyframes", new com.alibaba.fastjson.JSONArray());
        segment.put("enable_adjust", true);
        segment.put("enable_color_curves", true);
        segment.put("enable_color_match_adjust", false);
        segment.put("enable_color_wheels", true);
        segment.put("enable_lut", true);
        segment.put("enable_smart_color_adjust", false);
        segment.put("extra_material_refs", new com.alibaba.fastjson.JSONArray());
        segment.put("group_id", "");
        segment.put("hdr_settings", new JSONObject());
        segment.put("id", segmentId);
        segment.put("intensifies_audio", false);
        segment.put("is_placeholder", false);
        segment.put("is_tone_modify", false);
        segment.put("keyframe_refs", new com.alibaba.fastjson.JSONArray());
        segment.put("last_nonzero_volume", 1.0);
        segment.put("material_id", audioId);
        segment.put("render_index", 4000000 + index); // 使用索引确保唯一性
        segment.put("reverse", false);
        segment.put("source_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", 0L);
        }});
        segment.put("speed", 1.0);
        segment.put("target_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", startTime);
        }});
        segment.put("template_id", "");
        segment.put("template_scene", "default");
        segment.put("track_attribute", 0);
        segment.put("track_render_index", 0);
        segment.put("uniform_scale", new JSONObject());
        segment.put("visible", true);
        segment.put("volume", finalVolume);

        return segment;
    }

    // ========== Pro版字幕处理方法（复制自稳定版完整逻辑） ==========

    /**
     * Pro版内部生成字幕信息（复制自稳定版caption_infos的完整处理逻辑）
     * 实现真正的代码隔离，不依赖稳定版Service
     */
    private String generateCaptionInfosInternal(JianyingProAddCaptionsRequest request) {
        try {
            log.info("Pro版开始生成字幕信息，文本数量: {}", request.getTexts().size());

            // 参数处理（专注keywords数组，移除单个keyword参数）
            java.util.List<String> texts = request.getTexts();
            java.util.List<JSONObject> timelines = request.getTimelines();
            Integer fontSize = request.getFontSize();
            String keywordColor = request.getKeywordColor();
            java.util.List<String> keywords = request.getKeywords();
            Integer keywordFontSize = request.getKeywordFontSize();
            String inAnimation = request.getInAnimation();
            String outAnimation = request.getOutAnimation();
            String loopAnimation = request.getLoopAnimation();
            Long inAnimationDuration = request.getInAnimationDuration();
            Long outAnimationDuration = request.getOutAnimationDuration();
            Long loopAnimationDuration = request.getLoopAnimationDuration();

            // 参数验证（复制自稳定版逻辑）
            if (texts == null || texts.isEmpty()) {
                throw new RuntimeException("文本列表不能为空");
            }
            if (timelines == null || timelines.isEmpty()) {
                throw new RuntimeException("时间线列表不能为空");
            }

            log.info("Pro版字幕参数 - 文本数量: {}, 时间线数量: {}, 字体大小: {}, 关键词数组: {}",
                    texts.size(), timelines.size(), fontSize, (keywords != null ? keywords.size() : 0));

            // 生成字幕信息JSON字符串（复制自稳定版的完整逻辑）
            StringBuilder jsonBuilder = new StringBuilder("[");
            for (int i = 0; i < timelines.size(); i++) {
                JSONObject timeline = timelines.get(i);

                // 文本分配策略：按索引分配，超出范围则为空字符串（与imgs_infos、video_infos一致）
                String text = (i < texts.size()) ? texts.get(i) : "";

                if (i > 0) jsonBuilder.append(",");

                // 直接拼接JSON字符串，保证字段顺序：start → end → text → keyword → keyword_color → keyword_font_size → font_size → 动画字段
                jsonBuilder.append("{\"start\":")
                           .append(timeline.getLong("start"))
                           .append(",\"end\":")
                           .append(timeline.getLong("end"))
                           .append(",\"text\":\"")
                           .append(text.replace("\"", "\\\""))
                           .append("\"");

                // 添加可选字段（专注keywords数组，移除单个keyword）
                if (keywordColor != null && !keywordColor.trim().isEmpty()) {
                    jsonBuilder.append(",\"keyword_color\":\"").append(keywordColor.replace("\"", "\\\"")).append("\"");
                }
                if (keywords != null && !keywords.isEmpty()) {
                    jsonBuilder.append(",\"keywords\":[");
                    for (int k = 0; k < keywords.size(); k++) {
                        if (k > 0) jsonBuilder.append(",");
                        jsonBuilder.append("\"").append(keywords.get(k).replace("\"", "\\\"")).append("\"");
                    }
                    jsonBuilder.append("]");
                }
                if (keywordFontSize != null) {
                    jsonBuilder.append(",\"keyword_font_size\":").append(keywordFontSize);
                }
                if (fontSize != null) {
                    jsonBuilder.append(",\"font_size\":").append(fontSize);
                }

                // 动画字段（复制自稳定版逻辑）
                if (inAnimation != null && !inAnimation.trim().isEmpty()) {
                    jsonBuilder.append(",\"in_animation\":\"").append(inAnimation.replace("\"", "\\\"")).append("\"");
                }
                if (outAnimation != null && !outAnimation.trim().isEmpty()) {
                    jsonBuilder.append(",\"out_animation\":\"").append(outAnimation.replace("\"", "\\\"")).append("\"");
                }
                if (loopAnimation != null && !loopAnimation.trim().isEmpty()) {
                    jsonBuilder.append(",\"loop_animation\":\"").append(loopAnimation.replace("\"", "\\\"")).append("\"");
                }

                // 动画时长字段（复制自稳定版逻辑）
                if (inAnimationDuration != null) {
                    jsonBuilder.append(",\"in_animation_duration\":").append(inAnimationDuration);
                }
                if (outAnimationDuration != null) {
                    jsonBuilder.append(",\"out_animation_duration\":").append(outAnimationDuration);
                }
                if (loopAnimationDuration != null) {
                    jsonBuilder.append(",\"loop_animation_duration\":").append(loopAnimationDuration);
                }

                jsonBuilder.append("}");
            }
            jsonBuilder.append("]");

            log.info("Pro版字幕数据生成完成 - 生成了{}个字幕对象", timelines.size());
            log.debug("Pro版生成的字幕数据: {}", jsonBuilder.toString());

            return jsonBuilder.toString();

        } catch (Exception e) {
            log.error("Pro版生成字幕信息失败", e);
            return null;
        }
    }

    /**
     * Pro版内部添加字幕（复制自稳定版add_captions的完整处理逻辑）
     * 实现真正的代码隔离，不依赖稳定版Service
     */
    private JSONObject addCaptionsInternal(String draftUrl, String captionInfosJson, JianyingProAddCaptionsRequest request) {
        try {
            log.info("Pro版开始添加字幕到草稿，字幕数量: {}", request.getTexts().size());

            // 第1步：下载并解析草稿文件（复制自稳定版逻辑）
            JSONObject draft = downloadAndParseDraft(draftUrl);
            if (draft == null) {
                return createErrorResult("草稿文件下载失败", "PRO_DRAFT_DOWNLOAD_ERROR");
            }

            // 第2步：解析字幕信息数组（复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray captionsArray;
            try {
                captionsArray = com.alibaba.fastjson.JSONArray.parseArray(captionInfosJson);
                if (captionsArray == null || captionsArray.isEmpty()) {
                    return createErrorResult("字幕信息解析失败或为空", "PRO_CAPTION_INFOS_PARSE_ERROR");
                }
            } catch (Exception e) {
                log.error("Pro版解析字幕信息失败: {}", e.getMessage());
                return createErrorResult("字幕信息JSON格式错误: " + e.getMessage(), "PRO_CAPTION_INFOS_FORMAT_ERROR");
            }

            // 第3步：处理字幕添加逻辑（复制自稳定版逻辑）
            JSONObject result = processCaptionAddition(draft, captionsArray, draftUrl, request);
            if (result.containsKey("error")) {
                return result;
            }

            // 第4步：保存草稿文件（复制自稳定版逻辑）
            boolean saveSuccess = saveDraftFile(draftUrl, draft);
            if (!saveSuccess) {
                return createErrorResult("草稿文件保存失败", "PRO_DRAFT_SAVE_ERROR");
            }

            log.info("Pro版字幕添加完成，草稿已保存: {}", draftUrl);
            return result;

        } catch (Exception e) {
            log.error("Pro版添加字幕失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", "Pro版添加字幕失败: " + e.getMessage());
            errorResult.put("error_code", "PRO_ADD_CAPTIONS_ERROR");
            return errorResult;
        }
    }

    /**
     * 处理字幕添加逻辑（复制自稳定版逻辑）
     */
    private JSONObject processCaptionAddition(JSONObject draft, com.alibaba.fastjson.JSONArray captionsArray, String draftUrl, JianyingProAddCaptionsRequest request) {
        try {
            log.info("Pro版处理字幕添加逻辑，字幕数量: {}", captionsArray.size());

            // 第1步：确保materials对象存在且有正确的结构（复制自稳定版逻辑）
            JSONObject materials = draft.getJSONObject("materials");
            if (materials == null) {
                materials = new JSONObject(new java.util.LinkedHashMap<>());
                draft.put("materials", materials);
            }

            // 确保materials.texts数组存在
            com.alibaba.fastjson.JSONArray texts = materials.getJSONArray("texts");
            if (texts == null) {
                texts = new com.alibaba.fastjson.JSONArray();
                materials.put("texts", texts);
            }

            // 确保materials.material_animations数组存在（完全复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray materialAnimations = materials.getJSONArray("material_animations");
            if (materialAnimations == null) {
                materialAnimations = new com.alibaba.fastjson.JSONArray();
                materials.put("material_animations", materialAnimations);
            }

            // 第2步：确保tracks数组存在（复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray tracks = draft.getJSONArray("tracks");
            if (tracks == null) {
                tracks = new com.alibaba.fastjson.JSONArray();
                draft.put("tracks", tracks);
            }

            // 第3步：获取画布尺寸用于坐标转换（复制自稳定版逻辑）
            JSONObject canvasConfig = draft.getJSONObject("canvas_config");
            int canvasWidth = canvasConfig != null ? canvasConfig.getIntValue("width") : 1080;
            int canvasHeight = canvasConfig != null ? canvasConfig.getIntValue("height") : 1920;

            log.info("Pro版字幕坐标转换基准 - 画布尺寸: {}x{}", canvasWidth, canvasHeight);

            // 第4步：批量预加载所有需要的文字动画ID（完全复制自稳定版逻辑）
            java.util.Map<String, org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo> animationCache = preloadTextAnimationIdsInternal(captionsArray);
            log.info("Pro版文字动画ID预加载完成，缓存数量: {}", animationCache.size());

            // 第5步：创建新的文字轨道（复制自稳定版逻辑）
            String newTrackId = java.util.UUID.randomUUID().toString().toUpperCase();
            JSONObject newTrack = createTextTrack(newTrackId);
            tracks.add(newTrack);

            // 第6步：处理每个字幕，添加材料和片段（使用完整版本逻辑）
            java.util.List<String> textIds = new java.util.ArrayList<>();
            java.util.List<String> segmentIds = new java.util.ArrayList<>();
            java.util.List<JSONObject> segmentTimes = new java.util.ArrayList<>();

            for (int i = 0; i < captionsArray.size(); i++) {
                JSONObject caption = captionsArray.getJSONObject(i);
                try {
                    // 1. 先添加文字动画材料（如果有）
                    String animationId = addTextAnimationMaterialInternal(draft, caption, i, animationCache);

                    // 2. 添加字幕材料到materials.texts
                    String textId = addTextMaterialInternal(draft, caption, request, i);
                    textIds.add(textId);

                    // 3. 添加字幕段（关联动画ID）
                    String segmentId = addTextSegmentInternal(newTrack, caption, textId, animationId, request, i, canvasWidth, canvasHeight);
                    segmentIds.add(segmentId);

                    // 保存时间信息用于生成segment_infos
                    JSONObject timeInfo = new JSONObject();
                    long captionStart = caption.getLong("start") != null ? caption.getLong("start") : i * 5000000L;
                    long captionEnd = caption.getLong("end") != null ? caption.getLong("end") : (i + 1) * 5000000L;
                    timeInfo.put("start", captionStart);
                    timeInfo.put("end", captionEnd);
                    segmentTimes.add(timeInfo);

                    log.info("Pro版字幕处理成功[{}]: textId={}, segmentId={}", i, textId, segmentId);

                } catch (Exception e) {
                    log.error("Pro版处理字幕失败[{}]: {}", i, caption.getString("text"), e);
                    // 创建占位符ID保持数组长度一致
                    final int index = i; // 创建final变量供内部类使用
                    String placeholderTextId = "placeholder_text_" + i;
                    String placeholderSegmentId = "placeholder_segment_" + i;
                    textIds.add(placeholderTextId);
                    segmentIds.add(placeholderSegmentId);

                    // 创建时间信息对象
                    JSONObject timeInfo = new JSONObject();
                    timeInfo.put("start", index * 5000000L);
                    timeInfo.put("end", (index + 1) * 5000000L);
                    segmentTimes.add(timeInfo);
                }
            }

            // 第6步：返回处理结果（复制自稳定版格式）
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("track_id", newTrackId);
            result.put("text_ids", textIds);
            result.put("segment_ids", segmentIds);
            result.put("draft_url", draftUrl);
            result.put("segment_infos", segmentTimes);

            log.info("Pro版字幕添加逻辑处理完成，轨道ID: {}, 字幕数量: {}", newTrackId, textIds.size());
            return result;

        } catch (Exception e) {
            log.error("Pro版处理字幕添加失败: {}", e.getMessage());
            return createErrorResult("字幕添加处理失败: " + e.getMessage(), "PRO_CAPTION_PROCESS_ERROR");
        }
    }

    // ========== Pro版字幕处理辅助方法（复制自稳定版完整逻辑） ==========

    /**
     * 创建文字轨道（复制自稳定版逻辑）
     */
    private JSONObject createTextTrack(String trackId) {
        JSONObject track = new JSONObject(new java.util.LinkedHashMap<>());
        track.put("attribute", 0);
        track.put("flag", 3);  // 修复：使用稳定版的正确值，确保轨道正常显示
        track.put("id", trackId);
        track.put("is_default_name", true);
        track.put("name", "");
        track.put("segments", new com.alibaba.fastjson.JSONArray());
        track.put("type", "text");
        return track;
    }

    /**
     * 添加字幕材料到materials.texts（复制自稳定版逻辑，增强keywords数组支持）
     */
    private String addTextMaterialInternal(JSONObject draft, JSONObject caption, JianyingProAddCaptionsRequest request, int index) {
        try {
            String text = caption.getString("text");
            String keywordColor = caption.getString("keyword_color");
            Integer fontSize = caption.getInteger("font_size");
            Integer keywordFontSize = caption.getInteger("keyword_font_size");

            // 处理keywords数组参数（专注多关键词支持，移除单个keyword）
            com.alibaba.fastjson.JSONArray keywordsArray = caption.getJSONArray("keywords");
            java.util.List<String> keywords = null;
            if (keywordsArray != null && !keywordsArray.isEmpty()) {
                keywords = new java.util.ArrayList<>();
                for (int k = 0; k < keywordsArray.size(); k++) {
                    String kw = keywordsArray.getString(k);
                    if (kw != null && !kw.trim().isEmpty()) {
                        keywords.add(kw.trim());
                    }
                }
            }

            log.info("Pro版开始添加字幕材料[{}]: text={}, keywords={}", index, text, (keywords != null ? keywords.size() + "个关键词" : "无关键词"));

            // 生成字幕材料ID
            String textMaterialId = java.util.UUID.randomUUID().toString();

            // 创建字幕材料对象（专注keywords数组支持，移除单个keyword）
            JSONObject textMaterial = createTextMaterialObjectWithKeywords(textMaterialId, text, keywords, keywordColor, fontSize, keywordFontSize, request, index);

            // 添加到materials.texts数组
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray textsArray = materials.getJSONArray("texts");
            textsArray.add(textMaterial);

            log.info("Pro版字幕材料添加成功[{}]: ID={}, 文本={}", index, textMaterialId, text);
            return textMaterialId;

        } catch (Exception e) {
            log.error("Pro版添加字幕材料失败[{}]", index, e);
            throw new RuntimeException("添加字幕材料失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加字幕片段到轨道（支持动画关联，完全复制自稳定版逻辑）
     */
    private String addTextSegmentInternal(JSONObject track, JSONObject caption, String textId, String animationId, JianyingProAddCaptionsRequest request, int index, int canvasWidth, int canvasHeight) {
        try {
            log.debug("Pro版开始添加字幕片段[{}]: textId={}", index, textId);

            // 生成片段ID
            String segmentId = java.util.UUID.randomUUID().toString();

            // 使用caption中的实际时间范围
            long startTime = caption.getLong("start") != null ? caption.getLong("start") : index * 5000000L;
            long endTime = caption.getLong("end") != null ? caption.getLong("end") : (index + 1) * 5000000L;
            long duration = endTime - startTime;

            // 创建字幕片段对象（支持动画关联，复制自稳定版逻辑）
            JSONObject segment = createTextSegmentObject(segmentId, textId, animationId, startTime, endTime, duration, caption, request, index, canvasWidth, canvasHeight);

            // 添加到轨道的segments数组
            com.alibaba.fastjson.JSONArray segments = track.getJSONArray("segments");
            if (segments == null) {
                segments = new com.alibaba.fastjson.JSONArray();
                track.put("segments", segments);
            }
            segments.add(segment);

            log.info("Pro版字幕片段添加成功[{}]: ID={}, 时间范围={}~{}", index, segmentId, startTime, endTime);
            return segmentId;

        } catch (Exception e) {
            log.error("Pro版添加字幕片段失败[{}]", index, e);
            throw new RuntimeException("添加字幕片段失败: " + e.getMessage(), e);
        }
    }

    /**
     * 性能优化：批量预加载所有需要的文字动画ID（完全复制自稳定版逻辑）
     */
    private java.util.Map<String, org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo> preloadTextAnimationIdsInternal(com.alibaba.fastjson.JSONArray captionsArray) {
        java.util.Map<String, org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo> animationCache = new java.util.HashMap<>();
        java.util.Set<String> animationNames = new java.util.HashSet<>();

        // 收集所有需要的动画名称
        for (int i = 0; i < captionsArray.size(); i++) {
            JSONObject caption = captionsArray.getJSONObject(i);

            String inAnimation = caption.getString("in_animation");
            String outAnimation = caption.getString("out_animation");
            String loopAnimation = caption.getString("loop_animation");

            // 处理入场动画（支持多个动画，用|分割，完全复制add_images_pro逻辑）
            if (inAnimation != null && !inAnimation.trim().isEmpty() && !"无".equals(inAnimation)) {
                addTextAnimationNamesFromString(animationNames, inAnimation, "in");
            }
            // 处理出场动画（支持多个动画，用|分割，完全复制add_images_pro逻辑）
            if (outAnimation != null && !outAnimation.trim().isEmpty() && !"无".equals(outAnimation)) {
                addTextAnimationNamesFromString(animationNames, outAnimation, "out");
            }
            // 处理循环动画（支持多个动画，用|分割，完全复制add_images_pro逻辑）
            if (loopAnimation != null && !loopAnimation.trim().isEmpty() && !"无".equals(loopAnimation)) {
                addTextAnimationNamesFromString(animationNames, loopAnimation, "loop");  // 文字动画使用loop，不是group
            }
        }

        // 批量查找动画ID
        for (String animationKey : animationNames) {
            String[] parts = animationKey.split(":");
            if (parts.length == 2) {
                String animationName = parts[0];
                String animationType = parts[1];

                try {
                    // 指定panel为"text"，调用文字动画接口
                    org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo animationInfo =
                        jianyingIdResolverService.findAnimationByName(animationName, animationType, "text");
                    if (animationInfo != null) {
                        animationCache.put(animationKey, animationInfo);
                        log.debug("Pro版文字动画ID预加载成功: {} -> {}", animationKey, animationInfo.getResourceId());
                    } else {
                        log.warn("Pro版文字动画ID预加载失败: {}", animationKey);
                    }
                } catch (Exception e) {
                    log.warn("Pro版预加载文字动画ID失败: {} - {}", animationKey, e.getMessage());
                }
            }
        }

        log.info("Pro版文字动画ID预加载完成，成功: {}/{}", animationCache.size(), animationNames.size());
        return animationCache;
    }

    /**
     * 从动画字符串中提取所有动画名称并添加到集合中（字幕动画专用，完全复制add_images_pro逻辑）
     */
    private void addTextAnimationNamesFromString(java.util.Set<String> animationNames, String animationStr, String animationType) {
        if (animationStr == null || animationStr.trim().isEmpty()) {
            return;
        }

        // 同时支持英文|和中文｜分隔符
        String[] animations;
        if (animationStr.contains("|")) {
            animations = animationStr.split("\\|");
        } else if (animationStr.contains("｜")) {
            animations = animationStr.split("｜");
        } else {
            // 没有分隔符，直接添加
            animationNames.add(animationStr.trim() + ":" + animationType);
            return;
        }

        // 添加所有有效的动画名称
        for (String animation : animations) {
            String trimmed = animation.trim();
            if (!trimmed.isEmpty()) {
                animationNames.add(trimmed + ":" + animationType);
            }
        }
    }

    /**
     * 按索引循环选择字幕动画（修复随机选择导致的不稳定问题）
     */
    private String selectTextAnimationByIndexInternal(String animationStr, int index) {
        if (animationStr == null || animationStr.trim().isEmpty()) {
            return null;
        }

        // 同时支持英文|和中文｜分隔符
        String[] animations;
        if (animationStr.contains("|")) {
            animations = animationStr.split("\\|");
        } else if (animationStr.contains("｜")) {
            animations = animationStr.split("｜");
        } else {
            // 没有分隔符，直接返回原字符串
            return animationStr.trim();
        }

        // 过滤空字符串
        java.util.List<String> validAnimations = java.util.Arrays.stream(animations)
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(java.util.stream.Collectors.toList());

        if (validAnimations.isEmpty()) {
            return null;
        }

        // 按索引循环选择动画（修复随机选择问题）
        int animationIndex = index % validAnimations.size();
        String selectedAnimation = validAnimations.get(animationIndex);

        log.debug("Pro版字幕按索引选择动画: 原始={}, 候选={}, 字幕索引={}, 动画索引={}, 选中={}",
                 animationStr, validAnimations, index, animationIndex, selectedAnimation);
        return selectedAnimation;
    }

    /**
     * 添加文字动画材料到materials.material_animations（完全复制自稳定版逻辑）
     */
    private String addTextAnimationMaterialInternal(JSONObject draft, JSONObject caption, int index,
                                           java.util.Map<String, org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo> animationCache) {
        try {
            log.debug("Pro版开始添加文字动画材料[{}]", index);

            // 生成动画材料ID
            String animationId = java.util.UUID.randomUUID().toString();

            // 获取动画信息（完全复制add_images_pro的随机选择逻辑）
            String inAnimation = caption.getString("in_animation");
            String outAnimation = caption.getString("out_animation");
            String loopAnimation = caption.getString("loop_animation");

            Long inAnimationDuration = caption.getLong("in_animation_duration");
            Long outAnimationDuration = caption.getLong("out_animation_duration");
            Long loopAnimationDuration = caption.getLong("loop_animation_duration");

            // 设置默认动画时长（微秒）
            if (inAnimationDuration == null) inAnimationDuration = 1000000L;
            if (outAnimationDuration == null) outAnimationDuration = 1000000L;
            if (loopAnimationDuration == null) loopAnimationDuration = 4000000L;

            // 创建动画材料对象
            JSONObject animationMaterial = new JSONObject();
            animationMaterial.put("id", animationId);
            animationMaterial.put("multi_language_current", "none");
            animationMaterial.put("type", "sticker_animation");

            com.alibaba.fastjson.JSONArray animations = new com.alibaba.fastjson.JSONArray();

            // 入场动画（按索引循环选择，修复随机选择问题）
            if (inAnimation != null && !inAnimation.trim().isEmpty() && !"无".equals(inAnimation)) {
                String selectedInAnimation = selectTextAnimationByIndexInternal(inAnimation, index);
                if (selectedInAnimation != null) {
                    JSONObject inAnimationObj = createTextAnimationObjectWithCacheInternal(selectedInAnimation, "in", inAnimationDuration, "入场", animationCache);
                    if (inAnimationObj != null) {
                        animations.add(inAnimationObj);
                    }
                }
            }

            // 出场动画（按索引循环选择，修复随机选择问题）
            if (outAnimation != null && !outAnimation.trim().isEmpty() && !"无".equals(outAnimation)) {
                String selectedOutAnimation = selectTextAnimationByIndexInternal(outAnimation, index);
                if (selectedOutAnimation != null) {
                    JSONObject outAnimationObj = createTextAnimationObjectWithCacheInternal(selectedOutAnimation, "out", outAnimationDuration, "出场", animationCache);
                    if (outAnimationObj != null) {
                        animations.add(outAnimationObj);
                    }
                }
            }

            // 循环动画（按索引循环选择，修复随机选择问题）
            if (loopAnimation != null && !loopAnimation.trim().isEmpty() && !"无".equals(loopAnimation)) {
                String selectedLoopAnimation = selectTextAnimationByIndexInternal(loopAnimation, index);
                if (selectedLoopAnimation != null) {
                    JSONObject loopAnimationObj = createTextAnimationObjectWithCacheInternal(selectedLoopAnimation, "loop", loopAnimationDuration, "循环", animationCache);
                    if (loopAnimationObj != null) {
                        animations.add(loopAnimationObj);
                    }
                }
            }

            animationMaterial.put("animations", animations);

            // 添加到materials.material_animations数组
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray materialAnimations = materials.getJSONArray("material_animations");
            materialAnimations.add(animationMaterial);

            // 显示实际选中的动画（按索引循环选择，确保一致性）
            String selectedInAnimation = (inAnimation != null && !inAnimation.trim().isEmpty() && !"无".equals(inAnimation)) ?
                selectTextAnimationByIndexInternal(inAnimation, index) : "无";
            String selectedOutAnimation = (outAnimation != null && !outAnimation.trim().isEmpty() && !"无".equals(outAnimation)) ?
                selectTextAnimationByIndexInternal(outAnimation, index) : "无";
            String selectedLoopAnimation = (loopAnimation != null && !loopAnimation.trim().isEmpty() && !"无".equals(loopAnimation)) ?
                selectTextAnimationByIndexInternal(loopAnimation, index) : "无";

            log.info("Pro版文字动画材料添加成功[{}]: ID={}, 入场={}({}ms), 出场={}({}ms), 循环={}({}ms)",
                    index, animationId,
                    selectedInAnimation, inAnimationDuration/1000,
                    selectedOutAnimation, outAnimationDuration/1000,
                    selectedLoopAnimation, loopAnimationDuration/1000);
            return animationId;

        } catch (Exception e) {
            log.error("Pro版添加文字动画材料失败[{}]", index, e);
            throw new RuntimeException("添加文字动画材料失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建文字动画对象，使用缓存的真实ID（完全复制自稳定版逻辑）
     */
    private JSONObject createTextAnimationObjectWithCacheInternal(String animationName, String animationType, long duration, String categoryName,
                                                         java.util.Map<String, org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo> animationCache) {
        try {
            log.debug("Pro版创建文字动画对象: name={}, type={}, duration={}", animationName, animationType, duration);

            JSONObject animation = new JSONObject();
            animation.put("anim_adjust_params", null);
            animation.put("category_id", "");
            animation.put("category_name", categoryName);
            animation.put("duration", duration);
            animation.put("material_type", "sticker");
            animation.put("name", animationName);
            animation.put("panel", "");
            animation.put("path", "");
            animation.put("platform", "all");
            animation.put("request_id", "");
            animation.put("start", 0);
            animation.put("type", animationType);
            animation.put("icon_url", "");

            // 从缓存中获取动画信息
            String cacheKey = animationName + ":" + animationType;
            org.jeecg.modules.jianying.service.JianyingIdResolverService.AnimationInfo animationInfo = animationCache.get(cacheKey);

            if (animationInfo != null) {
                // 使用缓存的真实ID
                animation.put("resource_id", animationInfo.getResourceId());
                animation.put("id", animationInfo.getId());
                animation.put("category_id", animationInfo.getCategoryId() != null ? animationInfo.getCategoryId() : "");
                animation.put("material_type", "sticker");
                log.debug("Pro版文字动画真实ID从缓存获取成功: {} -> resource_id: {}, id: {}",
                        animationName, animationInfo.getResourceId(), animationInfo.getId());
            } else {
                // 缓存中没有找到，使用默认值而不是抛出异常
                log.warn("Pro版文字动画真实ID在缓存中未找到，使用默认值: {}", animationName);
                animation.put("resource_id", "default_text_animation_" + animationType);
                animation.put("id", "default_text_animation_" + animationType);
                animation.put("category_id", "");
                animation.put("material_type", "sticker");
            }

            return animation;

        } catch (Exception e) {
            log.error("Pro版创建文字动画对象失败，使用默认动画: {}", animationName, e);
            // 即使出错也要返回一个基本的动画对象，确保字幕能正常显示
            JSONObject defaultAnimation = new JSONObject();
            defaultAnimation.put("anim_adjust_params", null);
            defaultAnimation.put("category_id", "");
            defaultAnimation.put("category_name", categoryName);
            defaultAnimation.put("duration", duration);
            defaultAnimation.put("material_type", "sticker");
            defaultAnimation.put("name", animationName);
            defaultAnimation.put("panel", "");
            defaultAnimation.put("path", "");
            defaultAnimation.put("platform", "all");
            defaultAnimation.put("request_id", "");
            defaultAnimation.put("start", 0);
            defaultAnimation.put("type", animationType);
            defaultAnimation.put("icon_url", "");
            defaultAnimation.put("resource_id", "default_text_animation_" + animationType);
            defaultAnimation.put("id", "default_text_animation_" + animationType);
            return defaultAnimation;
        }
    }

    /**
     * 创建字幕材料对象（专注keywords数组，移除单个keyword参数）
     */
    private JSONObject createTextMaterialObjectWithKeywords(String textMaterialId, String text, java.util.List<String> keywords, String keywordColor,
                                               Integer fontSize, Integer keywordFontSize, JianyingProAddCaptionsRequest request, int index) {
        JSONObject textMaterial = new JSONObject(new java.util.LinkedHashMap<>());

        // 基础信息
        textMaterial.put("id", textMaterialId);
        textMaterial.put("material_id", textMaterialId);
        textMaterial.put("material_name", text != null ? text : "");
        textMaterial.put("material_url", "");

        // 基本属性
        textMaterial.put("create_time", System.currentTimeMillis() * 1000);
        textMaterial.put("duration", 5000000L); // 默认5秒
        textMaterial.put("extra_info", "");
        textMaterial.put("file_Path", "");
        textMaterial.put("import_time", System.currentTimeMillis() * 1000);
        textMaterial.put("import_time_ms", System.currentTimeMillis());
        textMaterial.put("md5", "");

        // 修复：添加关键的check_flag属性（与稳定版一致）
        textMaterial.put("check_flag", 7);

        // 修复：使用字符串类型而不是数字（与稳定版一致）
        textMaterial.put("type", "subtitle");

        // 字幕特有属性
        Integer effectiveFontSize = fontSize != null ? fontSize : (request.getFontSize() != null ? request.getFontSize() : 15);
        textMaterial.put("font_size", effectiveFontSize);

        // 修复：使用动态查询的字体信息（完全复制稳定版逻辑）
        String fontName = request.getFont();
        JSONObject fontInfo = getFontInfoInternal(fontName);

        // 设置字体相关属性
        textMaterial.put("font_id", fontInfo.getString("id"));
        textMaterial.put("font_name", fontInfo.getString("name"));
        textMaterial.put("font_path", "/Applications/VideoFusion-macOS.app/Contents/Resources/Font/SystemFont/zh-hans.ttf");
        textMaterial.put("font_resource_id", "");
        textMaterial.put("font_source_platform", 0);
        textMaterial.put("font_team_id", "");
        textMaterial.put("font_title", "none");
        textMaterial.put("font_url", "");

        // 添加fonts数组（完全复制稳定版逻辑）
        com.alibaba.fastjson.JSONArray fonts = new com.alibaba.fastjson.JSONArray();
        if (fontInfo.getString("id") != null && !fontInfo.getString("id").isEmpty()) {
            JSONObject fontItem = new JSONObject();
            fontItem.put("effect_id", fontInfo.getString("id"));
            fontItem.put("id", fontInfo.getString("id"));
            fontItem.put("path", fontInfo.getString("path"));
            fontItem.put("resource_id", fontInfo.getString("id"));
            fontItem.put("title", fontInfo.getString("name"));
            fonts.add(fontItem);
        }
        textMaterial.put("fonts", fonts);

        // 添加path字段
        textMaterial.put("path", fontInfo.getString("path"));

        // ========== 修复：根据styleText参数控制内容格式（完全复制稳定版逻辑） ==========

        // fontName和fontInfo已在上面定义，直接使用

        // 获取样式参数
        String textColor = request.getTextColor() != null ? request.getTextColor() : "#FFFFFF";
        String borderColor = request.getBorderColor() != null ? request.getBorderColor() : "";
        Integer styleText = request.getStyleText() != null ? request.getStyleText() : 0;
        Integer effectiveKeywordFontSize = keywordFontSize != null ? keywordFontSize : (effectiveFontSize + 5);

        // 根据styleText参数决定内容格式
        Object contentValue;
        if (styleText == 1) {
            // 富文本样式：使用简单文本字符串
            contentValue = text != null ? text : "";
            log.info("使用富文本样式(style_text=1): 简单文本格式");
        } else {
            // 默认样式：使用复杂JSON结构（专注keywords数组支持）
            contentValue = generateRichTextContentWithKeywords(text != null ? text : "", keywords, keywordColor, effectiveFontSize, effectiveKeywordFontSize, textColor, borderColor, fontInfo);
            log.info("使用默认样式(style_text=0): 复杂JSON格式，关键词数量: {}", (keywords != null ? keywords.size() : 0));
        }

        textMaterial.put("content", contentValue);

        // 颜色设置（使用已定义的变量）
        textMaterial.put("text_color", textColor);

        // ========== 修复：添加缺失的字幕样式参数处理（完全复制稳定版逻辑） ==========

        // 1. 边框颜色处理（使用已定义的变量）
        textMaterial.put("border_color", borderColor);

        // 2. 对齐方式处理（完全复制稳定版的0-5映射逻辑）
        Integer inputAlignment = request.getAlignment() != null ? request.getAlignment() : 1;
        int alignment = 1; // 默认居中
        int typesetting = 0; // 默认横排

        switch (inputAlignment) {
            case 0: alignment = 0; typesetting = 0; break; // 左对齐
            case 1: alignment = 1; typesetting = 0; break; // 居中对齐
            case 2: alignment = 2; typesetting = 0; break; // 右对齐
            case 3: alignment = 3; typesetting = 1; break; // 竖排居顶
            case 4: alignment = 1; typesetting = 1; break; // 竖排居中
            case 5: alignment = 4; typesetting = 1; break; // 竖排居底
            default: alignment = 1; typesetting = 0; break; // 默认居中
        }
        textMaterial.put("alignment", alignment);
        textMaterial.put("typesetting", typesetting);

        // 3. 间距处理（完全复制稳定版的归一化逻辑）
        Double letterSpacing = request.getLetterSpacing() != null ? request.getLetterSpacing() / 20.0 : 0.05;
        Double lineSpacing = request.getLineSpacing() != null ? request.getLineSpacing() / 20.0 : 0.05;
        textMaterial.put("letter_spacing", letterSpacing);
        textMaterial.put("line_spacing", lineSpacing);

        // 4. 文本样式处理（使用已定义的变量）
        textMaterial.put("style_text", styleText);

        // 关键词设置（专注keywords数组）
        if (keywords != null && !keywords.isEmpty()) {
            textMaterial.put("keywords", keywords);
            textMaterial.put("keyword_color", keywordColor != null ? keywordColor : "#FF0000");
            textMaterial.put("keyword_font_size", keywordFontSize != null ? keywordFontSize : (fontSize != null ? fontSize + 5 : 20));
        }

        // 修复：使用正确的请求参数而不是随意的默认值

        // 使用请求参数中的透明度（与稳定版逻辑一致）
        textMaterial.put("global_alpha", request.getAlpha() != null ? request.getAlpha() : 1.0);

        // 使用请求参数中的文字颜色（与稳定版逻辑一致）
        String effectiveTextColor = request.getTextColor() != null ? request.getTextColor() : "";
        textMaterial.put("text_color", effectiveTextColor);

        // 使用请求参数中的边框颜色（与稳定版逻辑一致）
        String effectiveBorderColor = request.getBorderColor() != null ? request.getBorderColor() : "";
        textMaterial.put("border_color", effectiveBorderColor);

        // 使用请求参数中的对齐方式（与稳定版逻辑一致）
        Integer effectiveAlignment = request.getAlignment() != null ? request.getAlignment() : 1;
        textMaterial.put("alignment", effectiveAlignment);

        // 使用请求参数中的行间距（与稳定版逻辑一致）
        Double effectiveLineSpacing = request.getLineSpacing() != null ? request.getLineSpacing() / 20.0 : 0.05;
        textMaterial.put("line_spacing", effectiveLineSpacing);

        // 使用请求参数中的字符间距（与稳定版逻辑一致）
        Double effectiveLetterSpacing = request.getLetterSpacing() != null ? request.getLetterSpacing() / 20.0 : 0.05;
        textMaterial.put("letter_spacing", effectiveLetterSpacing);

        // 固定的text_size（与稳定版一致）
        textMaterial.put("text_size", 30);

        // 其他必要的基础属性（只添加真正必要的）
        textMaterial.put("is_rich_text", true);
        textMaterial.put("multi_language_current", "none");

        // 词语时间信息
        JSONObject words = new JSONObject();
        words.put("end_time", new com.alibaba.fastjson.JSONArray());
        words.put("start_time", new com.alibaba.fastjson.JSONArray());
        words.put("text", new com.alibaba.fastjson.JSONArray());
        textMaterial.put("words", words);

        return textMaterial;
    }

    /**
     * 动态查询字体信息（完全复制稳定版逻辑）
     */
    private JSONObject getFontInfoInternal(String fontName) {
        JSONObject fontInfo = new JSONObject();

        if (fontName == null || fontName.trim().isEmpty()) {
            // 返回默认字体信息
            fontInfo.put("id", "");
            fontInfo.put("name", "");
            fontInfo.put("path", "");
            return fontInfo;
        }

        try {
            log.info("Pro版开始查询字体信息: {}", fontName);

            // 构建查询URL（完全复制稳定版）
            String baseUrl = "https://lv-api-sinfonlinec.ulikecam.com/artist/v1/effect/search";
            String queryParams = "?aid=3704&opengl_version=3.3&cpu=12th%20Gen%20Intel(R)%20Core(TM)%20i5-12600KF&version_name=5.9.0&device_platform=windows&biz_id=2";
            String fullUrl = baseUrl + queryParams;

            // 构建请求体（完全复制稳定版）
            JSONObject requestBody = new JSONObject();
            requestBody.put("keyword", fontName);
            requestBody.put("cursor", 0);
            requestBody.put("count", 20);
            requestBody.put("panel", "text");

            JSONObject searchOption = new JSONObject();
            searchOption.put("filter_uncommercial", false);
            searchOption.put("scene", "");
            searchOption.put("sticker_type", 0);
            requestBody.put("search_option", searchOption);

            log.info("Pro版字体查询请求URL: {}", fullUrl);
            log.info("Pro版字体查询请求体: {}", requestBody.toJSONString());

            // 发送HTTP请求
            String response = sendHttpRequestInternal(fullUrl, requestBody.toJSONString());
            log.info("Pro版字体查询响应: {}", response);

            // 解析响应（完全复制稳定版）
            JSONObject responseJson = JSONObject.parseObject(response);
            if (responseJson != null && responseJson.containsKey("data")) {
                JSONObject data = responseJson.getJSONObject("data");
                if (data != null && data.containsKey("effect_item_list")) {
                    com.alibaba.fastjson.JSONArray effectList = data.getJSONArray("effect_item_list");
                    if (effectList != null && effectList.size() > 0) {
                        JSONObject firstEffect = effectList.getJSONObject(0);
                        if (firstEffect != null && firstEffect.containsKey("common_attr")) {
                            JSONObject commonAttr = firstEffect.getJSONObject("common_attr");
                            String fontId = commonAttr.getString("id");
                            String fontTitle = commonAttr.getString("title");

                            fontInfo.put("id", fontId != null ? fontId : "");
                            fontInfo.put("name", fontTitle != null ? fontTitle : fontName);
                            fontInfo.put("path", fontTitle != null ? fontTitle + ".ttf" : fontName + ".ttf");

                            log.info("Pro版字体查询成功: id={}, name={}, path={}", fontId, fontTitle, fontInfo.getString("path"));
                            return fontInfo;
                        }
                    }
                }
            }

            log.warn("Pro版字体查询未找到结果，使用默认字体: {}", fontName);

        } catch (Exception e) {
            log.error("Pro版字体查询失败: {}", fontName, e);
        }

        // 查询失败时返回默认字体信息
        fontInfo.put("id", "");
        fontInfo.put("name", "");
        fontInfo.put("path", "");
        return fontInfo;
    }

    /**
     * 发送HTTP请求（完全复制稳定版逻辑）
     */
    private String sendHttpRequestInternal(String url, String requestBody) {
        try {
            java.net.URL urlObj = new java.net.URL(url);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) urlObj.openConnection();

            // 设置请求方法和头部
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            connection.setDoOutput(true);

            // 发送请求体
            try (java.io.OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            // 读取响应
            int responseCode = connection.getResponseCode();
            java.io.InputStream inputStream = responseCode >= 200 && responseCode < 300 ?
                    connection.getInputStream() : connection.getErrorStream();

            StringBuilder response = new StringBuilder();
            try (java.io.BufferedReader br = new java.io.BufferedReader(
                    new java.io.InputStreamReader(inputStream, "utf-8"))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
            }

            return response.toString();

        } catch (Exception e) {
            log.error("Pro版HTTP请求失败: {}", url, e);
            return "{}";
        }
    }

    /**
     * 创建字幕片段对象（支持动画关联，完全复制自稳定版addTextSegment逻辑）
     */
    private JSONObject createTextSegmentObject(String segmentId, String textId, String animationId, long startTime, long endTime, long duration,
                                             JSONObject caption, JianyingProAddCaptionsRequest request, int index, int canvasWidth, int canvasHeight) {
        JSONObject segment = new JSONObject(new java.util.LinkedHashMap<>());

        // 获取用户参数（支持坐标转换）
        Double scaleX = request.getScaleX();
        Double scaleY = request.getScaleY();
        double transformX = request.getTransformX() != null ? request.getTransformX() : 0.0;
        double transformY = request.getTransformY() != null ? request.getTransformY() : 0.0;

        // 坐标转换：像素值转归一化坐标（完全复制自稳定版）
        final double normalizedX = transformX / (double)canvasWidth;
        final double normalizedY = transformY / (double)canvasHeight;

        // 基础字段（完全复制自稳定版）
        segment.put("caption_info", null);
        segment.put("cartoon", false);

        // 片段剪辑信息（完全复制自稳定版clip结构）
        segment.put("clip", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("alpha", request.getAlpha() != null ? request.getAlpha() : 1);
            put("flip", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("horizontal", false);
                put("vertical", false);
            }});
            put("rotation", 0);
            put("scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", scaleX != null ? scaleX : 1);
                put("y", scaleY != null ? scaleY : 1);
            }});
            put("transform", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", normalizedX);
                put("y", normalizedY);
            }});
        }});

        // 其他标准字段（完全复制自稳定版）
        segment.put("common_keyframes", new com.alibaba.fastjson.JSONArray());
        segment.put("enable_adjust", true);
        segment.put("enable_color_correct_adjust", false);
        segment.put("enable_color_curves", true);
        segment.put("enable_color_match_adjust", false);
        segment.put("enable_color_wheels", true);
        segment.put("enable_lut", true);
        segment.put("enable_smart_color_adjust", false);

        // 关联动画材料（完全复制自稳定版）
        com.alibaba.fastjson.JSONArray extraMaterialRefs = new com.alibaba.fastjson.JSONArray();
        if (animationId != null) {
            extraMaterialRefs.add(animationId);
            log.debug("Pro版字幕段[{}]关联动画材料: {}", index, animationId);
        }
        segment.put("extra_material_refs", extraMaterialRefs);

        segment.put("group_id", "");

        // HDR设置（完全复制自稳定版）
        segment.put("hdr_settings", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("intensity", 1);
            put("mode", 1);
            put("nits", 1000);
        }});

        segment.put("id", segmentId);
        segment.put("intensifies_audio", false);
        segment.put("is_placeholder", false);
        segment.put("is_tone_modify", false);
        segment.put("keyframe_refs", new com.alibaba.fastjson.JSONArray());
        segment.put("last_nonzero_volume", 1);
        segment.put("material_id", textId);
        segment.put("render_index", 1); // 字幕使用render_index=1
        segment.put("reverse", false);
        segment.put("source_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", 0L);
        }});
        segment.put("speed", 1.0);
        segment.put("target_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", startTime);
        }});
        segment.put("template_id", "");
        segment.put("template_scene", "default");
        segment.put("track_attribute", 0);
        segment.put("track_render_index", 3); // 修复：使用稳定版的正确值

        // 修复：补全uniform_scale字段内容（完全复制稳定版逻辑）
        segment.put("uniform_scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("on", true);
            put("value", 1);
        }});

        // 响应式布局（完全复制自稳定版）
        segment.put("responsive_layout", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("enable", false);
            put("horizontal_pos_layout", 0);
            put("size_layout", 0);
            put("target_follow", "");
            put("vertical_pos_layout", 0);
        }});

        segment.put("visible", true);
        segment.put("volume", 1.0);

        return segment;
    }

    // ========== Pro版特效处理方法（复制自稳定版完整逻辑） ==========

    /**
     * JSON字符串转义处理
     */
    private String escapeJsonString(String str) {
        if (str == null) return "";
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }

    /**
     * Pro版内部生成特效信息（复制自稳定版effect_infos的完整处理逻辑）
     * 实现真正的代码隔离，不依赖稳定版Service
     */
    private String generateEffectInfosInternal(JianyingProAddEffectsRequest request) {
        try {
            log.info("Pro版开始生成特效信息，特效数量: {}", request.getEffects().size());

            // 参数处理（复制自稳定版逻辑）
            java.util.List<String> effects = request.getEffects();
            java.util.List<JSONObject> timelines = request.getTimelines();

            // 参数验证（复制自稳定版逻辑）
            if (effects == null || effects.isEmpty()) {
                throw new RuntimeException("特效列表不能为空");
            }
            if (timelines == null || timelines.isEmpty()) {
                throw new RuntimeException("时间线列表不能为空");
            }

            log.info("Pro版特效参数 - 特效数量: {}, 时间线数量: {}",
                    effects.size(), timelines.size());

            // 生成特效信息JSON字符串（复制自稳定版的完整逻辑）
            StringBuilder jsonBuilder = new StringBuilder("[");
            for (int i = 0; i < timelines.size(); i++) {
                JSONObject timeline = timelines.get(i);

                // 特效分配策略：按索引分配，超出范围则为空字符串（与imgs_infos、video_infos一致）
                String effectTitle = (i < effects.size()) ? effects.get(i) : "";

                if (i > 0) jsonBuilder.append(",");

                // 直接拼接JSON字符串，保证字段顺序：effect_title → start → end → 可选字段
                jsonBuilder.append("{");

                // 添加特效标题（如果有）
                if (!effectTitle.isEmpty()) {
                    jsonBuilder.append("\"effect_title\":\"")
                               .append(escapeJsonString(effectTitle))
                               .append("\",");
                }

                // 添加时间信息（字段顺序：start → end）
                Long startTime = timeline.getLong("start");
                Long endTime = timeline.getLong("end");
                if (startTime == null || endTime == null) {
                    log.warn("Pro版时间线对象缺少start或end字段，使用默认值: {}", timeline);
                    startTime = startTime != null ? startTime : i * 5000000L;
                    endTime = endTime != null ? endTime : (i + 1) * 5000000L;
                }
                jsonBuilder.append("\"start\":")
                           .append(startTime)
                           .append(",\"end\":")
                           .append(endTime);



                jsonBuilder.append("}");
            }
            jsonBuilder.append("]");

            log.info("Pro版特效数据生成完成 - 生成了{}个特效对象", timelines.size());
            log.debug("Pro版生成的特效数据: {}", jsonBuilder.toString());

            return jsonBuilder.toString();

        } catch (Exception e) {
            log.error("Pro版生成特效信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成特效信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * Pro版内部添加特效（复制自稳定版add_effects的完整处理逻辑）
     * 实现真正的代码隔离，不依赖稳定版Service
     */
    private JSONObject addEffectsInternal(String draftUrl, String effectInfosJson, JianyingProAddEffectsRequest request) {
        try {
            log.info("Pro版开始添加特效到草稿，特效数量: {}", request.getEffects().size());

            // 第1步：下载并解析草稿文件（复制自稳定版逻辑）
            JSONObject draft = downloadAndParseDraft(draftUrl);
            if (draft == null) {
                return createErrorResult("草稿文件下载失败", "PRO_DRAFT_DOWNLOAD_ERROR");
            }

            // 第2步：解析特效信息数组（复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray effectsArray;
            try {
                effectsArray = com.alibaba.fastjson.JSONArray.parseArray(effectInfosJson);
                if (effectsArray == null || effectsArray.isEmpty()) {
                    return createErrorResult("特效信息解析失败或为空", "PRO_EFFECT_INFOS_PARSE_ERROR");
                }
            } catch (Exception e) {
                log.error("Pro版解析特效信息失败: {}", e.getMessage());
                return createErrorResult("特效信息JSON格式错误: " + e.getMessage(), "PRO_EFFECT_INFOS_FORMAT_ERROR");
            }

            // 第3步：处理特效添加逻辑（复制自稳定版逻辑）
            JSONObject result = processEffectAddition(draft, effectsArray, draftUrl, request);
            if (result.containsKey("error")) {
                return result;
            }

            // 第4步：保存草稿文件（复制自稳定版逻辑）
            boolean saveSuccess = saveDraftFile(draftUrl, draft);
            if (!saveSuccess) {
                return createErrorResult("草稿文件保存失败", "PRO_DRAFT_SAVE_ERROR");
            }

            log.info("Pro版特效添加完成，草稿已保存: {}", draftUrl);
            return result;

        } catch (Exception e) {
            log.error("Pro版添加特效失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", "Pro版添加特效失败: " + e.getMessage());
            errorResult.put("error_code", "PRO_ADD_EFFECTS_ERROR");
            return errorResult;
        }
    }

    /**
     * 处理特效添加逻辑（复制自稳定版逻辑）
     */
    private JSONObject processEffectAddition(JSONObject draft, com.alibaba.fastjson.JSONArray effectsArray, String draftUrl, JianyingProAddEffectsRequest request) {
        try {
            log.info("Pro版处理特效添加逻辑，特效数量: {}", effectsArray.size());

            // 第1步：确保materials对象存在且有正确的结构（复制自稳定版逻辑）
            JSONObject materials = draft.getJSONObject("materials");
            if (materials == null) {
                materials = new JSONObject(new java.util.LinkedHashMap<>());
                draft.put("materials", materials);
            }

            // 确保materials.video_effects数组存在
            com.alibaba.fastjson.JSONArray videoEffects = materials.getJSONArray("video_effects");
            if (videoEffects == null) {
                videoEffects = new com.alibaba.fastjson.JSONArray();
                materials.put("video_effects", videoEffects);
            }

            // 第2步：确保tracks数组存在（复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray tracks = draft.getJSONArray("tracks");
            if (tracks == null) {
                tracks = new com.alibaba.fastjson.JSONArray();
                draft.put("tracks", tracks);
            }

            // 第3步：批量搜索特效信息（完全复制自稳定版逻辑）
            java.util.Set<String> uniqueEffectTitles = new java.util.HashSet<>();
            for (int i = 0; i < effectsArray.size(); i++) {
                JSONObject effectInfo = effectsArray.getJSONObject(i);
                String effectTitle = effectInfo.getString("effect_title");
                if (effectTitle != null && !effectTitle.trim().isEmpty()) {
                    uniqueEffectTitles.add(effectTitle.trim());
                }
            }

            // 批量搜索特效信息
            java.util.Map<String, EffectInfo> effectMapping = new java.util.HashMap<>();
            java.util.Set<String> failedEffects = new java.util.HashSet<>();

            for (String effectTitle : uniqueEffectTitles) {
                try {
                    EffectInfo effectInfo = effectSearchService.searchEffect(effectTitle);
                    if (effectInfo != null) {
                        effectMapping.put(effectTitle, effectInfo);
                        log.info("Pro版成功获取特效信息: {} -> effect_id: {}, resource_id: {}",
                                effectTitle, effectInfo.getEffectId(), effectInfo.getResourceId());
                    } else {
                        failedEffects.add(effectTitle);
                        log.warn("Pro版未找到特效: {}，将跳过该特效", effectTitle);
                    }
                } catch (Exception e) {
                    failedEffects.add(effectTitle);
                    log.error("Pro版搜索特效失败: {}，将跳过该特效", effectTitle, e);
                }
            }

            // 记录搜索结果统计
            log.info("Pro版特效搜索完成，成功: {}, 失败: {}", effectMapping.size(), failedEffects.size());
            if (!failedEffects.isEmpty()) {
                log.warn("Pro版失败的特效列表: {}", failedEffects);
            }

            // 验证是否有有效的特效
            if (effectMapping.isEmpty()) {
                return createErrorResult("没有找到任何有效的特效，请检查特效名称是否正确", "PRO_EFFECT_NOT_FOUND");
            }

            // 第4步：创建新的特效轨道（复制自稳定版逻辑）
            String newTrackId = java.util.UUID.randomUUID().toString().toUpperCase();
            JSONObject newTrack = createEffectTrack(newTrackId);
            tracks.add(newTrack);

            // 第5步：处理每个特效，添加材料和片段（使用真实特效信息）
            java.util.List<String> effectIds = new java.util.ArrayList<>();
            java.util.List<String> segmentIds = new java.util.ArrayList<>();
            java.util.List<JSONObject> segmentTimes = new java.util.ArrayList<>();

            for (int i = 0; i < effectsArray.size(); i++) {
                JSONObject effect = effectsArray.getJSONObject(i);
                try {
                    String effectTitle = effect.getString("effect_title");

                    // 检查特效是否存在于映射中
                    if (effectTitle == null || !effectMapping.containsKey(effectTitle)) {
                        log.warn("Pro版跳过无效特效[{}]: {}", i, effectTitle);
                        effectIds.add(null);
                        segmentIds.add(null);
                        // 添加占位符时间信息
                        JSONObject timeInfo = new JSONObject();
                        timeInfo.put("start", i * 5000000L);
                        timeInfo.put("end", (i + 1) * 5000000L);
                        segmentTimes.add(timeInfo);
                        continue;
                    }

                    // 获取真实特效信息
                    EffectInfo realEffectInfo = effectMapping.get(effectTitle);

                    // 添加特效材料到materials.video_effects（使用真实特效信息）
                    String effectId = addEffectMaterialInternalWithRealInfo(draft, effect, realEffectInfo, i);
                    effectIds.add(effectId);

                    // 添加特效片段到轨道
                    String segmentId = addEffectSegmentInternal(newTrack, effect, effectId, request, i);
                    segmentIds.add(segmentId);

                    // 保存时间信息用于生成segment_infos
                    JSONObject timeInfo = new JSONObject();
                    long effectStart = effect.getLong("start") != null ? effect.getLong("start") : i * 5000000L;
                    long effectEnd = effect.getLong("end") != null ? effect.getLong("end") : (i + 1) * 5000000L;
                    timeInfo.put("start", effectStart);
                    timeInfo.put("end", effectEnd);
                    segmentTimes.add(timeInfo);

                    log.info("Pro版特效处理成功[{}]: effectId={}, segmentId={}", i, effectId, segmentId);

                } catch (Exception e) {
                    log.error("Pro版处理特效失败[{}]: {}", i, effect.getString("effect_title"), e);
                    // 创建占位符ID保持数组长度一致
                    String placeholderEffectId = "placeholder_effect_" + i;
                    String placeholderSegmentId = "placeholder_segment_" + i;
                    effectIds.add(placeholderEffectId);
                    segmentIds.add(placeholderSegmentId);
                    JSONObject placeholderTime = new JSONObject();
                    placeholderTime.put("start", i * 5000000L);
                    placeholderTime.put("end", (i + 1) * 5000000L);
                    segmentTimes.add(placeholderTime);
                }
            }

            // 第5步：返回处理结果（完全复制自稳定版标准格式）
            JSONObject result = new JSONObject(new java.util.LinkedHashMap<>());
            result.put("success", true); // 添加success字段以保持一致性
            result.put("effect_ids", effectIds);
            result.put("draft_url", draftUrl);
            result.put("segment_ids", segmentIds);
            result.put("track_id", newTrackId);

            // 添加警告信息（如果有）- 暂时为空，后续可扩展
            java.util.List<String> warnings = new java.util.ArrayList<>();
            if (!warnings.isEmpty()) {
                result.put("warnings", warnings);
            }

            log.info("Pro版特效添加逻辑处理完成，轨道ID: {}, 特效数量: {}", newTrackId, effectIds.size());
            return result;

        } catch (Exception e) {
            log.error("Pro版处理特效添加失败: {}", e.getMessage());
            return createErrorResult("特效添加处理失败: " + e.getMessage(), "PRO_EFFECT_PROCESS_ERROR");
        }
    }

    // ========== Pro版特效处理辅助方法（复制自稳定版完整逻辑） ==========

    /**
     * 创建特效轨道（复制自稳定版逻辑）
     */
    private JSONObject createEffectTrack(String trackId) {
        JSONObject track = new JSONObject(new java.util.LinkedHashMap<>());
        track.put("attribute", 0);
        track.put("flag", 0);
        track.put("id", trackId);
        track.put("is_default_name", true);
        track.put("name", "");
        track.put("segments", new com.alibaba.fastjson.JSONArray());
        track.put("type", "effect");
        return track;
    }

    /**
     * 添加特效材料到materials.video_effects（使用真实特效信息，完全复制自稳定版逻辑）
     */
    private String addEffectMaterialInternalWithRealInfo(JSONObject draft, JSONObject effect, EffectInfo realEffectInfo, int index) {
        try {
            String effectTitle = effect.getString("effect_title");
            log.info("Pro版开始添加特效材料[{}]: {} -> 真实ID: {}", index, effectTitle, realEffectInfo.getEffectId());

            // 生成特效材料ID
            String materialId = java.util.UUID.randomUUID().toString();

            // 创建完整的特效材料对象（完全复制自稳定版）
            JSONObject videoEffect = new JSONObject();
            videoEffect.put("id", materialId);
            videoEffect.put("name", realEffectInfo.getName());
            videoEffect.put("effect_id", realEffectInfo.getEffectId()); // 使用真实特效ID
            videoEffect.put("resource_id", realEffectInfo.getResourceId()); // 使用真实资源ID
            videoEffect.put("type", "video_effect");
            videoEffect.put("path", "");
            videoEffect.put("platform", "all");
            videoEffect.put("version", "");
            videoEffect.put("category_id", "");
            videoEffect.put("category_name", "");
            videoEffect.put("source_platform", 0);
            videoEffect.put("apply_target_type", 2);
            videoEffect.put("value", 1);
            videoEffect.put("render_index", 0);
            videoEffect.put("track_render_index", 0);
            videoEffect.put("formula_id", "");
            videoEffect.put("request_id", "");
            videoEffect.put("algorithm_artifact_path", "");
            videoEffect.put("common_keyframes", new com.alibaba.fastjson.JSONArray());
            videoEffect.put("disable_effect_faces", new com.alibaba.fastjson.JSONArray());
            videoEffect.put("apply_time_range", null);
            videoEffect.put("time_range", null);

            // 添加特效调整参数（完全复制自稳定版）
            com.alibaba.fastjson.JSONArray adjustParams = new com.alibaba.fastjson.JSONArray();

            // 亮度调整参数
            JSONObject luminanceParam = new JSONObject();
            luminanceParam.put("name", "effects_adjust_luminance");
            luminanceParam.put("default_value", 0.8);
            luminanceParam.put("value", 0.8);
            adjustParams.add(luminanceParam);

            // 模糊调整参数
            JSONObject blurParam = new JSONObject();
            blurParam.put("name", "effects_adjust_blur");
            blurParam.put("default_value", 0.7);
            blurParam.put("value", 0.7);
            adjustParams.add(blurParam);

            // 滤镜调整参数
            JSONObject filterParam = new JSONObject();
            filterParam.put("name", "effects_adjust_filter");
            filterParam.put("default_value", 0.5);
            filterParam.put("value", 0.5);
            adjustParams.add(filterParam);

            // 速度调整参数
            JSONObject speedParam = new JSONObject();
            speedParam.put("name", "effects_adjust_speed");
            speedParam.put("default_value", 0.1);
            speedParam.put("value", 0.1);
            adjustParams.add(speedParam);

            // 大小调整参数
            JSONObject sizeParam = new JSONObject();
            sizeParam.put("name", "effects_adjust_size");
            sizeParam.put("default_value", 0);
            sizeParam.put("value", 0);
            adjustParams.add(sizeParam);

            videoEffect.put("adjust_params", adjustParams);

            // 添加到materials.video_effects数组
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray videoEffects = materials.getJSONArray("video_effects");
            videoEffects.add(videoEffect);

            log.info("Pro版特效材料添加成功[{}]: ID={}, 真实特效ID={}, 真实资源ID={}",
                    index, materialId, realEffectInfo.getEffectId(), realEffectInfo.getResourceId());
            return materialId;

        } catch (Exception e) {
            log.error("Pro版添加特效材料失败[{}]", index, e);
            throw new RuntimeException("添加特效材料失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加特效材料到materials.video_effects（复制自稳定版逻辑）
     */
    private String addEffectMaterialInternal(JSONObject draft, JSONObject effect, JianyingProAddEffectsRequest request, int index) {
        try {
            String effectTitle = effect.getString("effect_title");
            log.info("Pro版开始添加特效材料[{}]: {}", index, effectTitle);

            // 生成特效材料ID
            String effectMaterialId = java.util.UUID.randomUUID().toString();

            // 创建特效材料对象（复制自稳定版逻辑）
            JSONObject effectMaterial = createEffectMaterialObject(effectMaterialId, effectTitle, effect, request, index);

            // 添加到materials.video_effects数组
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray videoEffects = materials.getJSONArray("video_effects");
            videoEffects.add(effectMaterial);

            log.info("Pro版特效材料添加成功[{}]: ID={}, 特效={}", index, effectMaterialId, effectTitle);
            return effectMaterialId;

        } catch (Exception e) {
            log.error("Pro版添加特效材料失败[{}]", index, e);
            throw new RuntimeException("添加特效材料失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加特效片段到轨道（复制自稳定版逻辑）
     */
    private String addEffectSegmentInternal(JSONObject track, JSONObject effect, String effectId, JianyingProAddEffectsRequest request, int index) {
        try {
            log.debug("Pro版开始添加特效片段[{}]: effectId={}", index, effectId);

            // 生成片段ID
            String segmentId = java.util.UUID.randomUUID().toString();

            // 使用effect中的实际时间范围
            long startTime = effect.getLong("start") != null ? effect.getLong("start") : index * 5000000L;
            long endTime = effect.getLong("end") != null ? effect.getLong("end") : (index + 1) * 5000000L;
            long duration = endTime - startTime;

            // 创建特效片段对象（复制自稳定版逻辑）
            JSONObject segment = createEffectSegmentObject(segmentId, effectId, startTime, endTime, duration, effect, request, index);

            // 添加到轨道的segments数组
            com.alibaba.fastjson.JSONArray segments = track.getJSONArray("segments");
            if (segments == null) {
                segments = new com.alibaba.fastjson.JSONArray();
                track.put("segments", segments);
            }
            segments.add(segment);

            log.info("Pro版特效片段添加成功[{}]: ID={}, 时间范围={}~{}", index, segmentId, startTime, endTime);
            return segmentId;

        } catch (Exception e) {
            log.error("Pro版添加特效片段失败[{}]", index, e);
            throw new RuntimeException("添加特效片段失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建特效材料对象（复制自稳定版逻辑）
     */
    private JSONObject createEffectMaterialObject(String effectMaterialId, String effectTitle, JSONObject effect, JianyingProAddEffectsRequest request, int index) {
        JSONObject effectMaterial = new JSONObject(new java.util.LinkedHashMap<>());

        // 基础信息
        effectMaterial.put("id", effectMaterialId);
        effectMaterial.put("name", effectTitle != null ? effectTitle : "特效" + (index + 1));
        effectMaterial.put("type", "video_effect");
        effectMaterial.put("path", "");
        effectMaterial.put("platform", "all");
        effectMaterial.put("version", "");
        effectMaterial.put("category_id", "");
        effectMaterial.put("category_name", "");
        effectMaterial.put("source_platform", 0);
        effectMaterial.put("apply_target_type", 2);
        effectMaterial.put("value", 1);
        effectMaterial.put("render_index", 0);
        effectMaterial.put("track_render_index", 0);

        // 特效特有属性（使用默认值）
        effectMaterial.put("intensity", 1.0);

        // 使用默认的特效ID和资源ID（实际应用中可以通过特效搜索API获取真实ID）
        effectMaterial.put("effect_id", "default_effect_" + effectMaterialId);
        effectMaterial.put("resource_id", "default_resource_" + effectMaterialId);

        return effectMaterial;
    }

    /**
     * 创建特效片段对象（复制自稳定版逻辑）
     */
    private JSONObject createEffectSegmentObject(String segmentId, String effectId, long startTime, long endTime, long duration, JSONObject effect, JianyingProAddEffectsRequest request, int index) {
        JSONObject segment = new JSONObject(new java.util.LinkedHashMap<>());

        // 基础信息
        segment.put("id", segmentId);
        segment.put("material_id", effectId);
        segment.put("target_timerange", new JSONObject() {{
            put("duration", duration);
            put("start", startTime);
        }});
        segment.put("source_timerange", new JSONObject() {{
            put("duration", duration);
            put("start", 0L);
        }});

        // 特效属性
        segment.put("track_render_index", 3);
        segment.put("render_index", 1);
        segment.put("visible", true);
        segment.put("enable_adjust", true);
        segment.put("enable_color_correct_adjust", false);
        segment.put("enable_color_curves", true);
        segment.put("enable_color_match_adjust", false);
        segment.put("enable_color_wheels", true);
        segment.put("enable_lut", true);
        segment.put("enable_smart_color_adjust", false);
        segment.put("intensifies_audio", false);
        segment.put("is_placeholder", false);
        segment.put("is_tone_modify", false);
        segment.put("last_nonzero_volume", 1);
        segment.put("reverse", false);

        // 透明度设置（使用默认值）
        segment.put("alpha", 1.0);

        return segment;
    }

    // ========== Pro版关键帧处理方法（复制自稳定版完整逻辑） ==========

    /**
     * Pro版内部生成关键帧信息（复制自稳定版keyframes_infos的完整处理逻辑）
     * 实现真正的代码隔离，不依赖稳定版Service
     */
    private String generateKeyframeInfosInternal(JianyingProAddKeyframesRequest request) {
        try {
            log.info("Pro版开始生成关键帧数据，类型: {}, 段数量: {}",
                    request.getCtype(), request.getSegmentInfos().size());

            // 参数处理（复制自稳定版逻辑）
            String ctype = request.getCtype().trim();
            String offsets = request.getOffsets().trim();
            java.util.List<JSONObject> segmentInfos = request.getSegmentInfos();
            String values = request.getValues();
            // 保留全局 width/height 参数作为后备（向后兼容）
            Integer globalWidth = request.getWidth() != null ? request.getWidth() : 1920;
            Integer globalHeight = request.getHeight() != null ? request.getHeight() : 1080;

            // 获取Canvas尺寸作为归一化基准（修复坐标显示问题）
            JSONObject draft = downloadAndParseDraft(request.getDraftUrl());
            JSONObject canvasConfig = draft.getJSONObject("canvas_config");
            int canvasWidth = canvasConfig != null ? canvasConfig.getIntValue("width") : 1920;
            int canvasHeight = canvasConfig != null ? canvasConfig.getIntValue("height") : 1080;
            log.info("Pro版关键帧归一化基准 - Canvas尺寸: {}x{}", canvasWidth, canvasHeight);

            // 参数验证（完全复制自稳定版逻辑）
            if (ctype.isEmpty()) {
                throw new RuntimeException("关键帧类型不能为空");
            }

            // 验证ctype是否为支持的枚举值（复制自稳定版）
            java.util.Set<String> supportedCtypes = new java.util.HashSet<>();
            supportedCtypes.add("KFTypePositionX");
            supportedCtypes.add("KFTypePositionY");
            supportedCtypes.add("KFTypeRotation");
            supportedCtypes.add("KFTypeScaleX");
            supportedCtypes.add("KFTypeAlpha");

            if (!supportedCtypes.contains(ctype)) {
                throw new RuntimeException("不支持的关键帧类型: " + ctype + "，支持的类型: KFTypePositionX, KFTypePositionY, KFTypeRotation, KFTypeScaleX, KFTypeAlpha");
            }

            if (offsets.isEmpty()) {
                throw new RuntimeException("偏移量不能为空");
            }
            if (segmentInfos.isEmpty()) {
                throw new RuntimeException("段信息不能为空");
            }
            if (values.isEmpty()) {
                throw new RuntimeException("关键帧值不能为空");
            }

            // Pro版验证：检查segment_infos中是否包含必要的尺寸信息
            if ("KFTypePositionX".equals(ctype)) {
                // 检查是否有任何segment_info缺少width信息
                boolean hasValidWidth = segmentInfos.stream().anyMatch(segmentInfo ->
                    segmentInfo.getInteger("width") != null && segmentInfo.getInteger("width") > 0);
                if (!hasValidWidth && request.getWidth() == null) {
                    throw new RuntimeException("KFTypePositionX需要在segment_infos中提供width信息或使用全局width参数");
                }
            }
            if ("KFTypePositionY".equals(ctype)) {
                // 检查是否有任何segment_info缺少height信息
                boolean hasValidHeight = segmentInfos.stream().anyMatch(segmentInfo ->
                    segmentInfo.getInteger("height") != null && segmentInfo.getInteger("height") > 0);
                if (!hasValidHeight && request.getHeight() == null) {
                    throw new RuntimeException("KFTypePositionY需要在segment_infos中提供height信息或使用全局height参数");
                }
            }

            // 解析值（复制自稳定版逻辑）
            String[] valueArray = values.split("\\|");
            java.util.List<Double> valueList = new java.util.ArrayList<>();
            for (String value : valueArray) {
                try {
                    valueList.add(Double.parseDouble(value.trim()));
                } catch (NumberFormatException e) {
                    log.warn("Pro版跳过无效的值: {}", value);
                }
            }

            log.info("Pro版关键帧参数 - 类型: {}, 偏移量: {}, 段数量: {}, 值数量: {}",
                    ctype, offsets, segmentInfos.size(), valueList.size());

            // 解析偏移量（复制自稳定版逻辑）
            String[] offsetArray = offsets.split("\\|");
            java.util.List<Double> offsetPercentages = new java.util.ArrayList<>();
            for (String offsetStr : offsetArray) {
                try {
                    double offsetValue = Double.parseDouble(offsetStr.trim());
                    if (offsetValue < 0 || offsetValue > 100) {
                        log.warn("Pro版偏移量超出范围[0-100]: {}", offsetValue);
                        continue;
                    }
                    offsetPercentages.add(offsetValue);
                } catch (NumberFormatException e) {
                    log.warn("Pro版跳过无效的偏移量值: {}", offsetStr);
                }
            }

            if (offsetPercentages.isEmpty()) {
                throw new RuntimeException("无法解析偏移量: " + offsets);
            }

            // 验证数量一致性（复制自稳定版）
            if (offsetPercentages.size() != valueList.size()) {
                throw new RuntimeException("偏移量和值的数量必须一致，当前偏移量数量: " + offsetPercentages.size() + "，值数量: " + valueList.size());
            }

            // 生成关键帧数据（复制自稳定版的完整逻辑）
            com.alibaba.fastjson.JSONArray keyframes = new com.alibaba.fastjson.JSONArray();
            int invalidOffsetValueCount = 0;

            int segmentIndex = 0;
            for (JSONObject segmentInfo : segmentInfos) {
                segmentIndex++;
                String segmentId = segmentInfo.getString("segment_id");
                log.info("Pro版处理第{}个segment_info: segment_id={}", segmentIndex, segmentId);

                if (segmentId == null || segmentId.trim().isEmpty()) {
                    log.warn("Pro版跳过无效的段信息（缺少segment_id）: {}", segmentInfo);
                    continue;
                }

                // 获取段的时间范围（复制自稳定版逻辑）
                long segmentStart = 0L;
                long segmentEnd = 5000000L; // 默认5秒

                if (segmentInfo.containsKey("start") && segmentInfo.containsKey("end")) {
                    segmentStart = segmentInfo.getLong("start");
                    segmentEnd = segmentInfo.getLong("end");
                } else if (segmentInfo.containsKey("target_timerange")) {
                    JSONObject timerange = segmentInfo.getJSONObject("target_timerange");
                    if (timerange != null) {
                        segmentStart = timerange.getLong("start");
                        segmentEnd = segmentStart + timerange.getLong("duration");
                    }
                }

                long segmentDuration = segmentEnd - segmentStart;

                // 为每个偏移量和值生成关键帧（复制自稳定版逻辑）
                log.info("Pro版为segment {} 生成关键帧，offset数量: {}, value数量: {}",
                        segmentId, offsetPercentages.size(), valueList.size());

                for (int i = 0; i < Math.min(offsetPercentages.size(), valueList.size()); i++) {
                    try {
                        double offsetPercentage = offsetPercentages.get(i);
                        double rawValue = valueList.get(i);
                        log.debug("Pro版处理segment {} 的第{}个关键帧: offset={}%, value={}",
                                 segmentId, i+1, offsetPercentage, rawValue);

                        // 计算相对偏移量（微秒）- 剪映要求关键帧时间偏移相对于片段开始时间
                        long calculatedOffset = (long)(segmentDuration * offsetPercentage / 100.0);
                        log.debug("Pro版计算关键帧偏移: segment_start={}, duration={}, offset_percent={}%, relative_offset={}",
                                 segmentStart, segmentDuration, offsetPercentage, calculatedOffset);

                        // 值处理（完全复制自稳定版的完整逻辑和验证）
                        double calculatedValue;

                        // 根据不同的ctype使用不同的计算逻辑和验证
                        if ("KFTypeAlpha".equals(ctype)) {
                            // 透明度：验证范围0-1，直接使用原始值
                            if (rawValue < 0 || rawValue > 1) {
                                throw new RuntimeException("KFTypeAlpha的值必须在0-1范围内，当前值: " + rawValue);
                            }
                            calculatedValue = rawValue;
                        } else if ("KFTypeRotation".equals(ctype)) {
                            // 旋转：验证角度范围0-360度，直接使用原始值
                            if (rawValue < 0 || rawValue > 360) {
                                throw new RuntimeException("KFTypeRotation的值必须在0-360范围内，当前值: " + rawValue);
                            }
                            calculatedValue = rawValue;
                        } else if ("KFTypeScaleX".equals(ctype)) {
                            // 均匀缩放：验证范围0.01-5.0，直接使用原始值
                            if (rawValue < 0.01 || rawValue > 5.0) {
                                throw new RuntimeException("KFTypeScaleX的值必须在0.01-5.0范围内，当前值: " + rawValue);
                            }
                            calculatedValue = rawValue;
                        } else if ("KFTypePositionX".equals(ctype)) {
                            // X位置：使用Canvas宽度归一化（修复坐标显示问题）
                            log.debug("片段 {} X坐标归一化使用Canvas宽度: {}", segmentId, canvasWidth);
                            calculatedValue = rawValue / canvasWidth;
                        } else if ("KFTypePositionY".equals(ctype)) {
                            // Y位置：使用Canvas高度归一化（修复坐标显示问题）
                            log.debug("片段 {} Y坐标归一化使用Canvas高度: {}", segmentId, canvasHeight);
                            calculatedValue = rawValue / canvasHeight;
                        } else {
                            // 默认：使用Canvas宽度归一化（修复坐标显示问题）
                            log.debug("片段 {} 默认坐标归一化使用Canvas宽度: {}", segmentId, canvasWidth);
                            calculatedValue = rawValue / canvasWidth;
                        }

                        // 格式化value，避免科学计数法并去除尾随零（保持与稳定版一致）
                        java.math.BigDecimal formattedValue = new java.math.BigDecimal(calculatedValue);
                        formattedValue = formattedValue.stripTrailingZeros();

                        // 创建关键帧对象（复制自稳定版逻辑）
                        JSONObject keyframe = new JSONObject();
                        keyframe.put("offset", calculatedOffset);
                        keyframe.put("property", ctype);
                        keyframe.put("segment_id", segmentId);
                        keyframe.put("value", formattedValue);

                        keyframes.add(keyframe);
                        log.info("Pro版成功生成关键帧: segment_id={}, offset={}, value={}",
                                segmentId, calculatedOffset, formattedValue);

                    } catch (Exception e) {
                        invalidOffsetValueCount++;
                        log.warn("Pro版跳过无效的offset/value: {} / {}", offsetPercentages.get(i), valueList.get(i));
                    }
                }
            }

            if (keyframes.isEmpty()) {
                throw new RuntimeException("未生成任何有效的关键帧数据");
            }

            String result = keyframes.toJSONString();
            log.info("Pro版关键帧数据生成完成 - 生成了{}个关键帧，跳过了{}个无效值",
                    keyframes.size(), invalidOffsetValueCount);
            log.debug("Pro版生成的关键帧数据: {}", result);

            return result;

        } catch (Exception e) {
            log.error("Pro版生成关键帧信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成关键帧信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * Pro版内部添加关键帧（复制自稳定版add_keyframes的完整处理逻辑）
     * 实现真正的代码隔离，不依赖稳定版Service
     */
    private JSONObject addKeyframesInternal(String draftUrl, String keyframesJson, JianyingProAddKeyframesRequest request) {
        try {
            log.info("Pro版开始添加关键帧到草稿，关键帧数据: {}", keyframesJson);

            // 第1步：下载并解析草稿文件（复制自稳定版逻辑）
            JSONObject draft = downloadAndParseDraft(draftUrl);
            if (draft == null) {
                return createErrorResult("草稿文件下载失败", "PRO_DRAFT_DOWNLOAD_ERROR");
            }

            // 第2步：解析关键帧数据（复制自稳定版逻辑）
            com.alibaba.fastjson.JSONArray keyframes;
            try {
                keyframes = com.alibaba.fastjson.JSONArray.parseArray(keyframesJson);
                if (keyframes == null || keyframes.isEmpty()) {
                    return createErrorResult("关键帧数据解析失败或为空", "PRO_KEYFRAMES_PARSE_ERROR");
                }
            } catch (Exception e) {
                log.error("Pro版解析关键帧数据失败: {}", e.getMessage());
                return createErrorResult("关键帧数据JSON格式错误: " + e.getMessage(), "PRO_KEYFRAMES_FORMAT_ERROR");
            }

            // 第3步：添加关键帧到对应的segment（复制自稳定版逻辑）
            log.info("Pro版开始添加关键帧到草稿，总共生成了 {} 个关键帧", keyframes.size());
            int addedCount = 0;
            for (int i = 0; i < keyframes.size(); i++) {
                JSONObject keyframe = keyframes.getJSONObject(i);
                String segmentId = keyframe.getString("segment_id");
                String property = keyframe.getString("property");
                Long offset = keyframe.getLong("offset");
                Object value = keyframe.get("value");

                log.info("Pro版尝试添加第{}个关键帧: segment_id={}, property={}, offset={}, value={}",
                        i+1, segmentId, property, offset, value);

                if (segmentId == null || property == null || offset == null || value == null) {
                    log.warn("Pro版跳过无效的关键帧数据: {}", keyframe);
                    continue;
                }

                // 查找对应的segment并添加关键帧
                boolean segmentFound = addKeyframeToSegmentInternal(draft, segmentId, property, offset, value);
                if (segmentFound) {
                    addedCount++;
                    log.info("Pro版成功添加关键帧到segment {}: {}={} at {}", segmentId, property, value, offset);
                } else {
                    log.warn("Pro版未找到segment ID: {}", segmentId);
                }
            }

            // 第4步：保存草稿文件（复制自稳定版逻辑）
            boolean saveSuccess = saveDraftFile(draftUrl, draft);
            if (!saveSuccess) {
                return createErrorResult("草稿文件保存失败", "PRO_DRAFT_SAVE_ERROR");
            }

            log.info("Pro版关键帧添加完成，共添加 {} 个关键帧，跳过 {} 个无效帧",
                    addedCount, keyframes.size() - addedCount);

            // 第5步：返回处理结果（包含导入指南信息）
            JSONObject result = new JSONObject();
            result.put("draft_url", draftUrl); // 返回原始URL，与稳定版完全一致

            // 添加导入指南信息（符合Coze插件定义）
            String importGuideUrl = "https://www.aigcview.com/JianYingDraft?draft=" + draftUrl;
            result.put("message", "不知道导入的请看：" + importGuideUrl);

            return result;

        } catch (Exception e) {
            log.error("Pro版添加关键帧失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", "Pro版添加关键帧失败: " + e.getMessage());
            errorResult.put("error_code", "PRO_ADD_KEYFRAMES_ERROR");
            return errorResult;
        }
    }

    /**
     * 添加关键帧到指定的segment（复制自稳定版逻辑）
     */
    private boolean addKeyframeToSegmentInternal(JSONObject draft, String segmentId, String property, Long offset, Object value) {
        try {
            // 查找tracks数组
            com.alibaba.fastjson.JSONArray tracks = draft.getJSONArray("tracks");
            if (tracks == null) {
                log.warn("Pro版草稿文件中没有tracks数组");
                return false;
            }

            // 遍历所有轨道
            for (int i = 0; i < tracks.size(); i++) {
                JSONObject track = tracks.getJSONObject(i);
                com.alibaba.fastjson.JSONArray segments = track.getJSONArray("segments");
                if (segments == null) continue;

                // 遍历轨道中的所有segment
                for (int j = 0; j < segments.size(); j++) {
                    JSONObject segment = segments.getJSONObject(j);
                    String currentSegmentId = segment.getString("id");

                    if (segmentId.equals(currentSegmentId)) {
                        // 找到目标segment，添加关键帧
                        addKeyframeToSegmentClipInternal(segment, property, offset, value);
                        log.info("Pro版成功添加关键帧到segment {}: {}={} at {}", segmentId, property, value, offset);
                        return true;
                    }
                }
            }

            log.warn("Pro版未找到segment ID: {}", segmentId);
            return false;

        } catch (Exception e) {
            log.error("Pro版添加关键帧到segment失败", e);
            return false;
        }
    }

    /**
     * 添加关键帧到segment的common_keyframes中（完全复制自稳定版逻辑）
     */
    private void addKeyframeToSegmentClipInternal(JSONObject segment, String property, Long offset, Object value) {
        try {
            // 支持的属性类型：KFTypePositionX, KFTypePositionY, KFTypeRotation, UNIFORM_SCALE, KFTypeAlpha, KFTypeScaleX（均匀缩放）
            if ("KFTypePositionX".equals(property) ||
                "KFTypePositionY".equals(property) ||
                "KFTypeRotation".equals(property) ||
                "KFTypeAlpha".equals(property) ||
                "KFTypeScaleX".equals(property)) {

                // 直接添加关键帧，使用正确的property_type
                addKeyframeToPropertyInternal(segment, property, offset, value);

            } else if ("UNIFORM_SCALE".equals(property)) {
                // UNIFORM_SCALE映射为KFTypeScaleX实现均匀缩放（兼容性处理）
                log.info("Pro版将UNIFORM_SCALE映射为KFTypeScaleX以确保剪映兼容性");
                addKeyframeToPropertyInternal(segment, "KFTypeScaleX", offset, value);

            } else {
                log.warn("Pro版不支持的关键帧属性类型: {}", property);
            }

        } catch (Exception e) {
            log.error("Pro版添加关键帧到segment失败", e);
            throw new RuntimeException("添加关键帧到segment失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加关键帧到指定属性 - 使用剪映标准格式（完全复制自稳定版逻辑）
     */
    private void addKeyframeToPropertyInternal(JSONObject segment, String propertyType, Long offset, Object value) {
        try {
            // 获取或创建common_keyframes数组
            com.alibaba.fastjson.JSONArray commonKeyframes = segment.getJSONArray("common_keyframes");
            if (commonKeyframes == null) {
                commonKeyframes = new com.alibaba.fastjson.JSONArray();
                segment.put("common_keyframes", commonKeyframes);
            }

            // 查找是否已存在相同property_type的关键帧组
            JSONObject existingKeyframeGroup = findExistingKeyframeGroupInternal(commonKeyframes, propertyType);

            if (existingKeyframeGroup != null) {
                // 添加到现有关键帧组的keyframe_list中
                addKeyframePointToGroupInternal(existingKeyframeGroup, offset, value);
                log.debug("Pro版添加关键帧点到现有组: property_type={}, offset={}, value={}", propertyType, offset, value);
            } else {
                // 创建新的关键帧组
                JSONObject newKeyframeGroup = createKeyframeGroupInternal(propertyType, offset, value);
                commonKeyframes.add(newKeyframeGroup);
                log.debug("Pro版创建新关键帧组: property_type={}, offset={}, value={}", propertyType, offset, value);
            }

        } catch (Exception e) {
            log.error("Pro版添加关键帧到属性失败: propertyType={}", propertyType, e);
            throw new RuntimeException("添加关键帧到属性失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查找现有的关键帧组（完全复制自稳定版逻辑）
     */
    private JSONObject findExistingKeyframeGroupInternal(com.alibaba.fastjson.JSONArray commonKeyframes, String propertyType) {
        for (int i = 0; i < commonKeyframes.size(); i++) {
            JSONObject keyframeGroup = commonKeyframes.getJSONObject(i);
            String existingPropertyType = keyframeGroup.getString("property_type");
            if (propertyType.equals(existingPropertyType)) {
                return keyframeGroup;
            }
        }
        return null;
    }

    /**
     * 创建新的关键帧组（完全复制自稳定版逻辑）
     */
    private JSONObject createKeyframeGroupInternal(String propertyType, Long offset, Object value) {
        JSONObject keyframeGroup = new JSONObject();
        keyframeGroup.put("id", java.util.UUID.randomUUID().toString());
        keyframeGroup.put("material_id", "");
        keyframeGroup.put("property_type", propertyType);

        // 创建keyframe_list数组
        com.alibaba.fastjson.JSONArray keyframeList = new com.alibaba.fastjson.JSONArray();
        JSONObject keyframePoint = createKeyframePointInternal(offset, value);
        keyframeList.add(keyframePoint);

        keyframeGroup.put("keyframe_list", keyframeList);
        return keyframeGroup;
    }

    /**
     * 创建单个关键帧点（完全复制自稳定版逻辑）
     */
    private JSONObject createKeyframePointInternal(Long offset, Object value) {
        JSONObject keyframePoint = new JSONObject();
        keyframePoint.put("id", java.util.UUID.randomUUID().toString());
        keyframePoint.put("curveType", "Line");
        keyframePoint.put("graphID", "");

        // 创建控制点
        JSONObject leftControl = new JSONObject();
        leftControl.put("x", 0);
        leftControl.put("y", 0);
        keyframePoint.put("left_control", leftControl);

        JSONObject rightControl = new JSONObject();
        rightControl.put("x", 0);
        rightControl.put("y", 0);
        keyframePoint.put("right_control", rightControl);

        keyframePoint.put("time_offset", offset);

        // 创建values数组
        com.alibaba.fastjson.JSONArray values = new com.alibaba.fastjson.JSONArray();
        values.add(value);
        keyframePoint.put("values", values);

        return keyframePoint;
    }

    /**
     * 添加关键帧点到现有关键帧组（完全复制自稳定版逻辑）
     */
    private void addKeyframePointToGroupInternal(JSONObject keyframeGroup, Long offset, Object value) {
        com.alibaba.fastjson.JSONArray keyframeList = keyframeGroup.getJSONArray("keyframe_list");
        if (keyframeList == null) {
            keyframeList = new com.alibaba.fastjson.JSONArray();
            keyframeGroup.put("keyframe_list", keyframeList);
        }

        // 创建新的关键帧点
        JSONObject keyframePoint = createKeyframePointInternal(offset, value);

        // 插入到正确位置（按time_offset排序）
        insertKeyframePointSortedInternal(keyframeList, keyframePoint);
    }

    /**
     * 按时间偏移排序插入关键帧点（完全复制自稳定版逻辑）
     */
    private void insertKeyframePointSortedInternal(com.alibaba.fastjson.JSONArray keyframeList, JSONObject newKeyframePoint) {
        Long newOffset = newKeyframePoint.getLong("time_offset");

        // 找到正确的插入位置
        int insertIndex = keyframeList.size();
        for (int i = 0; i < keyframeList.size(); i++) {
            JSONObject existingPoint = keyframeList.getJSONObject(i);
            Long existingOffset = existingPoint.getLong("time_offset");
            if (newOffset < existingOffset) {
                insertIndex = i;
                break;
            }
        }

        // 在正确位置插入
        if (insertIndex >= keyframeList.size()) {
            keyframeList.add(newKeyframePoint);
        } else {
            // 手动实现插入逻辑
            com.alibaba.fastjson.JSONArray newList = new com.alibaba.fastjson.JSONArray();
            for (int i = 0; i < insertIndex; i++) {
                newList.add(keyframeList.get(i));
            }
            newList.add(newKeyframePoint);
            for (int i = insertIndex; i < keyframeList.size(); i++) {
                newList.add(keyframeList.get(i));
            }

            // 替换原数组内容
            keyframeList.clear();
            keyframeList.addAll(newList);
        }

        log.debug("Pro版关键帧点已按时间排序插入: offset={}, 插入位置={}", newOffset, insertIndex);
    }

    // ========== 修复：添加富文本内容生成逻辑（完全复制稳定版） ==========

    /**
     * 生成富文本内容（专注keywords数组的多关键词高亮）
     * 移除单个keyword参数，专注于keywords数组处理
     */
    private String generateRichTextContentWithKeywords(String text, java.util.List<String> keywords, String keywordColor, Integer fontSize, Integer keywordFontSize, String textColor, String borderColor, JSONObject fontInfo) {
        try {
            // 处理keywords数组
            java.util.List<String> effectiveKeywords = new java.util.ArrayList<>();
            if (keywords != null && !keywords.isEmpty()) {
                // 使用keywords数组
                for (String kw : keywords) {
                    if (kw != null && !kw.trim().isEmpty() && text.contains(kw.trim())) {
                        effectiveKeywords.add(kw.trim());
                    }
                }
                log.info("Pro版使用keywords数组，有效关键词数量: {}", effectiveKeywords.size());
            }

            if (effectiveKeywords.isEmpty()) {
                // 没有关键词，使用默认样式
                return generateDefaultTextStyle(text, fontSize, textColor, borderColor, fontInfo);
            }

            // 处理多关键词高亮
            return generateMultiKeywordTextStyle(text, effectiveKeywords, keywordColor, fontSize, keywordFontSize, textColor, borderColor, fontInfo);

        } catch (Exception e) {
            log.error("Pro版生成多关键词富文本内容失败", e);
            // 返回简单格式作为备用
            JSONObject simpleText = new JSONObject();
            simpleText.put("text", text);
            return simpleText.toJSONString();
        }
    }

    /**
     * 生成默认文本样式（无关键词高亮）
     */
    private String generateDefaultTextStyle(String text, Integer fontSize, String textColor, String borderColor, JSONObject fontInfo) {
        try {
            JSONObject richText = new JSONObject();
            com.alibaba.fastjson.JSONArray styles = new com.alibaba.fastjson.JSONArray();

            // 默认样式
            JSONObject defaultStyle = new JSONObject();
            JSONObject defaultFill = new JSONObject();
            JSONObject defaultContent = new JSONObject();
            JSONObject defaultSolid = new JSONObject();

            // 使用传入的文字颜色
            double[] textColorArray = parseColor(textColor);
            defaultSolid.put("color", textColorArray);
            defaultContent.put("solid", defaultSolid);
            defaultFill.put("content", defaultContent);
            defaultStyle.put("fill", defaultFill);
            defaultStyle.put("range", new int[]{0, text.length()});
            defaultStyle.put("size", fontSize != null ? fontSize : 15);

            JSONObject defaultFont = new JSONObject();
            defaultFont.put("id", fontInfo.getString("id"));
            defaultFont.put("path", fontInfo.getString("path"));
            defaultStyle.put("font", defaultFont);

            // 为默认样式添加边框描边效果
            if (borderColor != null && !borderColor.trim().isEmpty()) {
                com.alibaba.fastjson.JSONArray strokes = new com.alibaba.fastjson.JSONArray();
                JSONObject stroke = new JSONObject();
                stroke.put("alpha", 1);
                JSONObject strokeContent = new JSONObject();
                strokeContent.put("render_type", "solid");
                JSONObject strokeSolid = new JSONObject();
                strokeSolid.put("alpha", 1);
                double[] borderColorArray = parseColor(borderColor);
                strokeSolid.put("color", borderColorArray);
                strokeContent.put("solid", strokeSolid);
                stroke.put("content", strokeContent);
                stroke.put("width", 0.08);
                strokes.add(stroke);
                defaultStyle.put("strokes", strokes);
            }

            styles.add(defaultStyle);
            richText.put("styles", styles);
            richText.put("text", text);

            return richText.toJSONString();

        } catch (Exception e) {
            log.error("生成默认文本样式失败", e);
            JSONObject simpleText = new JSONObject();
            simpleText.put("text", text);
            return simpleText.toJSONString();
        }
    }

    /**
     * 生成多关键词文本样式
     */
    private String generateMultiKeywordTextStyle(String text, java.util.List<String> keywords, String keywordColor, Integer fontSize, Integer keywordFontSize, String textColor, String borderColor, JSONObject fontInfo) {
        try {
            // 计算所有关键词的位置
            java.util.List<KeywordRange> keywordRanges = new java.util.ArrayList<>();
            for (String keyword : keywords) {
                int start = text.indexOf(keyword);
                while (start != -1) {
                    int end = start + keyword.length();
                    keywordRanges.add(new KeywordRange(start, end, keyword));
                    start = text.indexOf(keyword, end);
                }
            }

            // 按位置排序
            keywordRanges.sort((a, b) -> Integer.compare(a.start, b.start));

            // 合并重叠的关键词范围
            java.util.List<KeywordRange> mergedRanges = mergeOverlappingRanges(keywordRanges);

            // 生成样式
            return generateStylesFromRanges(text, mergedRanges, keywordColor, fontSize, keywordFontSize, textColor, borderColor, fontInfo);

        } catch (Exception e) {
            log.error("生成多关键词文本样式失败", e);
            // 降级到默认样式处理
            return generateDefaultTextStyle(text, fontSize, textColor, borderColor, fontInfo);
        }
    }

    /**
     * 合并重叠的关键词范围
     */
    private java.util.List<KeywordRange> mergeOverlappingRanges(java.util.List<KeywordRange> ranges) {
        if (ranges.isEmpty()) return ranges;

        java.util.List<KeywordRange> merged = new java.util.ArrayList<>();
        KeywordRange current = ranges.get(0);

        for (int i = 1; i < ranges.size(); i++) {
            KeywordRange next = ranges.get(i);
            if (current.end >= next.start) {
                // 重叠，合并
                current = new KeywordRange(current.start, Math.max(current.end, next.end), current.keyword + "," + next.keyword);
            } else {
                // 不重叠，添加当前范围
                merged.add(current);
                current = next;
            }
        }
        merged.add(current);
        return merged;
    }

    /**
     * 从关键词范围生成样式
     */
    private String generateStylesFromRanges(String text, java.util.List<KeywordRange> ranges, String keywordColor, Integer fontSize, Integer keywordFontSize, String textColor, String borderColor, JSONObject fontInfo) {
        try {
            JSONObject richText = new JSONObject();
            com.alibaba.fastjson.JSONArray styles = new com.alibaba.fastjson.JSONArray();

            int currentPos = 0;

            for (KeywordRange range : ranges) {
                // 关键词前的普通文本
                if (currentPos < range.start) {
                    JSONObject beforeStyle = createTextStyle(currentPos, range.start, fontSize, textColor, borderColor, fontInfo, false);
                    styles.add(beforeStyle);
                }

                // 关键词文本
                JSONObject keywordStyle = createTextStyle(range.start, range.end, keywordFontSize, keywordColor, borderColor, fontInfo, true);
                styles.add(keywordStyle);

                currentPos = range.end;
            }

            // 最后的普通文本
            if (currentPos < text.length()) {
                JSONObject afterStyle = createTextStyle(currentPos, text.length(), fontSize, textColor, borderColor, fontInfo, false);
                styles.add(afterStyle);
            }

            richText.put("styles", styles);
            richText.put("text", text);

            return richText.toJSONString();

        } catch (Exception e) {
            log.error("从关键词范围生成样式失败", e);
            JSONObject simpleText = new JSONObject();
            simpleText.put("text", text);
            return simpleText.toJSONString();
        }
    }

    /**
     * 创建文本样式对象
     */
    private JSONObject createTextStyle(int start, int end, Integer fontSize, String color, String borderColor, JSONObject fontInfo, boolean isKeyword) {
        JSONObject style = new JSONObject();
        JSONObject fill = new JSONObject();
        JSONObject content = new JSONObject();
        JSONObject solid = new JSONObject();

        // 解析颜色
        double[] colorArray = parseColor(color);
        solid.put("color", colorArray);
        content.put("solid", solid);
        fill.put("content", content);
        style.put("fill", fill);
        style.put("range", new int[]{start, end});
        style.put("size", fontSize != null ? fontSize : 15);

        JSONObject font = new JSONObject();
        font.put("id", fontInfo.getString("id"));
        font.put("path", fontInfo.getString("path"));
        style.put("font", font);

        if (isKeyword) {
            style.put("useLetterColor", true);
        }

        // 添加边框描边效果
        if (borderColor != null && !borderColor.trim().isEmpty()) {
            com.alibaba.fastjson.JSONArray strokes = new com.alibaba.fastjson.JSONArray();
            JSONObject stroke = new JSONObject();
            stroke.put("alpha", 1);
            JSONObject strokeContent = new JSONObject();
            strokeContent.put("render_type", "solid");
            JSONObject strokeSolid = new JSONObject();
            strokeSolid.put("alpha", 1);
            double[] borderColorArray = parseColor(borderColor);
            strokeSolid.put("color", borderColorArray);
            strokeContent.put("solid", strokeSolid);
            stroke.put("content", strokeContent);
            stroke.put("width", 0.08);
            strokes.add(stroke);
            style.put("strokes", strokes);
        }

        return style;
    }

    /**
     * 关键词范围类
     */
    private static class KeywordRange {
        int start;
        int end;
        String keyword;

        KeywordRange(int start, int end, String keyword) {
            this.start = start;
            this.end = end;
            this.keyword = keyword;
        }
    }

    /**
     * 生成富文本内容（处理关键词高亮和边框描边效果）
     * 完全复制自稳定版JianyingAssistantService.generateRichTextContent方法
     * 注意：此方法已废弃，仅保留作为参考，实际使用generateRichTextContentWithKeywords
     */
    @Deprecated
    private String generateRichTextContent(String text, String keyword, String keywordColor, Integer fontSize, Integer keywordFontSize, String textColor, String borderColor, JSONObject fontInfo) {
        try {
            JSONObject richText = new JSONObject();
            com.alibaba.fastjson.JSONArray styles = new com.alibaba.fastjson.JSONArray();

            if (keyword != null && !keyword.trim().isEmpty() && text.contains(keyword)) {
                // 有关键词高亮的情况
                int keywordStart = text.indexOf(keyword);
                int keywordEnd = keywordStart + keyword.length();

                // 关键词前的文本样式
                if (keywordStart > 0) {
                    JSONObject beforeStyle = new JSONObject();
                    JSONObject beforeFill = new JSONObject();
                    JSONObject beforeContent = new JSONObject();
                    JSONObject beforeSolid = new JSONObject();
                    // 使用传入的文字颜色
                    double[] textColorArray = parseColor(textColor);
                    beforeSolid.put("color", textColorArray);
                    beforeContent.put("solid", beforeSolid);
                    beforeFill.put("content", beforeContent);
                    beforeStyle.put("fill", beforeFill);
                    beforeStyle.put("range", new int[]{0, keywordStart});
                    beforeStyle.put("size", fontSize != null ? fontSize : 15);
                    JSONObject beforeFont = new JSONObject();
                    beforeFont.put("id", fontInfo.getString("id"));
                    beforeFont.put("path", fontInfo.getString("path"));
                    beforeStyle.put("font", beforeFont);

                    // 添加边框描边效果
                    if (borderColor != null && !borderColor.trim().isEmpty()) {
                        com.alibaba.fastjson.JSONArray strokes = new com.alibaba.fastjson.JSONArray();
                        JSONObject stroke = new JSONObject();
                        stroke.put("alpha", 1);
                        JSONObject strokeContent = new JSONObject();
                        strokeContent.put("render_type", "solid");
                        JSONObject strokeSolid = new JSONObject();
                        strokeSolid.put("alpha", 1);
                        double[] borderColorArray = parseColor(borderColor);
                        strokeSolid.put("color", borderColorArray);
                        strokeContent.put("solid", strokeSolid);
                        stroke.put("content", strokeContent);
                        stroke.put("width", 0.08);
                        strokes.add(stroke);
                        beforeStyle.put("strokes", strokes);
                    }

                    styles.add(beforeStyle);
                }

                // 关键词样式
                JSONObject keywordStyle = new JSONObject();
                JSONObject keywordFill = new JSONObject();
                JSONObject keywordContent = new JSONObject();
                JSONObject keywordSolid = new JSONObject();

                // 解析关键词颜色
                double[] keywordColorArray = parseColor(keywordColor);
                keywordSolid.put("color", keywordColorArray);
                keywordContent.put("solid", keywordSolid);
                keywordFill.put("content", keywordContent);
                keywordStyle.put("fill", keywordFill);
                keywordStyle.put("range", new int[]{keywordStart, keywordEnd});
                keywordStyle.put("size", keywordFontSize != null ? keywordFontSize : 15);
                JSONObject keywordFont = new JSONObject();
                keywordFont.put("id", fontInfo.getString("id"));
                keywordFont.put("path", fontInfo.getString("path"));
                keywordStyle.put("font", keywordFont);
                keywordStyle.put("useLetterColor", true);

                // 为关键词添加边框描边效果
                if (borderColor != null && !borderColor.trim().isEmpty()) {
                    com.alibaba.fastjson.JSONArray strokes = new com.alibaba.fastjson.JSONArray();
                    JSONObject stroke = new JSONObject();
                    stroke.put("alpha", 1);
                    JSONObject strokeContent = new JSONObject();
                    strokeContent.put("render_type", "solid");
                    JSONObject strokeSolid = new JSONObject();
                    strokeSolid.put("alpha", 1);
                    double[] borderColorArray = parseColor(borderColor);
                    strokeSolid.put("color", borderColorArray);
                    strokeContent.put("solid", strokeSolid);
                    stroke.put("content", strokeContent);
                    stroke.put("width", 0.08);
                    strokes.add(stroke);
                    keywordStyle.put("strokes", strokes);
                }

                styles.add(keywordStyle);

                // 关键词后的文本样式
                if (keywordEnd < text.length()) {
                    JSONObject afterStyle = new JSONObject();
                    JSONObject afterFill = new JSONObject();
                    JSONObject afterContent = new JSONObject();
                    JSONObject afterSolid = new JSONObject();
                    // 使用传入的文字颜色
                    double[] textColorArray = parseColor(textColor);
                    afterSolid.put("color", textColorArray);
                    afterContent.put("solid", afterSolid);
                    afterFill.put("content", afterContent);
                    afterStyle.put("fill", afterFill);
                    afterStyle.put("range", new int[]{keywordEnd, text.length()});
                    afterStyle.put("size", fontSize != null ? fontSize : 15);
                    JSONObject afterFont = new JSONObject();
                    afterFont.put("id", fontInfo.getString("id"));
                    afterFont.put("path", fontInfo.getString("path"));
                    afterStyle.put("font", afterFont);

                    // 为后续文本添加边框描边效果
                    if (borderColor != null && !borderColor.trim().isEmpty()) {
                        com.alibaba.fastjson.JSONArray strokes = new com.alibaba.fastjson.JSONArray();
                        JSONObject stroke = new JSONObject();
                        stroke.put("alpha", 1);
                        JSONObject strokeContent = new JSONObject();
                        strokeContent.put("render_type", "solid");
                        JSONObject strokeSolid = new JSONObject();
                        strokeSolid.put("alpha", 1);
                        double[] borderColorArray = parseColor(borderColor);
                        strokeSolid.put("color", borderColorArray);
                        strokeContent.put("solid", strokeSolid);
                        stroke.put("content", strokeContent);
                        stroke.put("width", 0.08);
                        strokes.add(stroke);
                        afterStyle.put("strokes", strokes);
                    }

                    styles.add(afterStyle);
                }
            } else {
                // 没有关键词高亮的情况
                JSONObject defaultStyle = new JSONObject();
                JSONObject defaultFill = new JSONObject();
                JSONObject defaultContent = new JSONObject();
                JSONObject defaultSolid = new JSONObject();
                // 使用传入的文字颜色
                double[] textColorArray = parseColor(textColor);
                defaultSolid.put("color", textColorArray);
                defaultContent.put("solid", defaultSolid);
                defaultFill.put("content", defaultContent);
                defaultStyle.put("fill", defaultFill);
                defaultStyle.put("range", new int[]{0, text.length()});
                defaultStyle.put("size", fontSize != null ? fontSize : 15);
                JSONObject defaultFont = new JSONObject();
                defaultFont.put("id", fontInfo.getString("id"));
                defaultFont.put("path", fontInfo.getString("path"));
                defaultStyle.put("font", defaultFont);

                // 为默认样式添加边框描边效果
                if (borderColor != null && !borderColor.trim().isEmpty()) {
                    com.alibaba.fastjson.JSONArray strokes = new com.alibaba.fastjson.JSONArray();
                    JSONObject stroke = new JSONObject();
                    stroke.put("alpha", 1);
                    JSONObject strokeContent = new JSONObject();
                    strokeContent.put("render_type", "solid");
                    JSONObject strokeSolid = new JSONObject();
                    strokeSolid.put("alpha", 1);
                    double[] borderColorArray = parseColor(borderColor);
                    strokeSolid.put("color", borderColorArray);
                    strokeContent.put("solid", strokeSolid);
                    stroke.put("content", strokeContent);
                    stroke.put("width", 0.08);
                    strokes.add(stroke);
                    defaultStyle.put("strokes", strokes);
                }

                styles.add(defaultStyle);
            }

            richText.put("styles", styles);
            richText.put("text", text);

            return richText.toJSONString();

        } catch (Exception e) {
            log.error("生成富文本内容失败", e);
            // 返回简单格式作为备用
            JSONObject simpleText = new JSONObject();
            simpleText.put("text", text);
            return simpleText.toJSONString();
        }
    }

    /**
     * 解析颜色字符串为RGB数组
     * 完全复制自稳定版JianyingAssistantService.parseColor方法
     */
    private double[] parseColor(String colorStr) {
        if (colorStr == null || colorStr.trim().isEmpty()) {
            return new double[]{1, 0, 0}; // 默认红色
        }

        try {
            if ("red".equalsIgnoreCase(colorStr)) {
                return new double[]{1, 0, 0};
            } else if ("green".equalsIgnoreCase(colorStr)) {
                return new double[]{0, 1, 0};
            } else if ("blue".equalsIgnoreCase(colorStr)) {
                return new double[]{0, 0, 1};
            } else if ("yellow".equalsIgnoreCase(colorStr)) {
                return new double[]{1, 1, 0};
            } else if ("white".equalsIgnoreCase(colorStr)) {
                return new double[]{1, 1, 1};
            } else if ("black".equalsIgnoreCase(colorStr)) {
                return new double[]{0, 0, 0};
            } else if (colorStr.startsWith("#")) {
                // 解析十六进制颜色
                String hex = colorStr.substring(1);
                if (hex.length() == 6) {
                    int r = Integer.parseInt(hex.substring(0, 2), 16);
                    int g = Integer.parseInt(hex.substring(2, 4), 16);
                    int b = Integer.parseInt(hex.substring(4, 6), 16);
                    return new double[]{r / 255.0, g / 255.0, b / 255.0};
                } else if (hex.length() == 3) {
                    int r = Integer.parseInt(hex.substring(0, 1), 16) * 17;
                    int g = Integer.parseInt(hex.substring(1, 2), 16) * 17;
                    int b = Integer.parseInt(hex.substring(2, 3), 16) * 17;
                    return new double[]{r / 255.0, g / 255.0, b / 255.0};
                }
            }
        } catch (Exception e) {
            log.warn("解析颜色失败: {}", colorStr, e);
        }

        // 默认返回红色
        return new double[]{1, 0, 0};
    }
}
