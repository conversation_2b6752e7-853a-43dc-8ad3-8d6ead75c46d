/**
 * Duration计算器 - 专为electron-app设计
 * 统一处理所有草稿的总时长计算，确保以最长轨道为准 + 2秒缓冲
 */
class DurationCalculator {
    /**
     * 计算草稿的总时长（基于所有轨道的最长结束时间）
     * @param {Object} draftContent - 草稿内容JSON
     * @returns {number} 总时长（微秒）
     */
    static calculateDuration(draftContent) {
        let maxEndTime = 0;

        if (!draftContent.tracks || !Array.isArray(draftContent.tracks)) {
            console.warn('[DurationCalculator] Draft has no valid tracks, using 0 duration');
            return 0;
        }
        
        console.log(`[DurationCalculator] Processing ${draftContent.tracks.length} tracks`);
        
        // 遍历所有轨道（video、audio、text、sticker等）
        for (let i = 0; i < draftContent.tracks.length; i++) {
            const track = draftContent.tracks[i];
            const trackType = track.type || 'unknown';
            
            if (!track.segments || !Array.isArray(track.segments)) {
                console.log(`[DurationCalculator] Track ${i} (${trackType}) has no segments, skipping`);
                continue;
            }
            
            let trackMaxEndTime = 0;
            
            // 遍历轨道中的所有片段
            for (let j = 0; j < track.segments.length; j++) {
                const segment = track.segments[j];
                
                if (!segment.target_timerange) {
                    console.warn(`[DurationCalculator] Track ${i} segment ${j} missing target_timerange, skipping`);
                    continue;
                }
                
                const start = segment.target_timerange.start || 0;
                const duration = segment.target_timerange.duration || 0;
                const endTime = start + duration;
                
                trackMaxEndTime = Math.max(trackMaxEndTime, endTime);
                
                console.log(`[DurationCalculator] Track ${i} (${trackType}) segment ${j}: start=${start}, duration=${duration}, end=${endTime}`);
            }
            
            maxEndTime = Math.max(maxEndTime, trackMaxEndTime);
            console.log(`[DurationCalculator] Track ${i} (${trackType}) max end time: ${trackMaxEndTime}`);
        }
        
        // 直接返回计算出的精确时长
        console.log(`[DurationCalculator] Calculated duration: ${maxEndTime} microseconds (${maxEndTime / 1000000} seconds)`);
        return maxEndTime;
    }
    
    /**
     * 修正草稿的duration字段
     * @param {Object} draftContent - 草稿内容JSON
     * @returns {Object} 修正后的草稿内容
     */
    static fixDraftDuration(draftContent) {
        const calculatedDuration = this.calculateDuration(draftContent);
        const originalDuration = draftContent.duration || 0;
        
        if (calculatedDuration !== originalDuration) {
            console.log(`[DurationCalculator] Duration correction: ${originalDuration} -> ${calculatedDuration} (difference: ${calculatedDuration - originalDuration} microseconds, ${(calculatedDuration - originalDuration) / 1000000} seconds)`);

            // 创建修正后的草稿内容
            const fixedContent = { ...draftContent, duration: calculatedDuration };
            return fixedContent;
        } else {
            console.log(`[DurationCalculator] Duration already correct: ${originalDuration}`);
            return draftContent;
        }
    }
}

module.exports = DurationCalculator;
