import Vue from 'vue'
import router from './router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import notification from 'ant-design-vue/es/notification'
import { ACCESS_TOKEN,INDEX_MAIN_PAGE_PATH, OAUTH2_LOGIN_PAGE_PATH } from '@/store/mutation-types'
import { generateIndexRouter, isOAuth2AppEnv } from '@/utils/util'
import { isAdmin, getUserRole, clearRoleInfo } from '@/utils/roleUtils'
import { handleServerConnectionError } from '@/utils/simpleErrorHandler'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

// ✅ 白名单：无需登录即可访问的页面
const whiteList = ['/user/login', '/user/register', '/user/register-result', '/user/alteration']

// ✅ 官网页面：使用简洁路径，所有用户都可以访问（不包括个人中心和分销推广）
const websitePages = ['/home', '/market', '/cases', '/tutorials', '/signin',
                     '/membership', '/login', '/not-found',
                     '/carousel-test', '/route-test', '/server-error', '/error-test',
                     '/JianYingDraft']

// ✅ 需要登录的官网页面
const authRequiredPages = ['/usercenter', '/affiliate']

// 将官网页面和OAuth2页面加入白名单
whiteList.push(...websitePages)
whiteList.push(OAUTH2_LOGIN_PAGE_PATH)

/**
 * 判断是否为官网页面（支持动态路由）
 * @param {string} path 路径
 * @returns {boolean} 是否为官网页面
 */
function isWebsitePage(path) {
  // 官网页面的路径模式
  const websitePatterns = [
    '/market/plugin/',  // 插件详情页
    '/cases/',          // 客户案例详情页
    '/tutorials/',      // 教程详情页
  ]

  return websitePatterns.some(pattern => path.startsWith(pattern))
}

/**
 * 加载动态路由（如果尚未加载）
 */
async function loadDynamicRoutesIfNeeded() {
  console.log('  🔧 开始检查动态路由加载状态')
  console.log('  📋 当前权限列表长度:', store.getters.permissionList.length)

  if (store.getters.permissionList.length === 0) {
    console.log('  📡 权限列表为空，开始获取权限数据')
    const res = await store.dispatch('GetPermissionList')
    console.log('  📡 权限接口响应:', res)

    const menuData = res.result.menu
    console.log('  📋 菜单数据:', menuData)

    if (menuData === null || menuData === "" || menuData === undefined || menuData.length === 0) {
      console.error('  ❌ 菜单数据为空，无权限访问后台')
      throw new Error('无菜单权限')
    }

    console.log('  🏗️ 开始生成动态路由')
    const constRoutes = generateIndexRouter(menuData)
    console.log('  🏗️ 生成的动态路由:', constRoutes)

    console.log('  📦 更新应用路由状态')
    await store.dispatch('UpdateAppRouter', { constRoutes })

    console.log('  ➕ 添加动态路由到路由器')
    console.log('  ➕ 要添加的路由:', store.getters.addRouters)
    router.addRoutes(store.getters.addRouters)

    console.log('  ✅ 动态路由加载完成')
  } else {
    console.log('  ✅ 动态路由已存在，跳过加载')
  }
}

router.beforeEach(async (to, from, next) => {
  NProgress.start()

  // ✅ 添加调试信息
  console.log('🔍 路由守卫调试信息:')
  console.log('  📍 目标路径 (to):', to.path, to.fullPath)
  console.log('  📍 来源路径 (from):', from.path, from.fullPath)
  console.log('  🔑 是否有TOKEN:', !!Vue.ls.get(ACCESS_TOKEN))

  if (Vue.ls.get(ACCESS_TOKEN)) {
    /* 已登录用户 */

    // 确保角色信息已加载
    const userRole = await getUserRole()
    const isAdminUser = await isAdmin()
    console.log('  👤 用户角色:', userRole)
    console.log('  🔐 是否管理员:', isAdminUser)

    if (to.path === '/user/login' || to.path === OAUTH2_LOGIN_PAGE_PATH) {
      // 已登录用户访问登录页，根据角色重定向
      console.log('  ➡️ 已登录用户访问登录页，准备重定向')
      if (isAdminUser) {
        console.log('  🎯 管理员重定向到:', INDEX_MAIN_PAGE_PATH)
        next({ path: INDEX_MAIN_PAGE_PATH })
      } else {
        console.log('  🎯 普通用户重定向到: /home')
        next({ path: '/home' })
      }
      NProgress.done()
    } else if (to.path === '/') {
      // 根路径访问，根据角色决定去向
      console.log('  ➡️ 访问根路径，根据角色决定去向')
      if (isAdminUser) {
        // 管理员用户，检查并加载动态路由后进入后台
        console.log('  🔧 管理员用户，开始加载动态路由')
        try {
          await loadDynamicRoutesIfNeeded()
          console.log('  ✅ 动态路由加载成功，跳转到:', INDEX_MAIN_PAGE_PATH)
          console.log('  📋 当前路由表:', router.getRoutes ? router.getRoutes().map(r => r.path) : '无法获取')
          next({ path: INDEX_MAIN_PAGE_PATH })
        } catch (error) {
          console.error('  ❌ 加载动态路由失败:', error)
          console.log('  🔍 错误详情:', {
            message: error.message,
            code: error.code,
            response: error.response
          })

          // 检查是否是服务器连接错误
          const isServerError = handleServerConnectionError(error)
          console.log('  🔍 是否为服务器连接错误:', isServerError)

          if (isServerError) {
            console.log('  🚨 检测到服务器连接错误，跳转到异常页面')
            NProgress.done()
            return // handleServerConnectionError 会处理跳转
          }
          next({ path: '/404' })
        }
      } else {
        // 普通用户，重定向到官网首页
        console.log('  🎯 普通用户重定向到: /home')
        next({ path: '/home' })
      }
      NProgress.done()
    } else if (authRequiredPages.indexOf(to.path) !== -1) {
      // 需要登录的官网页面（如个人中心）
      console.log('  🔐 访问需要登录的官网页面，已登录，允许访问')
      next()
      NProgress.done()
    } else if (websitePages.indexOf(to.path) !== -1 || isWebsitePage(to.path)) {
      // 官网页面，所有用户都可以访问
      console.log('  🌐 访问官网页面，直接通过')
      next()
      NProgress.done()
    } else {
      // 后台页面，需要管理员角色
      console.log('  🏢 访问后台页面，检查管理员权限')
      if (isAdminUser) {
        // 管理员角色，检查菜单权限并加载动态路由
        console.log('  🔧 管理员访问后台，加载动态路由')
        try {
          await loadDynamicRoutesIfNeeded()
          console.log('  ✅ 动态路由加载成功，允许访问:', to.path)
          next()
        } catch (error) {
          console.error('  ❌ 加载动态路由失败:', error)
          console.log('  🔍 错误详情:', {
            message: error.message,
            code: error.code,
            response: error.response
          })
s
          // 检查是否是认证错误（401）
          if (error.response && error.response.status === 401) {
            console.log('  🔐 检测到认证失败，清除用户数据并重定向到登录页')
            Vue.ls.remove(ACCESS_TOKEN)
            store.dispatch('Logout').then(() => {
              next({ path: '/login' })
            })
            NProgress.done()
            return
          }

          // 检查是否是服务器连接错误
          const isServerError = handleServerConnectionError(error)
          console.log('  🔍 是否为服务器连接错误:', isServerError)

          if (isServerError) {
            console.log('  🚨 检测到服务器连接错误，跳转到异常页面')
            NProgress.done()
            return // handleServerConnectionError 会处理跳转
          }
          next({ path: '/404' })
        }
      } else {
        // 非管理员角色，重定向到官网首页
        console.log('  🚫 非管理员访问后台，重定向到: /home')
        next({ path: '/home' })
      }
      NProgress.done()
    }
  } else {
    /* 未登录用户 */
    console.log('  🚫 未登录用户')
    if (to.path === '/') {
      // 未登录用户访问根路径，重定向到官网首页
      console.log('  🎯 未登录访问根路径，重定向到: /home')
      next({ path: '/home' })
      NProgress.done()
    } else if (authRequiredPages.indexOf(to.path) !== -1) {
      // 需要登录的官网页面（如个人中心），未登录用户重定向到官网登录页
      console.log('  🔐 未登录访问个人中心，重定向到官网登录页')
      next({ path: '/login', query: { redirect: to.fullPath } })
      NProgress.done()
    } else if (whiteList.indexOf(to.path) !== -1 || isWebsitePage(to.path)) {
      // 白名单页面或官网页面，直接访问
      console.log('  ✅ 白名单/官网页面，直接访问')
      if (to.path === '/user/login' && isOAuth2AppEnv()) {
        next({ path: OAUTH2_LOGIN_PAGE_PATH })
      } else {
        next()
      }
      NProgress.done()
    } else {
      // 其他页面需要登录
      console.log('  🔐 需要登录，重定向到登录页')
      let path = isOAuth2AppEnv() ? OAUTH2_LOGIN_PAGE_PATH : '/user/login'
      next({ path: path, query: { redirect: to.fullPath } })
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
