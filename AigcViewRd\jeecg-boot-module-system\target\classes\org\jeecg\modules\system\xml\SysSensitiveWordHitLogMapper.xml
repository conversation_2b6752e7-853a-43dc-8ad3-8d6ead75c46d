<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.system.mapper.SysSensitiveWordHitLogMapper">

    <!-- 获取命中日志统计 -->
    <select id="getHitStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_hits,
            COUNT(DISTINCT word) as unique_words,
            COUNT(DISTINCT user_id) as unique_users,
            COUNT(DISTINCT ip_address) as unique_ips,
            DATE(hit_time) as hit_date,
            COUNT(*) as daily_hits
        FROM sys_sensitive_word_hit_log 
        WHERE hit_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(hit_time)
        ORDER BY hit_date DESC
    </select>

    <!-- 获取热门命中词汇 -->
    <select id="getTopHitWords" resultType="java.util.Map">
        SELECT 
            word,
            COUNT(*) as hit_count,
            COUNT(DISTINCT user_id) as unique_users,
            MAX(hit_time) as last_hit_time
        FROM sys_sensitive_word_hit_log 
        WHERE hit_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY word
        ORDER BY hit_count DESC
        LIMIT #{limit}
    </select>

    <!-- 获取用户命中统计 -->
    <select id="getUserHitStatistics" resultType="java.util.Map">
        SELECT 
            user_id,
            COUNT(*) as hit_count,
            COUNT(DISTINCT word) as unique_words,
            MAX(hit_time) as last_hit_time
        FROM sys_sensitive_word_hit_log 
        WHERE hit_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY user_id
        ORDER BY hit_count DESC
        LIMIT #{limit}
    </select>

    <!-- 按时间段统计命中情况 -->
    <select id="getHitTrendByHour" resultType="java.util.Map">
        SELECT 
            HOUR(hit_time) as hour,
            COUNT(*) as hit_count
        FROM sys_sensitive_word_hit_log 
        WHERE hit_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY HOUR(hit_time)
        ORDER BY hour
    </select>

    <!-- 按模块统计命中情况 -->
    <select id="getHitByModule" resultType="java.util.Map">
        SELECT
            module,
            COUNT(*) as hit_count,
            COUNT(DISTINCT word) as unique_words,
            COUNT(DISTINCT user_id) as unique_users
        FROM sys_sensitive_word_hit_log
        WHERE hit_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY module
        ORDER BY hit_count DESC
    </select>

    <!-- 获取敏感词命中趋势（按天统计） -->
    <select id="getHitTrend" resultType="java.util.Map">
        SELECT
            DATE(hit_time) as hit_date,
            COUNT(*) as hit_count,
            COUNT(DISTINCT word) as unique_words,
            COUNT(DISTINCT user_id) as unique_users
        FROM sys_sensitive_word_hit_log
        WHERE hit_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        GROUP BY DATE(hit_time)
        ORDER BY hit_date ASC
    </select>

</mapper>
