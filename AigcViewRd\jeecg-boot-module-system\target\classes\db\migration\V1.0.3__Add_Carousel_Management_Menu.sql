-- =====================================================
-- 轮播图管理菜单配置SQL脚本
-- 创建时间: 2025-06-24
-- 描述: 为后台管理系统添加轮播图管理功能的完整菜单配置
-- =====================================================

-- 1. 添加一级菜单：网站管理
INSERT INTO `sys_permission` (
    `id`, 
    `parent_id`, 
    `name`, 
    `url`, 
    `component`, 
    `component_name`, 
    `redirect`, 
    `menu_type`, 
    `perms`, 
    `perms_type`, 
    `sort_no`, 
    `always_show`, 
    `icon`, 
    `is_route`, 
    `is_leaf`, 
    `keep_alive`, 
    `hidden`, 
    `hide_tab`, 
    `description`, 
    `status`, 
    `del_flag`, 
    `rule_flag`, 
    `create_by`, 
    `create_time`, 
    `update_by`, 
    `update_time`, 
    `internal_or_external`
) VALUES (
    'website_management_menu_id',         -- 菜单ID
    '',                                   -- 父菜单ID（空表示一级菜单）
    '网站管理',                           -- 菜单名称
    '/website-management',                -- 路由路径
    'layouts/RouteView',                  -- 组件路径
    '',                                   -- 组件名称
    '/website-management/carousel',       -- 重定向路径
    0,                                    -- 菜单类型（0-目录）
    '',                                   -- 权限标识
    '1',                                  -- 权限类型
    0.05,                                 -- 排序号（在插件商城之前）
    0,                                    -- 是否总是显示
    'global',                             -- 图标
    1,                                    -- 是否路由菜单
    0,                                    -- 是否叶子节点
    0,                                    -- 是否缓存
    0,                                    -- 是否隐藏
    0,                                    -- 是否隐藏Tab
    '网站内容管理模块',                   -- 描述
    '1',                                  -- 状态
    0,                                    -- 删除标志
    0,                                    -- 规则标志
    'admin',                              -- 创建人
    NOW(),                                -- 创建时间
    'admin',                              -- 更新人
    NOW(),                                -- 更新时间
    0                                     -- 内部或外部
);

-- 2. 添加二级菜单：轮播图管理
INSERT INTO `sys_permission` (
    `id`, 
    `parent_id`, 
    `name`, 
    `url`, 
    `component`, 
    `component_name`, 
    `redirect`, 
    `menu_type`, 
    `perms`, 
    `perms_type`, 
    `sort_no`, 
    `always_show`, 
    `icon`, 
    `is_route`, 
    `is_leaf`, 
    `keep_alive`, 
    `hidden`, 
    `hide_tab`, 
    `description`, 
    `status`, 
    `del_flag`, 
    `rule_flag`, 
    `create_by`, 
    `create_time`, 
    `update_by`, 
    `update_time`, 
    `internal_or_external`
) VALUES (
    'carousel_management_menu_id',        -- 菜单ID
    'website_management_menu_id',         -- 父菜单ID
    '轮播图管理',                         -- 菜单名称
    '/website-management/carousel',       -- 路由路径
    'aigcview/vue/AigcHomeCarouselList',  -- 组件路径
    'AigcHomeCarouselList',               -- 组件名称
    '',                                   -- 重定向路径
    1,                                    -- 菜单类型（1-菜单）
    'carousel:list',                      -- 权限标识
    '1',                                  -- 权限类型
    1.0,                                  -- 排序号
    0,                                    -- 是否总是显示
    'picture',                            -- 图标
    1,                                    -- 是否路由菜单
    1,                                    -- 是否叶子节点
    1,                                    -- 是否缓存
    0,                                    -- 是否隐藏
    0,                                    -- 是否隐藏Tab
    '首页轮播图管理页面',                 -- 描述
    '1',                                  -- 状态
    0,                                    -- 删除标志
    0,                                    -- 规则标志
    'admin',                              -- 创建人
    NOW(),                                -- 创建时间
    'admin',                              -- 更新人
    NOW(),                                -- 更新时间
    0                                     -- 内部或外部
);

-- 3. 添加按钮权限：添加
INSERT INTO `sys_permission` (
    `id`, 
    `parent_id`, 
    `name`, 
    `url`, 
    `component`, 
    `component_name`, 
    `redirect`, 
    `menu_type`, 
    `perms`, 
    `perms_type`, 
    `sort_no`, 
    `always_show`, 
    `icon`, 
    `is_route`, 
    `is_leaf`, 
    `keep_alive`, 
    `hidden`, 
    `hide_tab`, 
    `description`, 
    `status`, 
    `del_flag`, 
    `rule_flag`, 
    `create_by`, 
    `create_time`, 
    `update_by`, 
    `update_time`, 
    `internal_or_external`
) VALUES (
    'carousel_add_btn_id',                -- 按钮ID
    'carousel_management_menu_id',        -- 父菜单ID
    '添加',                               -- 按钮名称
    '',                                   -- 路由路径
    '',                                   -- 组件路径
    '',                                   -- 组件名称
    '',                                   -- 重定向路径
    2,                                    -- 菜单类型（2-按钮）
    'carousel:add',                       -- 权限标识
    '1',                                  -- 权限类型
    1.0,                                  -- 排序号
    0,                                    -- 是否总是显示
    '',                                   -- 图标
    0,                                    -- 是否路由菜单
    1,                                    -- 是否叶子节点
    0,                                    -- 是否缓存
    0,                                    -- 是否隐藏
    0,                                    -- 是否隐藏Tab
    '添加轮播图',                         -- 描述
    '1',                                  -- 状态
    0,                                    -- 删除标志
    0,                                    -- 规则标志
    'admin',                              -- 创建人
    NOW(),                                -- 创建时间
    'admin',                              -- 更新人
    NOW(),                                -- 更新时间
    0                                     -- 内部或外部
);

-- 4. 添加按钮权限：编辑
INSERT INTO `sys_permission` (
    `id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, 
    `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`
) VALUES (
    'carousel_edit_btn_id', 'carousel_management_menu_id', '编辑', '', '', '', '', 
    2, 'carousel:edit', '1', 2.0, 0, '', 
    0, 1, 0, 0, 0, '编辑轮播图', 
    '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 5. 添加按钮权限：删除
INSERT INTO `sys_permission` (
    `id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, 
    `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`
) VALUES (
    'carousel_delete_btn_id', 'carousel_management_menu_id', '删除', '', '', '', '', 
    2, 'carousel:delete', '1', 3.0, 0, '', 
    0, 1, 0, 0, 0, '删除轮播图', 
    '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 6. 添加按钮权限：批量删除
INSERT INTO `sys_permission` (
    `id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, 
    `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`
) VALUES (
    'carousel_batch_delete_btn_id', 'carousel_management_menu_id', '批量删除', '', '', '', '', 
    2, 'carousel:deleteBatch', '1', 4.0, 0, '', 
    0, 1, 0, 0, 0, '批量删除轮播图', 
    '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 7. 添加按钮权限：导入
INSERT INTO `sys_permission` (
    `id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, 
    `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`
) VALUES (
    'carousel_import_btn_id', 'carousel_management_menu_id', '导入', '', '', '', '', 
    2, 'carousel:import', '1', 5.0, 0, '', 
    0, 1, 0, 0, 0, '导入轮播图', 
    '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 8. 添加按钮权限：导出
INSERT INTO `sys_permission` (
    `id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, 
    `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`
) VALUES (
    'carousel_export_btn_id', 'carousel_management_menu_id', '导出', '', '', '', '', 
    2, 'carousel:export', '1', 6.0, 0, '', 
    0, 1, 0, 0, 0, '导出轮播图', 
    '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 9. 添加按钮权限：查看详情
INSERT INTO `sys_permission` (
    `id`, `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, 
    `status`, `del_flag`, `rule_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `internal_or_external`
) VALUES (
    'carousel_detail_btn_id', 'carousel_management_menu_id', '详情', '', '', '', '', 
    2, 'carousel:detail', '1', 7.0, 0, '', 
    0, 1, 0, 0, 0, '查看轮播图详情', 
    '1', 0, 0, 'admin', NOW(), 'admin', NOW(), 0
);

-- 10. 为admin角色分配轮播图管理权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`) 
SELECT 'f6817f48af4fb3af11b9e8bf182f618b', `id` 
FROM `sys_permission` 
WHERE `id` IN (
    'website_management_menu_id',
    'carousel_management_menu_id',
    'carousel_add_btn_id',
    'carousel_edit_btn_id',
    'carousel_delete_btn_id',
    'carousel_batch_delete_btn_id',
    'carousel_import_btn_id',
    'carousel_export_btn_id',
    'carousel_detail_btn_id'
);

-- =====================================================
-- 脚本执行完成
-- 说明：
-- 1. 创建了"网站管理"一级菜单
-- 2. 在其下创建了"轮播图管理"二级菜单
-- 3. 添加了完整的按钮权限（增删改查、导入导出等）
-- 4. 为admin角色分配了所有相关权限
-- 5. 管理员登录后即可在后台看到轮播图管理菜单
-- =====================================================
