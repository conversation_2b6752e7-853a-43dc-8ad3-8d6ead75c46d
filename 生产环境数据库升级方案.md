# 🚀 智界Aigc生产环境数据库升级方案

## 📋 升级概述

**升级目标**: 为生产环境安全添加佣金配置功能相关的数据库字段和表结构  
**影响范围**: 主要涉及 `aicg_user_profile` 表的字段扩展，不影响现有数据  
**升级时间**: 预计5-10分钟  
**风险等级**: 🟢 低风险（仅添加字段，不修改现有数据）

---

## ⚠️ 升级前准备工作

### 1. 数据库备份（必须执行）

```bash
# 1. 完整数据库备份
mysqldump -u root -p --single-transaction --routines --triggers AigcView > backup_before_commission_upgrade_$(date +%Y%m%d_%H%M%S).sql

# 2. 仅备份将要修改的表
mysqldump -u root -p --single-transaction AigcView aicg_user_profile aicg_user_referral aicg_user_referral_reward > backup_commission_tables_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 应用服务停止（推荐）

```bash
# 停止应用服务，避免升级过程中的数据冲突
sudo systemctl stop aigcview
# 或者根据你的部署方式停止相应服务
```

### 3. 数据库连接测试

```sql
-- 测试数据库连接
SELECT 'Database connection OK' as status;

-- 检查当前表结构
DESC aicg_user_profile;
```

---

## 🔧 安全升级脚本

### 阶段一：检查现有字段（防重复执行）

```sql
-- =====================================================
-- 智界Aigc佣金功能数据库升级脚本 - 生产环境版
-- 版本: v1.0-PROD
-- 创建时间: 2025-07-26
-- 说明: 安全添加佣金相关字段，不影响现有数据
-- =====================================================

-- 检查字段是否已存在的函数
SET @sql = '';

-- 1. 检查并添加 commission_level 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND COLUMN_NAME = 'commission_level';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE aicg_user_profile ADD COLUMN commission_level TINYINT(1) DEFAULT 1 COMMENT "佣金等级：1-新手，2-高级，3-顶级";',
    'SELECT "commission_level字段已存在，跳过" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 检查并添加 valid_invite_count 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND COLUMN_NAME = 'valid_invite_count';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE aicg_user_profile ADD COLUMN valid_invite_count INT DEFAULT 0 COMMENT "有效邀请数量";',
    'SELECT "valid_invite_count字段已存在，跳过" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 检查并添加 total_commission 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND COLUMN_NAME = 'total_commission';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE aicg_user_profile ADD COLUMN total_commission DECIMAL(10,2) DEFAULT 0.00 COMMENT "累计佣金";',
    'SELECT "total_commission字段已存在，跳过" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 检查并添加 available_commission 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND COLUMN_NAME = 'available_commission';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE aicg_user_profile ADD COLUMN available_commission DECIMAL(10,2) DEFAULT 0.00 COMMENT "可提现佣金";',
    'SELECT "available_commission字段已存在，跳过" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
```

### 阶段二：添加索引（性能优化）

```sql
-- 为佣金相关字段添加索引（如果不存在）
SET @sql = '';

-- 检查并添加 commission_level 索引
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND INDEX_NAME = 'idx_commission_level';

SET @sql = IF(@index_exists = 0, 
    'ALTER TABLE aicg_user_profile ADD INDEX idx_commission_level (commission_level);',
    'SELECT "idx_commission_level索引已存在，跳过" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
```

### 阶段三：数据初始化（安全更新）

```sql
-- 为现有用户安全初始化佣金等级（仅更新NULL值）
UPDATE aicg_user_profile 
SET commission_level = 1 
WHERE commission_level IS NULL;

-- 为现有用户安全初始化有效邀请数（仅更新NULL值）
UPDATE aicg_user_profile 
SET valid_invite_count = 0 
WHERE valid_invite_count IS NULL;

-- 为现有用户安全初始化累计佣金（仅更新NULL值）
UPDATE aicg_user_profile 
SET total_commission = 0.00 
WHERE total_commission IS NULL;

-- 为现有用户安全初始化可用佣金（仅更新NULL值）
UPDATE aicg_user_profile 
SET available_commission = 0.00 
WHERE available_commission IS NULL;
```

### 阶段四：验证升级结果

```sql
-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aicg_user_profile' 
AND COLUMN_NAME IN ('commission_level', 'valid_invite_count', 'total_commission', 'available_commission')
ORDER BY ORDINAL_POSITION;

-- 验证数据初始化情况
SELECT 
    COUNT(*) as total_users,
    COUNT(commission_level) as users_with_commission_level,
    COUNT(valid_invite_count) as users_with_invite_count,
    AVG(commission_level) as avg_commission_level
FROM aicg_user_profile;

-- 查看示例数据
SELECT 
    user_id,
    username,
    commission_level,
    valid_invite_count,
    total_commission,
    available_commission
FROM aicg_user_profile 
LIMIT 5;
```

---

## 🔄 升级执行步骤

### 1. 执行升级

```bash
# 方式一：直接执行SQL文件
mysql -u root -p AigcView < 生产环境佣金升级脚本.sql

# 方式二：逐步执行（推荐）
mysql -u root -p AigcView
# 然后复制粘贴上述SQL脚本分阶段执行
```

### 2. 启动应用服务

```bash
# 启动应用服务
sudo systemctl start aigcview

# 检查服务状态
sudo systemctl status aigcview

# 查看应用日志
tail -f /path/to/your/application.log
```

### 3. 功能验证

```bash
# 测试佣金配置API
curl -X GET "http://your-domain/jeecg-boot/sys/user/getCommissionConfig" \
     -H "X-Access-Token: your-token"

# 预期返回格式：
# {
#   "success": true,
#   "result": {
#     "memberType": "NORMAL",
#     "commissionLevel": 1,
#     "validInviteCount": 0,
#     "totalCommission": 0.00,
#     "availableCommission": 0.00
#   }
# }
```

---

## 🛡️ 回滚方案（紧急情况）

### 如果升级出现问题，可以快速回滚：

```sql
-- 方案一：删除新增字段（谨慎使用）
ALTER TABLE aicg_user_profile DROP COLUMN commission_level;
ALTER TABLE aicg_user_profile DROP COLUMN valid_invite_count;
ALTER TABLE aicg_user_profile DROP COLUMN total_commission;
ALTER TABLE aicg_user_profile DROP COLUMN available_commission;

-- 方案二：从备份恢复（推荐）
# 停止应用
sudo systemctl stop aigcview

# 恢复数据库
mysql -u root -p AigcView < backup_before_commission_upgrade_YYYYMMDD_HHMMSS.sql

# 启动应用
sudo systemctl start aigcview
```

---

## ✅ 升级检查清单

- [ ] 1. 数据库完整备份已完成
- [ ] 2. 应用服务已停止
- [ ] 3. 数据库连接测试正常
- [ ] 4. 阶段一：字段添加完成
- [ ] 5. 阶段二：索引添加完成
- [ ] 6. 阶段三：数据初始化完成
- [ ] 7. 阶段四：验证结果正常
- [ ] 8. 应用服务重启成功
- [ ] 9. API功能测试通过
- [ ] 10. 前端页面显示正常

---

## 📞 技术支持

**升级负责人**: 开发团队  
**紧急联系**: 如遇问题请立即联系技术负责人  
**升级时间**: 建议在业务低峰期（如凌晨2-4点）执行  

---

## 📝 升级记录

| 时间 | 操作人 | 操作内容 | 结果 | 备注 |
|------|--------|----------|------|------|
| YYYY-MM-DD HH:MM | 姓名 | 数据库备份 | 成功/失败 | 备份文件路径 |
| YYYY-MM-DD HH:MM | 姓名 | 字段添加 | 成功/失败 | 影响记录数 |
| YYYY-MM-DD HH:MM | 姓名 | 服务重启 | 成功/失败 | 启动时间 |
| YYYY-MM-DD HH:MM | 姓名 | 功能验证 | 成功/失败 | 测试结果 |

---

**重要提醒**: 
1. 本升级脚本设计为幂等性，可重复执行而不会造成数据损坏
2. 所有字段都有默认值，不会影响现有业务逻辑
3. 升级过程中如有任何异常，请立即停止并联系技术团队
4. 升级完成后请及时更新相关文档和配置