const DurationCalculator = require('./durationCalculator');

/**
 * 草稿处理助手 - 集成Duration修正功能
 */
class DraftHelper {
    /**
     * 处理草稿JSON字符串，自动修正duration
     * @param {string} jsonString - 草稿JSON字符串
     * @returns {string} 修正后的JSON字符串
     */
    static processDraftJsonString(jsonString) {
        try {
            console.log('[DraftHelper] Processing draft JSON string...');
            
            // 解析JSON
            const draftContent = JSON.parse(jsonString);
            
            // 修正duration
            const fixedContent = DurationCalculator.fixDraftDuration(draftContent);
            
            // 返回修正后的JSON字符串
            const fixedJsonString = JSON.stringify(fixedContent);
            
            console.log('[DraftHelper] Draft JSON processing complete');
            return fixedJsonString;
        } catch (error) {
            console.error('[DraftHelper] Failed to process draft JSON:', error);
            // 如果处理失败，返回原始字符串
            return jsonString;
        }
    }
    
    /**
     * 处理草稿对象，自动修正duration
     * @param {Object} draftContent - 草稿内容对象
     * @returns {Object} 修正后的草稿对象
     */
    static processDraftObject(draftContent) {
        try {
            console.log('[DraftHelper] Processing draft object...');
            
            // 修正duration
            const fixedContent = DurationCalculator.fixDraftDuration(draftContent);
            
            console.log('[DraftHelper] Draft object processing complete');
            return fixedContent;
        } catch (error) {
            console.error('[DraftHelper] Failed to process draft object:', error);
            // 如果处理失败，返回原始对象
            return draftContent;
        }
    }
}

module.exports = DraftHelper;
